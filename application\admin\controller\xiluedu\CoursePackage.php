<?php

namespace app\admin\controller\xiluedu;

use app\common\controller\Backend;
use app\common\model\xiluedu\Course;
use app\common\model\xiluedu\CoursePackage as CoursePackageModel;
use think\Db;
use think\Exception;

/**
 * 课程套餐管理
 */
class CoursePackage extends Backend
{
    protected $model = null;
    protected $noNeedLogin = [];
    protected $noNeedRight = [];

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new CoursePackageModel;
    }

    /**
     * 查看课程套餐列表
     */
    public function index()
    {
        $course_id = $this->request->param('course_id', 0);

        if (!$course_id) {
            $this->error('课程ID不能为空');
        }

        // 获取课程信息
        $course = Course::where('id', $course_id)->find();
        if (!$course) {
            $this->error('课程不存在');
        }

        $this->view->assign('course', $course);
        $this->view->assign('course_id', $course_id);

        if ($this->request->isAjax()) {
            list($where, $sort, $order, , $limit) = $this->buildparams();
            
       
            $list = $this->model
                ->where('course_id', $course_id)
                ->order($sort, $order)
                ->paginate($limit);

            $result = ['total' => $list->total(), 'rows' => $list->items()];
            return json($result);
        }

        return $this->view->fetch();
    }

    /**
     * 添加套餐
     */
    public function add()
    {
        $course_id = $this->request->param('course_id', 0);
        
        if (!$course_id) {
            $this->error('课程ID不能为空');
        }

        // 获取课程信息
        $course = Course::where('id', $course_id)->find();
        if (!$course) {
            $this->error('课程不存在');
        }

        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            if (!$params) {
                $this->error(__('Parameter %s can not be empty', ''));
            }

            $params['course_id'] = $course_id;
            $params['user_types'] = isset($params['user_types']) ? implode(',', $params['user_types']) : '';

            Db::startTrans();
            try {
                $result = $this->model->allowField(true)->save($params);
                if (!$result) {
                    throw new Exception('保存失败');
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success();
        }

        $this->view->assign('course', $course);
        $this->view->assign('course_id', $course_id);
        return $this->view->fetch();
    }

    /**
     * 编辑套餐
     */
    public function edit($ids = null)
    {
        $id = $ids ? $ids : $this->request->param('id');
        $row = $this->model->get($id);
        if (!$row) {
            $this->error(__('No Results were found'));
        }

        $course = Course::where('id', $row->course_id)->find();
        if (!$course) {
            $this->error('课程不存在');
        }

        if ($this->request->isPost()) {
            $params = $this->request->post('row/a');
            if (!$params) {
                $this->error(__('Parameter %s can not be empty', ''));
            }

            $params['user_types'] = isset($params['user_types']) ? implode(',', $params['user_types']) : '';

            Db::startTrans();
            try {
                $result = $row->allowField(true)->save($params);
                if (!$result) {
                    throw new Exception('保存失败');
                }
                Db::commit();
            } catch (Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            $this->success();
        }

        // 处理适用人员类型
        $rowData = $row->toArray();
        $rowData['user_types'] = $row->user_types ? explode(',', $row->user_types) : [];

        $this->view->assign('row', $rowData);
        $this->view->assign('course', $course);
        return $this->view->fetch();
    }

    /**
     * 删除套餐
     */
    public function del($ids = null)
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }

        $ids = $ids ? $ids : $this->request->post('ids');
        if (!$ids) {
            $this->error(__('Parameter %s can not be empty', 'ids'));
        }

        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }

        $list = $this->model->where($pk, 'in', $ids)->select();
        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                $count += $item->delete();
            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }

        if ($count) {
            $this->success();
        } else {
            $this->error(__('No rows were deleted'));
        }
    }

    /**
     * 批量更新状态
     */
    public function multi($ids = null)
    {
        if (!$this->request->isPost()) {
            $this->error(__('Invalid parameters'));
        }

        $ids = $ids ? $ids : $this->request->post('ids');
        $action = $this->request->post('action');

        if (!$ids || !$action) {
            $this->error(__('Parameter %s can not be empty', 'ids/action'));
        }

        $pk = $this->model->getPk();
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            $this->model->where($this->dataLimitField, 'in', $adminIds);
        }

        $list = $this->model->where($pk, 'in', $ids)->select();
        $count = 0;
        Db::startTrans();
        try {
            foreach ($list as $item) {
                switch ($action) {
                    case 'status':
                        $item->status = $item->status ? 0 : 1;
                        break;
                    default:
                        $this->error(__('Unknown action'));
                        break;
                }
                $count += $item->save();
            }
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }

        if ($count) {
            $this->success();
        } else {
            $this->error(__('No rows were updated'));
        }
    }
}
