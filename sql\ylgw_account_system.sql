-- 养老顾问对账系统数据库结构

-- 1. 创建养老顾问提现申请表
CREATE TABLE `fa_ylgw_withdraw` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` int(10) unsigned NOT NULL COMMENT '申请提现的养老顾问ID',
  `parent_id` int(10) unsigned NOT NULL COMMENT '审核人（上级）ID',
  `money` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '申请提现金额',
  `status` tinyint(1) NOT NULL DEFAULT '0' COMMENT '状态:0=待审核,1=已审核通过,2=已拒绝',
  `transfer_amount` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '实际转账金额',
  `transfer_image` varchar(500) DEFAULT '' COMMENT '转账截图',
  `remark` varchar(500) DEFAULT '' COMMENT '备注',
  `reject_reason` varchar(500) DEFAULT '' COMMENT '拒绝原因',
  `createtime` int(10) DEFAULT NULL COMMENT '申请时间',
  `updatetime` int(10) DEFAULT NULL COMMENT '更新时间',
  `audit_time` int(10) DEFAULT NULL COMMENT '审核时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `parent_id` (`parent_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='养老顾问提现申请表';

-- 2. 添加配置项到fa_config表
INSERT INTO `fa_config` (`name`, `group`, `title`, `tip`, `type`, `value`, `content`, `rule`, `extend`) 
VALUES ('ylgw_profit_rate', 'site', '养老顾问利润分佣比例', '设置养老顾问的利润分佣比例，单位为百分比', 'number', '10', '', 'required', '');

-- 3. 为用户表添加养老顾问相关统计字段（如果不存在）
ALTER TABLE `fa_user` ADD COLUMN `ylgw_total_commission` decimal(10,2) DEFAULT '0.00' COMMENT '养老顾问总佣金' AFTER `money`;
ALTER TABLE `fa_user` ADD COLUMN `ylgw_withdraw_amount` decimal(10,2) DEFAULT '0.00' COMMENT '养老顾问已提现金额' AFTER `ylgw_total_commission`;

-- 查看表结构
DESCRIBE fa_ylgw_withdraw;
