-- 检查分佣日志管理菜单是否已添加

-- 1. 检查是否已存在分佣管理菜单
SELECT 
    '=== 检查分佣管理菜单是否存在 ===' as info;

SELECT 
    id, 
    pid, 
    name, 
    title, 
    icon, 
    ismenu, 
    weigh, 
    status,
    CASE 
        WHEN pid = 0 THEN '主菜单'
        ELSE '子菜单'
    END as menu_type
FROM fa_auth_rule 
WHERE name LIKE 'commissionlog%' 
ORDER BY pid, weigh DESC;

-- 2. 检查超级管理员权限
SELECT 
    '=== 检查超级管理员权限 ===' as info;

SELECT 
    g.id,
    g.name,
    g.rules,
    CASE 
        WHEN g.rules LIKE '%commissionlog%' THEN '包含分佣管理权限'
        ELSE '不包含分佣管理权限'
    END as permission_status
FROM fa_auth_group g 
WHERE g.id = 1;

-- 3. 如果菜单不存在，显示添加建议
SELECT 
    '=== 添加建议 ===' as info;

SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM fa_auth_rule WHERE name = 'commissionlog') > 0 
        THEN '分佣管理菜单已存在，无需重复添加'
        ELSE '分佣管理菜单不存在，请执行 quick_add_commission_menu.sql 文件'
    END as suggestion;

-- 4. 显示菜单访问路径
SELECT 
    '=== 菜单访问路径 ===' as info;

SELECT 
    name as controller_path,
    title as menu_title,
    CONCAT('/admin/', REPLACE(name, '/', '/')) as access_url,
    CASE 
        WHEN ismenu = 1 THEN '在左侧菜单显示'
        ELSE '不在菜单显示（功能页面）'
    END as display_status
FROM fa_auth_rule 
WHERE name LIKE 'commissionlog%' 
ORDER BY weigh DESC;
