-- 修复用户角色bug的SQL脚本
-- 问题：城市负责人和养老院长购买套餐后被错误地设置为养老顾问

-- 1. 查看当前有问题的用户（同时拥有多个角色的用户）
SELECT 
    id,
    nickname,
    mobile,
    is_qydl as '城市负责人',
    is_sqdl as '养老院长', 
    is_ylgw as '养老顾问',
    ylgw_quota as '开通额度',
    createtime
FROM fa_user 
WHERE (is_qydl = 1 AND is_ylgw = 1) 
   OR (is_sqdl = 1 AND is_ylgw = 1)
ORDER BY createtime DESC;

-- 2. 备份当前用户状态（创建备份表）
CREATE TABLE IF NOT EXISTS `fa_user_role_backup` (
    `id` int(10) unsigned NOT NULL,
    `nickname` varchar(50) NOT NULL DEFAULT '',
    `mobile` varchar(11) NOT NULL DEFAULT '',
    `is_qydl` tinyint(1) NOT NULL DEFAULT '0',
    `is_sqdl` tinyint(1) NOT NULL DEFAULT '0',
    `is_ylgw` tinyint(1) NOT NULL DEFAULT '0',
    `ylgw_quota` int(10) NOT NULL DEFAULT '0',
    `backup_time` timestamp DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色修复备份表';

-- 插入备份数据
INSERT IGNORE INTO fa_user_role_backup (id, nickname, mobile, is_qydl, is_sqdl, is_ylgw, ylgw_quota)
SELECT id, nickname, mobile, is_qydl, is_sqdl, is_ylgw, ylgw_quota
FROM fa_user 
WHERE (is_qydl = 1 AND is_ylgw = 1) 
   OR (is_sqdl = 1 AND is_ylgw = 1);

-- 3. 修复城市负责人：移除养老顾问身份，保留开通额度
UPDATE fa_user 
SET is_ylgw = 0 
WHERE is_qydl = 1 AND is_ylgw = 1;

-- 4. 修复养老院长：移除养老顾问身份，保留开通额度
UPDATE fa_user 
SET is_ylgw = 0 
WHERE is_sqdl = 1 AND is_ylgw = 1;

-- 5. 查看修复后的结果
SELECT 
    '修复前有问题的用户数量' as description,
    COUNT(*) as count
FROM fa_user_role_backup

UNION ALL

SELECT 
    '修复后仍有问题的用户数量' as description,
    COUNT(*) as count
FROM fa_user 
WHERE (is_qydl = 1 AND is_ylgw = 1) 
   OR (is_sqdl = 1 AND is_ylgw = 1);

-- 6. 显示修复详情
SELECT 
    '用户类型' as user_type,
    COUNT(*) as count
FROM (
    SELECT 
        CASE 
            WHEN is_qydl = 1 AND is_sqdl = 0 AND is_ylgw = 0 THEN '城市负责人'
            WHEN is_qydl = 0 AND is_sqdl = 1 AND is_ylgw = 0 THEN '养老院长'
            WHEN is_qydl = 0 AND is_sqdl = 0 AND is_ylgw = 1 THEN '养老顾问'
            WHEN is_qydl = 0 AND is_sqdl = 0 AND is_ylgw = 0 THEN '普通用户'
            ELSE '多重角色(异常)'
        END as user_type
    FROM fa_user
    WHERE is_qydl = 1 OR is_sqdl = 1 OR is_ylgw = 1
) t
GROUP BY user_type;

-- 7. 检查开通额度分布
SELECT 
    CASE 
        WHEN is_qydl = 1 THEN '城市负责人'
        WHEN is_sqdl = 1 THEN '养老院长'
        WHEN is_ylgw = 1 THEN '养老顾问'
        ELSE '普通用户'
    END as user_type,
    COUNT(*) as user_count,
    SUM(ylgw_quota) as total_quota,
    AVG(ylgw_quota) as avg_quota,
    MAX(ylgw_quota) as max_quota
FROM fa_user
WHERE ylgw_quota > 0
GROUP BY 
    CASE 
        WHEN is_qydl = 1 THEN '城市负责人'
        WHEN is_sqdl = 1 THEN '养老院长'
        WHEN is_ylgw = 1 THEN '养老顾问'
        ELSE '普通用户'
    END
ORDER BY total_quota DESC;

-- 8. 查看最近的课程16订单，确认是否有异常
SELECT 
    co.id,
    co.order_no,
    co.user_id,
    u.nickname,
    u.mobile,
    CASE 
        WHEN u.is_qydl = 1 THEN '城市负责人'
        WHEN u.is_sqdl = 1 THEN '养老院长'
        WHEN u.is_ylgw = 1 THEN '养老顾问'
        ELSE '普通用户'
    END as current_role,
    co.package_quantity as quota_purchased,
    co.total_price,
    co.pay_status,
    FROM_UNIXTIME(co.createtime) as order_time
FROM fa_xiluedu_course_order co
LEFT JOIN fa_user u ON co.user_id = u.id
WHERE co.course_id = 16 
AND co.createtime > UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 7 DAY))
ORDER BY co.createtime DESC
LIMIT 20;

-- 9. 创建监控视图，用于后续检查
CREATE OR REPLACE VIEW v_user_role_check AS
SELECT 
    id,
    nickname,
    mobile,
    is_qydl,
    is_sqdl,
    is_ylgw,
    ylgw_quota,
    CASE 
        WHEN (is_qydl + is_sqdl + is_ylgw) > 1 THEN '多重角色(异常)'
        WHEN is_qydl = 1 THEN '城市负责人'
        WHEN is_sqdl = 1 THEN '养老院长'
        WHEN is_ylgw = 1 THEN '养老顾问'
        ELSE '普通用户'
    END as role_status,
    (is_qydl + is_sqdl + is_ylgw) as role_count,
    FROM_UNIXTIME(createtime) as create_time
FROM fa_user
WHERE is_qydl = 1 OR is_sqdl = 1 OR is_ylgw = 1;

-- 10. 最终验证
SELECT 
    '修复完成' as status,
    '请检查以下查询结果' as message;

-- 检查是否还有多重角色的用户
SELECT 
    '仍有多重角色的用户' as issue,
    COUNT(*) as count
FROM v_user_role_check 
WHERE role_count > 1;

-- 显示修复摘要
SELECT 
    role_status,
    COUNT(*) as user_count,
    SUM(ylgw_quota) as total_quota
FROM v_user_role_check
GROUP BY role_status
ORDER BY user_count DESC;
