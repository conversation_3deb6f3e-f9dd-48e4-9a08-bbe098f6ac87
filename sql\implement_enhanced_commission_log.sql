-- 实施增强版分佣日志系统
-- 执行此文件来完整部署分佣日志功能

-- 1. 检查并创建分佣日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `fa_commission_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_no` varchar(32) NOT NULL DEFAULT '' COMMENT '批次号（同一订单的所有分佣记录）',
  `step_no` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '步骤序号（同批次内的分佣步骤）',
  
  -- 订单信息
  `order_type` enum('course','offline_course','course_package','wanlshop','service') NOT NULL COMMENT '订单类型',
  `order_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
  `goods_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品/课程/服务ID',
  `goods_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品/课程/服务名称',
  `order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `payment_method` varchar(20) NOT NULL DEFAULT 'wechat' COMMENT '支付方式(wechat,alipay,balance,mixed)',
  `balance_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额支付金额',
  `online_payment_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '线上支付金额',
  `base_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣计算基数',
  
  -- 分佣角色信息
  `commission_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '获得分佣的用户ID',
  `commission_user_role` enum('platform','city_manager','nursing_home_director','elderly_advisor','teacher','service_provider') NOT NULL COMMENT '获得分佣的用户角色',
  `buyer_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '购买用户ID',
  `buyer_user_role` enum('platform','city_manager','nursing_home_director','elderly_advisor','regular_user') NOT NULL COMMENT '购买用户角色',
  
  -- 分佣计算信息
  `commission_type` enum('direct','indirect','recommendation','teacher','service') NOT NULL COMMENT '分佣类型',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '分佣比例（百分比）',
  `commission_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣金额',
  `platform_profit` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '平台利润',
  `total_distributed` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '总分佣金额',
  `calculation_rule` text COMMENT '计算规则说明',
  `calculation_details` json COMMENT '详细计算过程JSON',
  `config_source` enum('global','goods_level','special_rule') NOT NULL DEFAULT 'global' COMMENT '配置来源',
  `config_value` varchar(100) NOT NULL DEFAULT '' COMMENT '使用的配置值',
  
  -- 发放信息
  `is_distributed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否实际发放（0=未发放，1=已发放）',
  `distribution_type` enum('online','offline') NOT NULL DEFAULT 'online' COMMENT '发放方式',
  `distribution_status` enum('pending','success','failed') NOT NULL DEFAULT 'pending' COMMENT '发放状态',
  `distribution_memo` varchar(500) NOT NULL DEFAULT '' COMMENT '发放备注',
  `money_log_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联的资金变动记录ID',
  `before_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣前用户余额',
  `after_balance` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣后用户余额',
  
  -- 上级关系信息
  `parent_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上级用户ID',
  `parent_user_role` enum('platform','city_manager','nursing_home_director','elderly_advisor') NOT NULL DEFAULT 'platform' COMMENT '上级用户角色',
  `has_superior` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有上级（影响发放方式）',
  
  -- 时间字段
  `createtime` bigint(16) NOT NULL COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_order` (`order_type`, `order_id`),
  KEY `idx_commission_user` (`commission_user_id`),
  KEY `idx_buyer_user` (`buyer_user_id`),
  KEY `idx_createtime` (`createtime`),
  KEY `idx_commission_type` (`commission_type`),
  KEY `idx_distribution` (`is_distributed`, `distribution_type`),
  KEY `idx_user_role` (`commission_user_id`, `commission_user_role`),
  KEY `idx_payment_method` (`payment_method`),
  KEY `idx_balance_amount` (`balance_amount`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分佣日志表';

-- 2. 创建余额支付记录表
CREATE TABLE IF NOT EXISTS `fa_balance_payment_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_type` varchar(20) NOT NULL DEFAULT '' COMMENT '订单类型',
  `order_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
  `user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `balance_before` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付前余额',
  `balance_used` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '使用余额',
  `balance_after` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '支付后余额',
  `online_payment` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '线上支付金额',
  `total_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `payment_method` varchar(20) NOT NULL DEFAULT '' COMMENT '线上支付方式',
  `memo` varchar(500) NOT NULL DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order` (`order_type`, `order_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='余额支付记录表';

-- 3. 创建分佣统计视图
CREATE OR REPLACE VIEW `v_commission_detailed_summary` AS
SELECT 
    cl.order_type,
    cl.commission_user_id,
    u.nickname as commission_user_name,
    cl.commission_user_role,
    COUNT(*) as commission_count,
    SUM(cl.commission_amount) as total_commission,
    SUM(CASE WHEN cl.is_distributed = 1 THEN cl.commission_amount ELSE 0 END) as distributed_amount,
    SUM(CASE WHEN cl.distribution_type = 'online' THEN cl.commission_amount ELSE 0 END) as online_amount,
    SUM(CASE WHEN cl.distribution_type = 'offline' THEN cl.commission_amount ELSE 0 END) as offline_amount,
    SUM(cl.balance_amount) as total_balance_used,
    SUM(cl.online_payment_amount) as total_online_payment,
    AVG(cl.commission_rate) as avg_commission_rate,
    MIN(cl.createtime) as first_commission_time,
    MAX(cl.createtime) as last_commission_time
FROM fa_commission_log cl
LEFT JOIN fa_user u ON cl.commission_user_id = u.id
GROUP BY cl.order_type, cl.commission_user_id, cl.commission_user_role;

-- 4. 创建按日期统计的视图
CREATE OR REPLACE VIEW `v_commission_daily_stats` AS
SELECT 
    DATE(FROM_UNIXTIME(createtime)) as stat_date,
    order_type,
    commission_user_role,
    COUNT(*) as commission_count,
    SUM(commission_amount) as total_amount,
    SUM(CASE WHEN is_distributed = 1 THEN commission_amount ELSE 0 END) as distributed_amount,
    SUM(balance_amount) as total_balance_used
FROM fa_commission_log
GROUP BY DATE(FROM_UNIXTIME(createtime)), order_type, commission_user_role;

-- 5. 插入一些测试数据（可选）
INSERT IGNORE INTO `fa_commission_log` (
    `batch_no`, `step_no`, `order_type`, `order_id`, `order_no`, 
    `goods_id`, `goods_name`, `order_amount`, `payment_method`, 
    `balance_amount`, `online_payment_amount`, `base_amount`,
    `commission_user_id`, `commission_user_role`, `buyer_user_id`, `buyer_user_role`,
    `commission_type`, `commission_rate`, `commission_amount`, `platform_profit`, `total_distributed`,
    `calculation_rule`, `config_source`, `config_value`,
    `is_distributed`, `distribution_type`, `distribution_status`,
    `createtime`
) VALUES 
(
    'course_1001_test', 1, 'course', 1001, 'C202412240001',
    10, '养老院长课程', 6800.00, 'mixed',
    1000.00, 5800.00, 6800.00,
    2, 'nursing_home_director', 1001, 'regular_user',
    'direct', 50.00, 3400.00, 0.00, 6800.00,
    '养老院长直接分佣：订单总额 6800 × 50% = 3400', 'global', '50%',
    1, 'online', 'success',
    UNIX_TIMESTAMP()
),
(
    'course_1001_test', 2, 'course', 1001, 'C202412240001',
    10, '养老院长课程', 6800.00, 'mixed',
    1000.00, 5800.00, 3400.00,
    3, 'elderly_advisor', 1001, 'regular_user',
    'indirect', 50.00, 1700.00, 0.00, 6800.00,
    '养老顾问分佣：养老院长分佣 3400 × 50% = 1700', 'special_rule', '50%',
    0, 'offline', 'pending',
    UNIX_TIMESTAMP()
);

-- 6. 插入余额支付测试数据
INSERT IGNORE INTO `fa_balance_payment_log` (
    `order_type`, `order_id`, `order_no`, `user_id`,
    `balance_before`, `balance_used`, `balance_after`,
    `online_payment`, `total_amount`, `payment_method`,
    `memo`, `createtime`
) VALUES (
    'course', 1001, 'C202412240001', 1001,
    2000.00, 1000.00, 1000.00,
    5800.00, 6800.00, 'wechat',
    '订单支付：余额 1000 + wechat 5800 = 总计 6800',
    UNIX_TIMESTAMP()
);

-- 7. 创建分佣日志查询的存储过程
DELIMITER $$
CREATE PROCEDURE `GetOrderCommissionDetails`(
    IN p_order_type VARCHAR(20),
    IN p_order_id INT
)
BEGIN
    SELECT 
        cl.*,
        u.nickname as commission_user_name,
        bu.nickname as buyer_user_name,
        CASE cl.commission_user_role
            WHEN 'platform' THEN '平台'
            WHEN 'city_manager' THEN '城市负责人'
            WHEN 'nursing_home_director' THEN '养老院长'
            WHEN 'elderly_advisor' THEN '养老顾问'
            WHEN 'teacher' THEN '老师'
            WHEN 'service_provider' THEN '服务者'
            ELSE cl.commission_user_role
        END as commission_user_role_cn,
        CASE cl.commission_type
            WHEN 'direct' THEN '直接分佣'
            WHEN 'indirect' THEN '间接分佣'
            WHEN 'recommendation' THEN '推荐分佣'
            WHEN 'teacher' THEN '老师分佣'
            WHEN 'service' THEN '服务者分佣'
            ELSE cl.commission_type
        END as commission_type_cn,
        FROM_UNIXTIME(cl.createtime) as createtime_formatted
    FROM fa_commission_log cl
    LEFT JOIN fa_user u ON cl.commission_user_id = u.id
    LEFT JOIN fa_user bu ON cl.buyer_user_id = bu.id
    WHERE cl.order_type = p_order_type AND cl.order_id = p_order_id
    ORDER BY cl.step_no ASC;
END$$
DELIMITER ;

-- 8. 创建用户分佣统计的存储过程
DELIMITER $$
CREATE PROCEDURE `GetUserCommissionStats`(
    IN p_user_id INT,
    IN p_start_date DATE,
    IN p_end_date DATE
)
BEGIN
    SELECT 
        order_type,
        commission_type,
        COUNT(*) as commission_count,
        SUM(commission_amount) as total_amount,
        SUM(CASE WHEN is_distributed = 1 THEN commission_amount ELSE 0 END) as distributed_amount,
        SUM(CASE WHEN distribution_type = 'online' THEN commission_amount ELSE 0 END) as online_amount,
        SUM(CASE WHEN distribution_type = 'offline' THEN commission_amount ELSE 0 END) as offline_amount,
        AVG(commission_rate) as avg_rate
    FROM fa_commission_log
    WHERE commission_user_id = p_user_id
    AND DATE(FROM_UNIXTIME(createtime)) BETWEEN p_start_date AND p_end_date
    GROUP BY order_type, commission_type
    ORDER BY order_type, commission_type;
END$$
DELIMITER ;

-- 9. 添加配置项（如果不存在）
INSERT IGNORE INTO `fa_config` (`name`, `group`, `title`, `tip`, `type`, `value`, `content`, `rule`, `extend`) VALUES
('commission_log_enabled', 'system', '启用分佣日志', '是否启用详细的分佣日志记录', 'radio', '1', '["0","1"]', '', ''),
('commission_log_retention_days', 'system', '分佣日志保留天数', '分佣日志数据保留天数，0表示永久保留', 'number', '365', '', 'require|number', ''),
('balance_payment_log_enabled', 'system', '启用余额支付日志', '是否启用余额支付详细日志', 'radio', '1', '["0","1"]', '', '');

-- 完成提示
SELECT '分佣日志系统部署完成！' as message;
SELECT '请检查以下表是否创建成功：' as notice;
SELECT 'fa_commission_log, fa_balance_payment_log' as tables;
SELECT '请检查以下视图是否创建成功：' as notice2;
SELECT 'v_commission_detailed_summary, v_commission_daily_stats' as views;
