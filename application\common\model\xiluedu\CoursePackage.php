<?php

namespace app\common\model\xiluedu;

use think\Model;

/**
 * 课程套餐模型
 */
class CoursePackage extends Model
{
    // 表名
    protected $name = 'xiluedu_course_package';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';

    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    protected $deleteTime = false;

    // 追加属性
    protected $append = [
        'status_text',
        'user_types_text'
    ];

    /**
     * 状态文本获取器
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = [0 => '禁用', 1 => '启用'];
        return isset($status[$data['status']]) ? $status[$data['status']] : '';
    }

    /**
     * 适用人员类型文本获取器
     */
    public function getUserTypesTextAttr($value, $data)
    {
        $types = [
            'qydl' => '城市负责人',
            'sqdl' => '养老院长', 
            'ylgw' => '养老顾问',
            'user' => '普通用户'
        ];
        
        if (empty($data['user_types'])) {
            return '';
        }
        
        $userTypes = explode(',', $data['user_types']);
        $typeTexts = [];
        
        foreach ($userTypes as $type) {
            if (isset($types[$type])) {
                $typeTexts[] = $types[$type];
            }
        }
        
        return implode('、', $typeTexts);
    }

    /**
     * 关联课程
     */
    public function course()
    {
        return $this->belongsTo('Course', 'course_id');
    }

    /**
     * 根据用户类型获取价格
     */
    public function getPriceByUserType($userType)
    {
        switch ($userType) {
            case 'qydl':
                return $this->qydl_price;
            case 'sqdl':
                return $this->sqdl_price;
            case 'ylgw':
                return $this->ylgw_price;
            default:
                return $this->original_price;
        }
    }

    /**
     * 检查用户是否可以购买此套餐
     */
    public function canUserBuy($userType)
    {
        if (empty($this->user_types)) {
            return false;
        }
        
        $allowedTypes = explode(',', $this->user_types);
        return in_array($userType, $allowedTypes);
    }

    /**
     * 获取课程的所有套餐
     */
    public static function getCoursePackages($courseId, $userType = null)
    {
        $query = self::where('course_id', $courseId)
            ->where('status', 1)
            ->order('sort', 'asc');
            
        if ($userType) {
            $query->where('user_types', 'like', "%{$userType}%");
        }
        
        return $query->select();
    }

    /**
     * 获取套餐的总价
     * 注意：套餐价格是固定的，不按数量计算
     * quantity字段表示购买的养老顾问名额数量
     */
    public function getTotalPrice($userType = 'user')
    {
        return $this->getPriceByUserType($userType);
    }
}
