<?php
// 测试APP升级接口
require_once './thinkphp/start.php';

use think\Db;

try {
    // 测试获取配置
    $configModel = model('app\common\model\Config');
    
    echo "=== 测试APP升级接口 ===\n";
    
    // 获取Android版本信息
    $androidVersion = $configModel->where('name', 'android_app_version')->value('value');
    $androidApp = $configModel->where('name', 'android_app')->value('value');
    
    echo "Android版本: " . ($androidVersion ?: '未设置') . "\n";
    echo "Android下载链接: " . ($androidApp ?: '未设置') . "\n";
    
    // 获取iOS版本信息
    $iosVersion = $configModel->where('name', 'ios_app_version')->value('value');
    $iosApp = $configModel->where('name', 'ios_app')->value('value');
    
    echo "iOS版本: " . ($iosVersion ?: '未设置') . "\n";
    echo "iOS下载链接: " . ($iosApp ?: '未设置') . "\n";
    
    // 测试版本比较
    $currentVersion = '1.0.0';
    $latestVersion = $androidVersion ?: '1.0.8';
    
    echo "\n=== 版本比较测试 ===\n";
    echo "当前版本: {$currentVersion}\n";
    echo "最新版本: {$latestVersion}\n";
    
    $hasUpdate = version_compare($latestVersion, $currentVersion, '>');
    echo "是否有更新: " . ($hasUpdate ? '是' : '否') . "\n";
    
    // 测试文件大小获取
    if ($androidApp) {
        $filePath = './public' . $androidApp;
        if (file_exists($filePath)) {
            $size = filesize($filePath);
            echo "文件大小: " . formatFileSize($size) . "\n";
        } else {
            echo "文件不存在: {$filePath}\n";
        }
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "错误行号: " . $e->getLine() . "\n";
    echo "错误文件: " . $e->getFile() . "\n";
}

function formatFileSize($size)
{
    $units = ['B', 'KB', 'MB', 'GB'];
    $unitIndex = 0;
    
    while ($size >= 1024 && $unitIndex < count($units) - 1) {
        $size /= 1024;
        $unitIndex++;
    }
    
    return round($size, 1) . $units[$unitIndex];
}
