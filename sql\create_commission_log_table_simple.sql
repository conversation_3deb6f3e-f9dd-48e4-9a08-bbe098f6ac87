-- 简单创建分佣日志表和测试数据

-- 创建表（如果不存在）
CREATE TABLE IF NOT EXISTS `fa_commission_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(32) NOT NULL DEFAULT '',
  `step_no` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `order_type` varchar(20) NOT NULL DEFAULT '',
  `order_id` int(10) unsigned NOT NULL DEFAULT '0',
  `order_no` varchar(50) NOT NULL DEFAULT '',
  `goods_id` int(10) unsigned NOT NULL DEFAULT '0',
  `goods_name` varchar(255) NOT NULL DEFAULT '',
  `order_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `base_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `commission_user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `commission_user_role` varchar(50) NOT NULL DEFAULT '',
  `buyer_user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `buyer_user_role` varchar(50) NOT NULL DEFAULT '',
  `commission_type` varchar(20) NOT NULL DEFAULT '',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `commission_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `calculation_rule` text,
  `config_source` varchar(20) NOT NULL DEFAULT 'global',
  `config_value` varchar(100) NOT NULL DEFAULT '',
  `is_distributed` tinyint(1) NOT NULL DEFAULT '0',
  `distribution_type` varchar(10) NOT NULL DEFAULT 'online',
  `distribution_status` varchar(10) NOT NULL DEFAULT 'pending',
  `distribution_memo` varchar(500) NOT NULL DEFAULT '',
  `money_log_id` int(10) unsigned NOT NULL DEFAULT '0',
  `parent_user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `parent_user_role` varchar(50) NOT NULL DEFAULT 'platform',
  `has_superior` tinyint(1) NOT NULL DEFAULT '0',
  `createtime` bigint(16) NOT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_order` (`order_type`, `order_id`),
  KEY `idx_commission_user` (`commission_user_id`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分佣日志表';

-- 插入测试数据
INSERT IGNORE INTO `fa_commission_log` VALUES 
(1, 'course_1001_**********', 1, 'course', 1001, 'C202412240001', 10, '养老院长课程', 6800.00, 6800.00, 2, 'nursing_home_director', 1001, 'regular_user', 'direct', 50.00, 3400.00, '养老院长直接分佣50%', 'global', '50%', 1, 'online', 'success', '已发放到账户', 101, 0, 'platform', 0, **********),
(2, 'course_1001_**********', 2, 'course', 1001, 'C202412240001', 10, '养老院长课程', 6800.00, 3400.00, 3, 'elderly_advisor', 1001, 'regular_user', 'indirect', 50.00, 1700.00, '养老顾问获得养老院长分佣的50%', 'global', '50%', 0, 'offline', 'pending', '等待线下发放', 0, 2, 'nursing_home_director', 1, **********),
(3, 'wanlshop_2001_**********', 1, 'wanlshop', 2001, 'W202412240001', 201, '养老用品套装', 299.00, 299.00, 4, 'elderly_advisor', 1002, 'regular_user', 'direct', 30.00, 89.70, '养老顾问商城分佣30%', 'global', '30%', 1, 'online', 'success', '已发放到账户', 102, 0, 'platform', 0, **********),
(4, 'service_3001_**********', 1, 'service', 3001, 'S202412240001', 301, '上门护理服务', 500.00, 500.00, 5, 'service_provider', 1003, 'elderly_advisor', 'service', 20.00, 100.00, '服务者分佣20%', 'global', '20%', 0, 'online', 'pending', '等待发放', 0, 4, 'elderly_advisor', 1, **********),
(5, 'course_package_4001_**********', 1, 'course_package', 4001, 'CP202412240001', 16, '课程套餐（10个名额）', 1000.00, 1000.00, 6, 'city_manager', 1004, 'nursing_home_director', 'direct', 10.00, 100.00, '城市负责人套餐分佣10%', 'global', '10%', 1, 'online', 'success', '已发放到账户', 103, 0, 'platform', 0, **********);

SELECT '表创建完成，已插入测试数据' as result;
