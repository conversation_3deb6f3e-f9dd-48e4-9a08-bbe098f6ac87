-- 服务者APP更新配置相关SQL
-- 执行时间：2024-06-26

-- 1. 添加APP更新相关配置项到fa_config表
INSERT INTO `fa_config` (`name`, `group`, `title`, `tip`, `type`, `value`, `content`, `rule`, `extend`) VALUES
('service_app_update_check', 'service', '启用APP更新检查', '是否启用APP版本更新检查功能', 'radio', '1', '[\"1\",\"0\"]', '', ''),
('service_app_update_log', 'service', '记录APP更新日志', '是否记录APP更新检查操作日志', 'radio', '1', '[\"1\",\"0\"]', '', ''),
('service_app_update_content', 'service', 'APP更新内容', '填写APP更新的详细内容，支持富文本格式', 'text', '1. 优化用户体验\n2. 修复已知问题\n3. 提升系统稳定性\n4. 新增功能特性', '', '', ''),
('service_android_app_version', 'service', 'Android APP版本号', '当前Android APP的版本号', 'string', '1.0.0', '', 'require', ''),
('service_android_app_download', 'service', 'Android APP下载地址', 'Android APP安装包下载地址', 'string', '', '', '', ''),
('service_ios_app_version', 'service', 'iOS APP版本号', '当前iOS APP的版本号', 'string', '1.0.0', '', 'require', ''),
('service_ios_app_download', 'service', 'iOS APP下载地址', 'iOS APP安装包下载地址', 'string', '', '', '', ''),
('service_app_force_update', 'service', '强制更新', '是否强制用户更新APP', 'radio', '0', '[\"1\",\"0\"]', '', '');

-- 2. 创建APP更新日志表（如果不存在）
CREATE TABLE IF NOT EXISTS `fa_service_app_update_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `platform` varchar(20) NOT NULL DEFAULT '' COMMENT '平台类型(android/ios)',
  `old_version` varchar(20) NOT NULL DEFAULT '' COMMENT '旧版本号',
  `new_version` varchar(20) NOT NULL DEFAULT '' COMMENT '新版本号',
  `update_type` enum('check','download','install') NOT NULL DEFAULT 'check' COMMENT '更新类型',
  `ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(500) NOT NULL DEFAULT '' COMMENT '用户代理',
  `createtime` int(10) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `platform` (`platform`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务者APP更新日志表';

-- 3. 查看创建结果
SELECT 'Service APP Update Config Created' as status;
SELECT name, title, value FROM fa_config WHERE name LIKE 'service_app_%' OR name LIKE 'service_android_%' OR name LIKE 'service_ios_%';

-- 注意事项：
-- 1. 配置项添加后需要在后台系统配置中进行设置
-- 2. APP版本号建议使用语义化版本号格式（如：1.0.0）
-- 3. 下载地址可以是相对路径或完整URL
-- 4. 富文本内容支持HTML格式，前端需要正确解析
-- 5. 日志表会记录用户的更新检查行为，注意定期清理
