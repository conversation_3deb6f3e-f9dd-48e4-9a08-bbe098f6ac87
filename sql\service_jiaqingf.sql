/*
 Navicat Premium Dump SQL

 Source Server         : 嘉庆福
 Source Server Type    : MySQL
 Source Server Version : 50744 (5.7.44-log)
 Source Host           : *************:3306
 Source Schema         : service_jiaqingf

 Target Server Type    : MySQL
 Target Server Version : 50744 (5.7.44-log)
 File Encoding         : 65001

 Date: 16/06/2025 15:14:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for fa_admin
-- ----------------------------
DROP TABLE IF EXISTS `fa_admin`;
CREATE TABLE `fa_admin`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `username` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户名',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '昵称',
  `password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
  `salt` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码盐',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '电子邮箱',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `loginfailure` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '失败次数',
  `logintime` bigint(16) NULL DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '登录IP',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `token` varchar(59) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'Session标识',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `username`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '管理员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_admin_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_admin_log`;
CREATE TABLE `fa_admin_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `username` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '管理员名字',
  `url` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作页面',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '日志标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'IP',
  `useragent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'User-Agent',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '操作时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `name`(`username`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17098 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '管理员日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_ads
-- ----------------------------
DROP TABLE IF EXISTS `fa_ads`;
CREATE TABLE `fa_ads`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '广告名称',
  `ads_type_ids` int(11) NOT NULL COMMENT '所属分类',
  `thumb_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `url_type` enum('1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '跳转类型:1=H5,2=小程序',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '跳转链接',
  `mini_appid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序Appid',
  `status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=显示',
  `weigh` int(11) NOT NULL DEFAULT 1 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL,
  `updatetime` bigint(16) NULL DEFAULT NULL,
  `deletetime` bigint(16) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 50 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '广告列表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_ads_type
-- ----------------------------
DROP TABLE IF EXISTS `fa_ads_type`;
CREATE TABLE `fa_ads_type`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` enum('1','2','3','4','5','6') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型:1=服务,2=商城,3=课程,4=服务端',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类名称',
  `status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL,
  `updatetime` bigint(16) NULL DEFAULT NULL,
  `deletetime` bigint(16) NULL DEFAULT NULL,
  `weigh` int(11) NOT NULL DEFAULT 1 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '广告分类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_area
-- ----------------------------
DROP TABLE IF EXISTS `fa_area`;
CREATE TABLE `fa_area`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `pid` int(10) NULL DEFAULT NULL COMMENT '父id',
  `shortname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '简称',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `mergename` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '全称',
  `level` tinyint(4) NULL DEFAULT NULL COMMENT '层级:1=省,2=市,3=区/县',
  `pinyin` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拼音',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '长途区号',
  `zip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮编',
  `first` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '首字母',
  `lng` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '经度',
  `lat` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '纬度',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3749 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '地区表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_attachment
-- ----------------------------
DROP TABLE IF EXISTS `fa_attachment`;
CREATE TABLE `fa_attachment`  (
  `id` int(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '类别',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '物理路径',
  `imagewidth` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '宽度',
  `imageheight` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '高度',
  `imagetype` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图片类型',
  `imageframes` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '图片帧数',
  `filename` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '文件名称',
  `filesize` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件大小',
  `mimetype` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'mime类型',
  `extparam` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '透传数据',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建日期',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `uploadtime` bigint(16) NULL DEFAULT NULL COMMENT '上传时间',
  `storage` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'local' COMMENT '存储位置',
  `sha1` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '文件 sha1编码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26363 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_attachment_copy1
-- ----------------------------
DROP TABLE IF EXISTS `fa_attachment_copy1`;
CREATE TABLE `fa_attachment_copy1`  (
  `id` int(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '类别',
  `admin_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '物理路径',
  `imagewidth` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '宽度',
  `imageheight` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '高度',
  `imagetype` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图片类型',
  `imageframes` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '图片帧数',
  `filename` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '文件名称',
  `filesize` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '文件大小',
  `mimetype` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'mime类型',
  `extparam` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '透传数据',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建日期',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `uploadtime` bigint(16) NULL DEFAULT NULL COMMENT '上传时间',
  `storage` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'local' COMMENT '存储位置',
  `sha1` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '文件 sha1编码',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 397 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附件表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_auth_group
-- ----------------------------
DROP TABLE IF EXISTS `fa_auth_group`;
CREATE TABLE `fa_auth_group`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父组别',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '组名',
  `rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规则ID',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_auth_group_access
-- ----------------------------
DROP TABLE IF EXISTS `fa_auth_group_access`;
CREATE TABLE `fa_auth_group_access`  (
  `uid` int(10) UNSIGNED NOT NULL COMMENT '会员ID',
  `group_id` int(10) UNSIGNED NOT NULL COMMENT '级别ID',
  UNIQUE INDEX `uid_group_id`(`uid`, `group_id`) USING BTREE,
  INDEX `uid`(`uid`) USING BTREE,
  INDEX `group_id`(`group_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '权限分组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_auth_rule
-- ----------------------------
DROP TABLE IF EXISTS `fa_auth_rule`;
CREATE TABLE `fa_auth_rule`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` enum('menu','file') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'file' COMMENT 'menu为菜单,file为权限节点',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规则名称',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规则名称',
  `icon` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图标',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '规则URL',
  `condition` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '条件',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `ismenu` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否为菜单',
  `menutype` enum('addtabs','blank','dialog','ajax') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '菜单类型',
  `extend` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '扩展属性',
  `py` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '拼音首字母',
  `pinyin` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '拼音',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE,
  INDEX `weigh`(`weigh`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1499 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '节点表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_big_customer
-- ----------------------------
DROP TABLE IF EXISTS `fa_big_customer`;
CREATE TABLE `fa_big_customer`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) NULL DEFAULT NULL COMMENT '区域代理',
  `group_id` int(10) NULL DEFAULT NULL COMMENT '分组',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `phone` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '电话',
  `gender` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `birthday` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '出生',
  `age` int(10) NULL DEFAULT NULL COMMENT '年龄',
  `disease` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '疾病',
  `province` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '市',
  `district` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '县',
  `addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '住址',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 44 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '大客户信息' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_big_customer
_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_big_customer
_order`;
CREATE TABLE `fa_big_customer
_order`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL,
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `order_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `price` decimal(10, 2) NULL DEFAULT NULL,
  `kehu_info` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `status` tinyint(1) NULL DEFAULT 0,
  `paytime` bigint(16) NULL DEFAULT NULL,
  `createtime` bigint(16) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_big_customer_group
-- ----------------------------
DROP TABLE IF EXISTS `fa_big_customer_group`;
CREATE TABLE `fa_big_customer_group`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '区域代理ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '大客户名称',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '大客户管理' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_category
-- ----------------------------
DROP TABLE IF EXISTS `fa_category`;
CREATE TABLE `fa_category`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父ID',
  `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '栏目类型',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `flag` set('hot','index','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图片',
  `keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '关键字',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '描述',
  `diyname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '自定义名称',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `weigh`(`weigh`, `id`) USING BTREE,
  INDEX `pid`(`pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_command
-- ----------------------------
DROP TABLE IF EXISTS `fa_command`;
CREATE TABLE `fa_command`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '类型',
  `params` varchar(1500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '参数',
  `command` varchar(1500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '命令',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '返回结果',
  `executetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '执行时间',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `status` enum('successed','failured') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'failured' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 129 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '在线命令表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_config`;
CREATE TABLE `fa_config`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '变量名',
  `group` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '分组',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '变量标题',
  `tip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '变量描述',
  `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '类型:string,text,int,bool,array,datetime,date,file',
  `visible` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '可见条件',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '变量值',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '变量字典数据',
  `rule` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证规则',
  `extend` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '扩展属性',
  `setting` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '配置',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 118 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_course_module
-- ----------------------------
DROP TABLE IF EXISTS `fa_course_module`;
CREATE TABLE `fa_course_module`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `course_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '课程',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模块名称',
  `status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL,
  `updatetime` bigint(16) NULL DEFAULT NULL,
  `deletetime` bigint(16) NULL DEFAULT NULL,
  `type` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '类型:0=横版,1=竖版',
  `weigh` int(11) NULL DEFAULT 1 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '课程首页模块' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_cron_tasks
-- ----------------------------
DROP TABLE IF EXISTS `fa_cron_tasks`;
CREATE TABLE `fa_cron_tasks`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务名称',
  `command` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '执行命令',
  `cron_expression` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Cron表达式',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务描述',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=禁用,1=启用',
  `last_run_time` int(10) NULL DEFAULT NULL COMMENT '最后执行时间',
  `next_run_time` int(10) NULL DEFAULT NULL COMMENT '下次执行时间',
  `run_count` int(11) NOT NULL DEFAULT 0 COMMENT '执行次数',
  `success_count` int(11) NOT NULL DEFAULT 0 COMMENT '成功次数',
  `fail_count` int(11) NOT NULL DEFAULT 0 COMMENT '失败次数',
  `last_result` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '最后执行结果',
  `createtime` int(10) NOT NULL COMMENT '创建时间',
  `updatetime` int(10) NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '定时任务表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_ems
-- ----------------------------
DROP TABLE IF EXISTS `fa_ems`;
CREATE TABLE `fa_ems`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `event` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '事件',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '邮箱',
  `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证码',
  `times` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '验证次数',
  `ip` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'IP',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 136 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '邮箱验证码表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_fixed_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_fixed_log`;
CREATE TABLE `fa_fixed_log`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `skill_user_id` int(11) NULL DEFAULT NULL,
  `start_imgs` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `start_time` datetime NULL DEFAULT NULL,
  `finish_imgs` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `finish_time` datetime NULL DEFAULT NULL,
  `num` int(1) NULL DEFAULT NULL,
  `confirm_time` datetime NULL DEFAULT NULL,
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '1=服务人员到达，2=服务开始，3=服务结束，4=用户确认',
  `createtime` bigint(16) NULL DEFAULT NULL,
  `reach_time` datetime NULL DEFAULT NULL,
  `reach_imgs` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_invoice
-- ----------------------------
DROP TABLE IF EXISTS `fa_invoice`;
CREATE TABLE `fa_invoice`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户id',
  `order_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联订单',
  `money` decimal(10, 2) NULL DEFAULT NULL COMMENT '金额',
  `type` tinyint(1) NULL DEFAULT NULL COMMENT '类型:1=个人,2=企业',
  `title` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '抬头',
  `fp_file` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '发票',
  `credit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '信用代码',
  `status` enum('1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '状态:1=待开票,2=已开票',
  `createtime` bigint(11) NULL DEFAULT NULL COMMENT '创建时间',
  `opentime` bigint(11) NULL DEFAULT NULL COMMENT '开票时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '发票' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_nav
-- ----------------------------
DROP TABLE IF EXISTS `fa_nav`;
CREATE TABLE `fa_nav`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '导航名称',
  `thumb_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `group` enum('1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '分组:1=导航一,2=导航二',
  `type` enum('1','2','3') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '类型:1=服务,2=商城,3=课程',
  `url_type` enum('1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '跳转类型:1=H5,2=小程序',
  `url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '跳转链接',
  `mini_appid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小程序Appid',
  `status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=显示',
  `weigh` int(11) NOT NULL DEFAULT 1 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL,
  `updatetime` bigint(16) NULL DEFAULT NULL,
  `deletetime` bigint(16) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 39 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '导航配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_qun
-- ----------------------------
DROP TABLE IF EXISTS `fa_qun`;
CREATE TABLE `fa_qun`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `userarea_id` int(11) NULL DEFAULT NULL COMMENT '所属区域代理',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '群昵称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `ewm_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '二维码',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细位置',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '类型:0=服务,1=商城',
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市',
  `district` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '企业群' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_recharge
-- ----------------------------
DROP TABLE IF EXISTS `fa_recharge`;
CREATE TABLE `fa_recharge`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `service_coupon_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '赠送服务优惠券',
  `wanlshop_coupon_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '赠送商城优惠券',
  `money` decimal(11, 2) NULL DEFAULT NULL COMMENT '金额',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '添加时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '充值配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_recharge_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_recharge_order`;
CREATE TABLE `fa_recharge_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `orderid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单ID',
  `user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '会员ID',
  `amount` double(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '订单金额',
  `payamount` double(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '支付金额',
  `paytype` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '支付类型',
  `paytime` int(10) NULL DEFAULT NULL COMMENT '支付时间',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'IP地址',
  `useragent` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'UserAgent',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '添加时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `status` enum('created','paid','expired') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'created' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '充值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_recruit_jishi
-- ----------------------------
DROP TABLE IF EXISTS `fa_recruit_jishi`;
CREATE TABLE `fa_recruit_jishi`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作城市',
  `sex` tinyint(1) NULL DEFAULT 1 COMMENT '性别:1=男,2=女',
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '招募技师记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_rm_search
-- ----------------------------
DROP TABLE IF EXISTS `fa_rm_search`;
CREATE TABLE `fa_rm_search`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型:0=服务,1=商城,2=课程',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关键字',
  `weigh` int(11) NULL DEFAULT 1 COMMENT '权重',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '热门搜索' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_add_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_add_order`;
CREATE TABLE `fa_service_add_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `order_id` int(10) UNSIGNED NOT NULL COMMENT '订单id',
  `payprice` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '项目金额',
  `total_cost_seconds` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '时长',
  `paytype` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付方式:0=微信小程序,1=微信APP,2=公众号支付,3=支付宝APP,4=余额',
  `orderId` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `add_ids` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附加项目id集合',
  `trade_no` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易单号',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待支付,1=支付成功',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `paytime` bigint(16) NULL DEFAULT NULL COMMENT '支付时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附加项目订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_add_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_add_order_detail`;
CREATE TABLE `fa_service_add_order_detail`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `order_id` int(10) UNSIGNED NOT NULL COMMENT '订单id',
  `goods_add_id` int(10) UNSIGNED NOT NULL COMMENT '附加项目id',
  `service_add_order_id` int(10) NULL DEFAULT NULL COMMENT '附加项目订单id',
  `name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附加项目名称',
  `num` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '数量',
  `price` decimal(10, 2) UNSIGNED NOT NULL COMMENT '附加项目金额',
  `cost_seconds` tinyint(1) UNSIGNED NOT NULL COMMENT '时长',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单附加项目详情' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_address
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_address`;
CREATE TABLE `fa_service_address`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) NOT NULL COMMENT '用户id',
  `name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系人',
  `sex` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '性别:1=男,0=女',
  `mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '联系电话',
  `province` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '市',
  `district` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '县',
  `area` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务地址',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `lng` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经度',
  `lat` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '纬度',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=非默认,1=默认',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_agent_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_agent_order`;
CREATE TABLE `fa_service_agent_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `orderid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单编号',
  `trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '交易单号',
  `area_user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '区域代理会员ID',
  `kt_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '开通手机号',
  `price` double(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '订单金额',
  `paytype` tinyint(1) NULL DEFAULT NULL COMMENT '支付类型:0=微信小程序,1=微信APP,2=公众号支付,3=支付宝支付,4=余额',
  `paytime` int(10) NULL DEFAULT NULL COMMENT '支付时间',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '备注',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '添加时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `status` enum('created','paid') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'created' COMMENT '状态:created=未支付,paid=已支付',
  `addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT ' 区域',
  `type` tinyint(1) NULL DEFAULT 1 COMMENT '1=代理开通，2=自己开通',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '开通社区代理记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_apply_shop
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_apply_shop`;
CREATE TABLE `fa_service_apply_shop`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '商家用户id',
  `applytype` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型:0=申请,1=完善资料',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家名称',
  `abbr` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '缩写',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型:0=企业,1=个体工商户',
  `credit_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统一社会信用代码',
  `license_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '营业执照',
  `username` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '法人姓名',
  `mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '法人手机号',
  `idcard` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号',
  `front_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证正面',
  `opposite_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证反面',
  `intro` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家简介',
  `logo_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'logo',
  `category_ids` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经营分类',
  `goods_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务项目',
  `leader_name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '负责人姓名',
  `leader_mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '负责人手机号',
  `trade_hour` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营业时间',
  `province` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '县',
  `address` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `lng` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经度',
  `lat` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '纬度',
  `state` enum('0','1','-1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '状态:0=待审核,1=审核通过,-1=审核拒绝',
  `note` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝理由',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '申请商户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_apply_skill
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_apply_skill`;
CREATE TABLE `fa_service_apply_skill`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `applytype` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型:0=申请,1=完善资料',
  `name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `sex` tinyint(1) NOT NULL DEFAULT 0 COMMENT '性别:1=男,0=女',
  `idcard` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号码',
  `front_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证正面',
  `opposite_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证反面',
  `user_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '实拍照',
  `certificate_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '从业资格证',
  `health_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '健康证照片',
  `health_sc_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手持健康证照片',
  `health_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '健康证持卡人',
  `health_card` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '健康证号',
  `health_end` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '健康证结束日期',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '职业技能id',
  `skill_cate_id` int(10) UNSIGNED NOT NULL COMMENT '服务技能id',
  `goods_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务项目',
  `image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工装照',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生活照',
  `edu` tinyint(1) UNSIGNED NOT NULL COMMENT '学历:0=小学文化,1=初中,2=高中,3=大专,4=本科,5=硕士,6=博士',
  `nation` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '民族',
  `age` tinyint(1) UNSIGNED NOT NULL COMMENT '年龄',
  `height` tinyint(1) NULL DEFAULT NULL COMMENT '身高/cm',
  `weight` tinyint(1) NULL DEFAULT NULL COMMENT '体重/kg',
  `exper` tinyint(1) NOT NULL DEFAULT 0 COMMENT '经验:0=1年以下,1=1-3年,2=3-5年,3=5年以上',
  `province` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '县',
  `address` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `lng` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经度',
  `lat` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '纬度',
  `note` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `state` enum('0','1','-1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '审核状态:0=待审核,1=审核通过,-1=审核拒绝',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `hx_range` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核心服务区域',
  `zd_range` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重点服务区域',
  `qt_range` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他服务区域',
  `xy_sign` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '协议签署内容',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `city`(`city`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 162 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '申请服务者' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_bm
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_bm`;
CREATE TABLE `fa_service_bm`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '所属用户ID',
  `tx_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '头像',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `sex` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '性别',
  `work_time` bigint(16) NULL DEFAULT 0 COMMENT '工作时间',
  `cs_time` bigint(16) NULL DEFAULT 0 COMMENT '出生日期',
  `sx` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生肖',
  `xz` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '星座',
  `gzqk` enum('1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作情况:1=保姆,2=护理员',
  `zjqk` enum('0','1','2','3','4') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '住家情况:0=均可,1=住家,2=白天全班,3=上午白班,4=下午白班',
  `js` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '自我介绍',
  `scjn` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '擅长技能',
  `rz_image` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '认证证书',
  `shz_images` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生活照',
  `status` enum('0','1','2','3') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态:0=待审核,1=审核驳回,2=待上架,3=已上架',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `work` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '驳回原因',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '护工保姆' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_bm_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_bm_log`;
CREATE TABLE `fa_service_bm_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `bm_num` int(10) NOT NULL DEFAULT 0 COMMENT '变更次数',
  `before` int(10) NOT NULL DEFAULT 0 COMMENT '变更前次数',
  `after` int(10) NOT NULL DEFAULT 0 COMMENT '变更后次数',
  `bm_id` int(11) NULL DEFAULT NULL COMMENT '解锁保姆ID',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '保姆次数变动' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_bm_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_bm_order`;
CREATE TABLE `fa_service_bm_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `orderid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单编号',
  `trade_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '交易单号',
  `user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '会员ID',
  `tc_id` int(10) NULL DEFAULT NULL COMMENT '解锁套餐ID',
  `price` double(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '订单金额',
  `paytype` tinyint(1) NULL DEFAULT NULL COMMENT '支付类型:0=微信小程序,1=微信APP,2=公众号支付,3=支付宝支付,4=余额',
  `paytime` int(10) NULL DEFAULT NULL COMMENT '支付时间',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `num` int(11) NULL DEFAULT 0 COMMENT '解锁数量',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '添加时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `status` enum('created','paid') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'created' COMMENT '状态:created=未支付,paid=已支付',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '解锁订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_bm_tc
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_bm_tc`;
CREATE TABLE `fa_service_bm_tc`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '套餐名称',
  `price` decimal(10, 2) NULL DEFAULT NULL COMMENT '价格',
  `market_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '市场价',
  `num` int(11) NULL DEFAULT 0 COMMENT '解锁数量',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '解锁套餐' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_bm_work
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_bm_work`;
CREATE TABLE `fa_service_bm_work`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `bm_id` int(11) NULL DEFAULT NULL COMMENT '所属保姆ID',
  `start_time` int(11) NULL DEFAULT NULL COMMENT '入职开始时间',
  `end_time` int(11) NULL DEFAULT NULL COMMENT '入驻结束时间',
  `desc` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作内容',
  `updatetime` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 444 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '护工保姆工作经历' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_bm_xq
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_bm_xq`;
CREATE TABLE `fa_service_bm_xq`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '所属用户ID',
  `fw_type` enum('1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '工作情况:1=保姆,2=护理员',
  `xq` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '需求介绍',
  `fw_times` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务时间',
  `zjqk` enum('0','1','2','3','4') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '住家情况:0=均可,1=住家,2=白天全班,3=上午白班,4=下午白班',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `gz` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预算工资',
  `fw_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '详细地址',
  `updatetime` int(11) NULL DEFAULT NULL COMMENT '更新时间',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '市',
  `district` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '县/区',
  `lng` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经度',
  `lat` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '纬度',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务地址',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户护工保姆需求' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_category
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_category`;
CREATE TABLE `fa_service_category`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '上级id',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类名称',
  `flag` set('hot','new','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标志(多选):hot=热门,new=新产品,recommend=推荐',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分类图标',
  `is_show` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否首页显示:0=否,1=是',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `pid_index`(`pid`) USING BTREE,
  INDEX `weigh`(`weigh`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 166 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_cert
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_cert`;
CREATE TABLE `fa_service_cert`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `example_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '示例图',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '证书配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_cert_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_cert_log`;
CREATE TABLE `fa_service_cert_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) NOT NULL COMMENT '用户',
  `district` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域',
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '城市',
  `cert_id` int(11) NOT NULL COMMENT '证书ID',
  `z_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '正面照',
  `b_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '背面照',
  `skill_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '选择的技能',
  `status` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态:0=审核中,1=审核通过,2=审核驳回',
  `bh_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '驳回原因',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `sign_agreement` tinyint(1) NULL DEFAULT 0,
  `sign_img` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `is_read` tinyint(1) NULL DEFAULT 0 COMMENT '1=已读,0=未读',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 335 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '证书上传记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_city_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_city_config`;
CREATE TABLE `fa_service_city_config`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市名',
  `init_price` decimal(5, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '起步价',
  `init_distance` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '起步里程/公里',
  `add_price` decimal(5, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '增加1公里增加费用',
  `bus_start` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '公交车开启时间(24小时制)',
  `bus_end` tinyint(1) NOT NULL DEFAULT 0 COMMENT '公交车结束时间(24小时制)',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `city`(`city`) USING BTREE,
  INDEX `weigh`(`weigh`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '城市出行配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_comment
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_comment`;
CREATE TABLE `fa_service_comment`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `order_id` int(10) UNSIGNED NOT NULL COMMENT '订单id',
  `skill_id` int(10) NULL DEFAULT NULL COMMENT '服务人员id',
  `shop_id` int(10) NULL DEFAULT NULL COMMENT '商家id',
  `goods_id` int(10) UNSIGNED NOT NULL COMMENT '项目id',
  `score` tinyint(1) UNSIGNED NOT NULL DEFAULT 5 COMMENT '总体评分',
  `fw_score` tinyint(1) NULL DEFAULT 5 COMMENT '服务评分',
  `zz_score` tinyint(1) NULL DEFAULT 5 COMMENT '着装评分',
  `comment_label_ids` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '评论标签id',
  `comment_label` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标签内容',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '评论内容',
  `images` varchar(800) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
  `is_img` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '有图:0=否,1=是',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `is_hide` tinyint(1) NULL DEFAULT 0 COMMENT '匿名:0=否,1=是',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `reply` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回复',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `order_id`(`order_id`) USING BTREE,
  INDEX `goods_id`(`goods_id`) USING BTREE,
  INDEX `skill_id`(`skill_id`) USING BTREE,
  INDEX `shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评论表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_comment_label
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_comment_label`;
CREATE TABLE `fa_service_comment_label`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标签内容',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评论标签表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_common_questions
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_common_questions`;
CREATE TABLE `fa_service_common_questions`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `title` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(11) NULL DEFAULT 0 COMMENT '权重',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '常见问题表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_common_questions2
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_common_questions2`;
CREATE TABLE `fa_service_common_questions2`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `title` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '问题标题',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(11) NULL DEFAULT 0 COMMENT '权重',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '常见问题表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_complaint
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_complaint`;
CREATE TABLE `fa_service_complaint`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `order_id` int(10) UNSIGNED NOT NULL COMMENT '订单id',
  `complaint_content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '投诉内容',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '补充内容',
  `images` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待处理,1=已处理,-1=已拒绝',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '投诉表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_complaint_reason
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_complaint_reason`;
CREATE TABLE `fa_service_complaint_reason`  (
  `id` int(16) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '原因',
  `state` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '投诉原因表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_config_text
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_config_text`;
CREATE TABLE `fa_service_config_text`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文本名称',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文本名称',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '富文本表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_coupon
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_coupon`;
CREATE TABLE `fa_service_coupon`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠券名称',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '0=平台通用券,1=平台项目券,3=商户通用券,4=商户项目券4=口令券,5=分享赠送范围 优惠券类型:',
  `goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '项目id',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '商户id',
  `code` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '口令',
  `achieve` int(10) UNSIGNED NOT NULL COMMENT '满',
  `reduce` int(10) UNSIGNED NOT NULL COMMENT '减',
  `starttime` bigint(16) NOT NULL COMMENT '展示开始时间',
  `endtime` bigint(16) NOT NULL COMMENT '展示结束时间',
  `effective_day` int(10) UNSIGNED NOT NULL COMMENT '领取后有效期/天',
  `is_check` tinyint(1) NOT NULL DEFAULT 0 COMMENT '审核状态:0=待审核,1=审核通过,-1=审核未通过',
  `note` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `scope` tinyint(2) NULL DEFAULT 1 COMMENT '1=通用，2=商城，3=服务，4=课程',
  `grant_num` int(10) NULL DEFAULT NULL,
  `use_num` int(10) NULL DEFAULT NULL,
  `is_service` int(1) NULL DEFAULT 0,
  `shop_coupon_id` int(11) NULL DEFAULT NULL,
  `course_coupon_id` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '优惠券表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_ensure_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_ensure_log`;
CREATE TABLE `fa_service_ensure_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更保证金',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型:0=服务者,1=商户',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '保证金日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_feedback
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_feedback`;
CREATE TABLE `fa_service_feedback`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型:0=用户端,1=服务端,2=商户端',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '反馈内容',
  `images` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
  `mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手机号',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待处理,1=已处理',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '反馈意见表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_follow
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_follow`;
CREATE TABLE `fa_service_follow`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型:0=服务者,1=服务项目,2=商家',
  `follow_id` int(10) UNSIGNED NOT NULL COMMENT '被关注id',
  `state` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=取消关注,1=关注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 53 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '关注表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_footer_view
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_footer_view`;
CREATE TABLE `fa_service_footer_view`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `footer_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'ID',
  `view_time` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '上一次看的时间',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2405 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服务足迹' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_goods`;
CREATE TABLE `fa_service_goods`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `category_id` int(10) UNSIGNED NOT NULL COMMENT '一级分类',
  `two_category_id` int(10) UNSIGNED NOT NULL COMMENT '二级分类',
  `skill_cate_ids` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '所属服务人员分类',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户id',
  `shop_user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商户的用户id',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '项目类型:0=平台项目,1=商户项目',
  `province` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省',
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '市',
  `district` char(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '县',
  `to_shop` set('door','shop') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'door' COMMENT '服务方式:door=上门服务,shop=到店核销',
  `is_travel` tinyint(1) NOT NULL DEFAULT 0 COMMENT '开启出行:0=否,1=是',
  `tag_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目标签',
  `goods_tip` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务提示',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目主图',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目轮播图',
  `video` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目视频',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '项目价格',
  `market_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '市场价',
  `start_hour` tinyint(1) NOT NULL DEFAULT 0 COMMENT '预约开始(0-23时)',
  `end_hour` tinyint(1) NOT NULL DEFAULT 0 COMMENT '预约结束(0-23时)',
  `cost_seconds` int(10) UNSIGNED NOT NULL COMMENT '时长/分钟',
  `response_hour` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '最快响应时间/小时',
  `spec_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '规格类型:0=单规格,1=多规格',
  `choose_skill_type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '选择技师:0=否,1=是',
  `flow_path_images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务流程',
  `illustrate_images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务说明',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '服务详情',
  `salenums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `shop_state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商户项目状态:0=待审核,1=审核通过,-1=审核拒绝',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '上架状态:normal=上架,hidden=下架',
  `note` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `weigh` int(10) NULL DEFAULT 0 COMMENT '权重',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  `bx_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '保险名称',
  `bx_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '保险简介',
  `is_bx` tinyint(1) NULL DEFAULT 0 COMMENT '保险:0=否,1=是',
  `bx_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '保险详情',
  `goods_type` tinyint(1) NULL DEFAULT 0 COMMENT '项目类型:0=家政商品,1=定死服务,2=按次服务',
  `is_yh` tinyint(1) NULL DEFAULT 0 COMMENT '医护项目:0=否,1=是',
  `night_money` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '夜间费用',
  `is_wt` tinyint(1) NULL DEFAULT 0 COMMENT '委托订单:0=否,1=是',
  `wt_type` tinyint(1) NULL DEFAULT 0 COMMENT '委托类型:0=按小时,1=按次数',
  `wt_qb` tinyint(1) NULL DEFAULT 1 COMMENT '委托起步,单位=半小时',
  `shequ_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '社区代理分成比例',
  `quyu_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '区域代理分成比例',
  `service_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '服务者分成比例',
  `all_city_goods_id` int(11) NULL DEFAULT 0 COMMENT '关联全国通用项目',
  `is_hot` tinyint(1) NULL DEFAULT 0 COMMENT '热门:1=是,0=否',
  `unit` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '单位',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `salesnum`(`salenums`) USING BTREE,
  INDEX `category1`(`category_id`) USING BTREE,
  INDEX `category2`(`two_category_id`) USING BTREE,
  INDEX `price`(`price`) USING BTREE,
  INDEX `shop_id`(`shop_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 136 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_goods_add
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_goods_add`;
CREATE TABLE `fa_service_goods_add`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `goods_id` int(10) NOT NULL COMMENT '所属项目id',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '预览图',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '附加项目名称',
  `price` decimal(10, 2) NOT NULL COMMENT '价格',
  `market_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '市场价',
  `type` tinyint(1) NULL DEFAULT 0 COMMENT '类型:0=附加服务,1=医护工具',
  `cost_seconds` int(10) UNSIGNED NOT NULL COMMENT '时长/分钟',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `hcb` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '耗材包',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '附加项目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_goods_sku
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_goods_sku`;
CREATE TABLE `fa_service_goods_sku`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `goods_id` int(10) UNSIGNED NOT NULL COMMENT '商品id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格名称',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',
  `cost_seconds` int(10) UNSIGNED NOT NULL COMMENT '时长/分钟',
  `salenums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态:hidden=未使用,normal=使用中',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `market_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '市场价',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `goods_id`(`goods_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 413 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品sku表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_goods_spu
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_goods_spu`;
CREATE TABLE `fa_service_goods_spu`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `goods_id` int(10) NOT NULL COMMENT '商品id',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格名称',
  `skudetail` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '规格详情',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'normal' COMMENT '状态:hidden=隐藏,normal=正常',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 126 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商品spu' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_gz
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_gz`;
CREATE TABLE `fa_service_gz`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '服务者用户ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '标题',
  `cj_time` int(11) NULL DEFAULT NULL COMMENT '抽查时间',
  `status` enum('0','1','2','3') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态:0=待上传,1=审核中,2=审核通过,3=审核驳回',
  `user_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户拍照图',
  `bh_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '驳回原因',
  `is_reset` tinyint(1) NULL DEFAULT 0 COMMENT '是否可重新提交:0=否,1=是',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '工装抽查' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_insurance
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_insurance`;
CREATE TABLE `fa_service_insurance`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL,
  `date` date NULL DEFAULT NULL,
  `money` decimal(10, 2) NULL DEFAULT NULL,
  `order_id` int(10) NULL DEFAULT NULL,
  `createtime` bigint(16) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_join_shop
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_join_shop`;
CREATE TABLE `fa_service_join_shop`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `skill_id` int(10) UNSIGNED NOT NULL COMMENT '服务人员id',
  `skill_name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务人员姓名',
  `shop_id` int(10) UNSIGNED NOT NULL COMMENT '商户id',
  `shop_name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家名称',
  `code` char(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家码',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待审核,1=审核通过,-1=审核拒绝',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '申请加入门店' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_jump
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_jump`;
CREATE TABLE `fa_service_jump`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '链接名称',
  `url` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '跳转链接',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '跳转链接' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_mini_template
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_mini_template`;
CREATE TABLE `fa_service_mini_template`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_notice_template` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '接单通知',
  `user_order_template` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单状态通知',
  `order_finish_template` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单完成推送用户',
  `user_sales_template` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '售后处理通知',
  `user_complaint_template` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户投诉结果通知',
  `skill_order_template` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务者订单通知',
  `skill_sales_template` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务者售后订单通知',
  `shop_order_template` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户新订单通知',
  `shop_finish_template` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户订单完成通知',
  `shop_sales_template` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户售后通知',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '微信推送配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_module
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_module`;
CREATE TABLE `fa_service_module`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `goods_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模块名称',
  `status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=显示',
  `type` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '类型:0=横版,1=竖版',
  `weigh` int(11) NULL DEFAULT 1 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL,
  `updatetime` bigint(16) NULL DEFAULT NULL,
  `deletetime` bigint(16) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服务首页模块' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_notice
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_notice`;
CREATE TABLE `fa_service_notice`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `title` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型:0=用户端,1=服务端,2=商户端',
  `image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '封面图',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '平台公告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_order`;
CREATE TABLE `fa_service_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `skill_id` int(10) NULL DEFAULT NULL COMMENT '服务者id',
  `shop_id` int(10) NULL DEFAULT NULL COMMENT '商户id',
  `goods_id` int(10) UNSIGNED NOT NULL COMMENT '项目id',
  `traveltype` tinyint(1) NOT NULL DEFAULT 0 COMMENT '出行方式:0=免费出行,1=出租车,2=公交',
  `choose_skill_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '选择技师:0=否,1=是',
  `paytype` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付方式:0=微信小程序,1=微信APP,2=公众号支付,3=支付宝APP,4=余额',
  `to_shop` enum('door','shop') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'door' COMMENT '服务方式:door=上门服务,shop=到店核销',
  `orderId` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市',
  `district` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区/县',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '项目金额',
  `distance` float(8, 2) NOT NULL DEFAULT 0.00 COMMENT '距离/km',
  `travel_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '出行费用',
  `sumprice` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `goods_total_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '项目实际总金额',
  `discount` tinyint(1) UNSIGNED NOT NULL DEFAULT 100 COMMENT '折扣%',
  `coupon_id` int(10) NULL DEFAULT NULL COMMENT '优惠券id',
  `coupon_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '优惠券金额',
  `premium_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '补价',
  `add_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '附加项目金额',
  `payprice` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `act_travel_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际结算车费',
  `refund_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `settle_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '结算金额',
  `total_cost_seconds` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '项目总时长/次数',
  `total_cost_use_num` int(10) NULL DEFAULT 0 COMMENT '已使用次数',
  `total_cost_sy` int(10) NULL DEFAULT 0 COMMENT '项目剩余次数',
  `starttime` bigint(16) NULL DEFAULT NULL COMMENT '开始时间',
  `endtime` bigint(16) NULL DEFAULT NULL COMMENT '结束时间',
  `actendtime` bigint(16) NULL DEFAULT NULL COMMENT '实际结束时间',
  `trade_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易单号',
  `memo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单备注',
  `qrcode_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销码',
  `check_name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核销码内容',
  `is_service` tinyint(1) NOT NULL DEFAULT 0 COMMENT '售后状态:0=未售后,1=售后中,2=已退款,-1=已拒绝',
  `is_complaint` tinyint(1) NOT NULL DEFAULT 0 COMMENT '投诉状态:0=未投诉,1=已投诉',
  `is_pool` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单池:0=否,1=是',
  `is_settle` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态:0=未结算,1=待结算,2=已结算',
  `skill_percent` tinyint(1) NOT NULL DEFAULT 0 COMMENT '服务者分佣比例',
  `shop_percent` tinyint(1) NOT NULL DEFAULT 0 COMMENT '商户分佣比例',
  `reach_images` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '到达拍照',
  `start_images` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开始服务拍照',
  `finish_images` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '完成服务拍照',
  `status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '订单状态:0=待支付,1=待接单,2=待出发,3=已出发,4=已到达,5=服务中,6=已完成,7=已评价,-1=已取消,8=用户确认,9=用户确认完成',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `paytime` bigint(16) NULL DEFAULT NULL COMMENT '支付时间',
  `accepttime` bigint(16) NULL DEFAULT NULL COMMENT '接单时间',
  `gotime` bigint(16) NULL DEFAULT NULL COMMENT '出发时间',
  `reachtime` bigint(16) NULL DEFAULT NULL COMMENT '到达时间',
  `servicetime` bigint(16) NULL DEFAULT NULL COMMENT '开始服务时间',
  `finishtime` bigint(16) NULL DEFAULT NULL COMMENT '服务完成时间',
  `settletime` bigint(16) NULL DEFAULT NULL COMMENT '结算时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  `goods_type` tinyint(1) NULL DEFAULT 0 COMMENT '项目类型:0=家政商品,1=定死服务,2=按次服务',
  `p_order_id` int(11) NULL DEFAULT 0 COMMENT '所属订单ID，按次、服务包使用',
  `is_wt` tinyint(1) NULL DEFAULT 0 COMMENT '委托订单:0=否,1=是',
  `goodsadd_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '附加服务ID',
  `goodsadd2_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '医护服务ID',
  `fwb_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '服务包需支付费用',
  `night_money` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '夜间费用',
  `sex` tinyint(1) NULL DEFAULT 0 COMMENT '限制性别:0=不限,1=男,2=女',
  `pay_end_time` int(11) NULL DEFAULT 0 COMMENT '最晚支付时间',
  `user_confirm` tinyint(1) NULL DEFAULT 0 COMMENT '用户确认订单:0=否,1=是',
  `user_confirm_time` int(11) NULL DEFAULT NULL COMMENT '用户确认时间',
  `shequ_money` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '社区代理分成金额',
  `quyu_money` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '区域代理分成金额',
  `service_money` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '服务者分成金额',
  `zj_skill_id` int(11) NULL DEFAULT 0 COMMENT '转接服务人员ID',
  `lng` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经度',
  `lat` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '纬度',
  `is_fwb` tinyint(1) NULL DEFAULT 0 COMMENT '是否服务包',
  `skill_cate_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '技能分类',
  `zj_time` bigint(16) NULL DEFAULT NULL COMMENT '转接时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_unique`(`user_id`, `createtime`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `skill_id`(`skill_id`) USING BTREE,
  INDEX `shop_id`(`shop_id`) USING BTREE,
  INDEX `status`(`status`) USING BTREE,
  INDEX `is_service`(`is_service`) USING BTREE,
  INDEX `is_settle`(`is_settle`) USING BTREE,
  INDEX `goods_id`(`goods_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 944 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_order_address
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_order_address`;
CREATE TABLE `fa_service_order_address`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `order_id` int(10) UNSIGNED NOT NULL COMMENT '订单id',
  `name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `sex` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '性别:1=男,0=女',
  `mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `province` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '县',
  `address` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `area` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务地址',
  `lng` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经度',
  `lat` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '纬度',
  `state` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=已取消,1=使用中',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `address_id` int(11) NULL DEFAULT NULL COMMENT '地址id',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 738 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单地址' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_order_detail
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_order_detail`;
CREATE TABLE `fa_service_order_detail`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `order_id` int(10) UNSIGNED NOT NULL COMMENT '订单id',
  `goods_id` int(10) UNSIGNED NOT NULL COMMENT '项目id',
  `goods_sku_id` int(10) NULL DEFAULT NULL COMMENT '项目规格id',
  `skill_id` int(10) NULL DEFAULT NULL COMMENT '服务人员id',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '项目名称',
  `image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '项目图片',
  `sku_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '规格名称',
  `num` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '项目数量',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '项目单价',
  `sumprice` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '项目总价',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `service_time_json` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务包时间',
  `bfw_id` int(11) NULL DEFAULT NULL COMMENT '被服务者ID',
  `bfw_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被服务者姓名',
  `bfw_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被服务者手机号',
  `bfw_sex` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '被服务器性别',
  `case` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '病例',
  `is_yh_tools` tinyint(1) NULL DEFAULT 0 COMMENT '医护工具:0=无,1=有',
  `is_gm` tinyint(1) NULL DEFAULT 0 COMMENT '过敏史:0=无,1=有',
  `gm_text` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '过敏史说明',
  `cf_images` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '就医处方照片',
  `yp_images` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '就医药品照片',
  `wt_desc` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '委托说明',
  `wt_images` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '委托图片',
  `is_yh` tinyint(1) NULL DEFAULT 0 COMMENT '医护项目:0=否,1=是',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 741 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单详情表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_order_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_order_log`;
CREATE TABLE `fa_service_order_log`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `order_id` int(10) UNSIGNED NOT NULL COMMENT '订单id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '订单用户id',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单状态:0=创建订单,1=已支付,2=服务者已接单,3=已出发,4=已到达,5=服务中,6=已完成,7=已评价,8=申请退款,9=已退款,10=申请退款拒绝,11=订单投诉,12=订单补差价,13=订单添加附加项目,14=订单已分配,15=商户已接单,16=平台已分配,17=用户取消申请退款,18=投诉已处理,19=取消订单,20=用户确认订单',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1795 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_order_zj
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_order_zj`;
CREATE TABLE `fa_service_order_zj`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NULL DEFAULT NULL COMMENT '转接订单ID',
  `user_id` int(11) NULL DEFAULT NULL COMMENT '服务人员ID',
  `type` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型:0=直接转接,1=抢单大厅,2=呼叫调度',
  `djdd_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '呼叫调度原因',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服务人员转接记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_plus_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_plus_config`;
CREATE TABLE `fa_service_plus_config`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '会员名称',
  `daynums` int(3) UNSIGNED NOT NULL COMMENT '有效天数',
  `original_price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '原价',
  `price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '现价',
  `discount` tinyint(1) UNSIGNED NOT NULL COMMENT '享受折扣%',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_plus_pay
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_plus_pay`;
CREATE TABLE `fa_service_plus_pay`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) NOT NULL COMMENT '用户id',
  `plus_id` int(10) UNSIGNED NOT NULL COMMENT '选取配置id',
  `price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `orderId` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `trade_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易单号',
  `paytype` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付类型:0=微信小程序,1=微信APP,2=公众号支付,3=支付宝支付',
  `is_update` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '状态:0=会员未更新,1=会员已更新',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待支付,1=支付成功',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `paytime` bigint(16) NULL DEFAULT NULL COMMENT '支付时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员支付' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_premium_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_premium_order`;
CREATE TABLE `fa_service_premium_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `order_id` int(10) UNSIGNED NOT NULL COMMENT '订单id',
  `paytype` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付方式:0=微信小程序,1=微信APP,2=公众号支付,3=支付宝APP,4=余额',
  `payprice` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '补充价',
  `orderId` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `trade_no` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易单号',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待支付,1=支付成功',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `paytime` bigint(16) NULL DEFAULT NULL COMMENT '支付时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `order_id`(`order_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '补差价表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_project_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_project_config`;
CREATE TABLE `fa_service_project_config`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `service_mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客服电话',
  `cooperate_mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '合作电话',
  `user_notice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户端小广播',
  `skill_notice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务端小广播',
  `shop_notice` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家端小广播',
  `refund_notice` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '退款注意事项',
  `head_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '默认头像',
  `user_privacy_info_id` int(10) NULL DEFAULT NULL COMMENT '用户端隐私政策',
  `user_agreement_info_id` int(10) NULL DEFAULT NULL COMMENT '用户端用户协议',
  `service_privacy_info_id` int(10) NULL DEFAULT NULL COMMENT '服务端隐私政策',
  `service_agreement_info_id` int(10) NULL DEFAULT NULL COMMENT '服务端用户协议',
  `shop_privacy_info_id` int(10) NULL DEFAULT NULL COMMENT '商家端隐私政策',
  `shop_agreement_info_id` int(10) NULL DEFAULT NULL COMMENT '商家端用户协议',
  `plus_info_id` int(10) NULL DEFAULT NULL COMMENT '充值会员须知',
  `service_ensure_info_id` int(10) NULL DEFAULT NULL COMMENT '服务端保证金',
  `shop_ensure_info_id` int(10) NULL DEFAULT NULL COMMENT '商家端保证金',
  `skill_equity_info_id` int(10) NULL DEFAULT NULL COMMENT '服务者保证金权益',
  `shop_equity_info_id` int(10) NULL DEFAULT NULL COMMENT '商户端保证金权益',
  `about_info_id` int(10) NULL DEFAULT NULL COMMENT '关于我们',
  `order_info_id` int(10) NULL DEFAULT NULL COMMENT '下单须知',
  `recharge_info_id` int(10) NULL DEFAULT NULL COMMENT '充值须知',
  `withdraw_info_id` int(10) NULL DEFAULT NULL COMMENT '提现须知',
  `exchange_info_id` int(10) NULL DEFAULT NULL COMMENT '兑换须知',
  `settle_day` int(5) UNSIGNED NOT NULL DEFAULT 1 COMMENT '平台订单结算日期',
  `cancel_minute` int(10) UNSIGNED NOT NULL COMMENT '超时未支付取消',
  `comment_day` tinyint(1) UNSIGNED NOT NULL COMMENT '超时未评价',
  `withdrawtype` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提现方式:0=月,1=周',
  `accept_nums` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '默认服务者接单数',
  `shop_accept_nums` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '默认商户可接单数',
  `service_nums` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '默认商家服务者数量',
  `day` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '具体提现日期',
  `user_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户端logo',
  `user_name` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户端名称',
  `user_intro` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户端描述',
  `skill_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务端logo',
  `skill_name` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务端名称',
  `skill_intro` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务端描述',
  `shop_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户端logo',
  `shop_name` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户端名称',
  `shop_intro` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户端简介',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=取消,1=使用',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `state`(`state`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_project_configure
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_project_configure`;
CREATE TABLE `fa_service_project_configure`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `userappid` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户端appid',
  `usersecret` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户端secret',
  `skillappid` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务端appid',
  `skillsecret` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务端secret',
  `shopappid` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户端appid',
  `shopsecret` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商户端secret',
  `gaodekey` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '高德key',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=暂停中,1=使用中',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `state`(`state`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '项目配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_rebate
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_rebate`;
CREATE TABLE `fa_service_rebate`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `from_user_id` int(10) UNSIGNED NOT NULL COMMENT '来源用户id',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型:0=服务者佣金,1=商户商户佣金',
  `order_id` int(10) NULL DEFAULT NULL COMMENT '订单id',
  `num` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '返佣金额',
  `rebatetype` tinyint(1) NOT NULL DEFAULT 0 COMMENT '佣金来源:0=平台,1=门店,2=车费',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待结算,1=已结算',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `order_id`(`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '返佣记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_recharge
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_recharge`;
CREATE TABLE `fa_service_recharge`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '充值金额',
  `orderId` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `trade_no` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易单号',
  `paytype` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付类型:0=微信小程序,1=微信APP,2=公众号支付,3=支付宝支付',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待支付,1=支付成功',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `paytime` bigint(16) NULL DEFAULT NULL COMMENT '支付时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `service_coupon_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '赠送服务优惠券',
  `wanlshop_coupon_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '赠送商城优惠券',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 448 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '充值表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_refund_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_refund_order`;
CREATE TABLE `fa_service_refund_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `order_id` int(10) UNSIGNED NOT NULL COMMENT '订单id',
  `refund_reason` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '退款原因',
  `refund_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `images` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图片',
  `content` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '补充原因',
  `is_dec` tinyint(1) NOT NULL DEFAULT 0 COMMENT '扣除保证金:0=否,1=是',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待审核,1=审核通过,-1=审核拒绝,-2=已取消',
  `note` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 49 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '订单退款表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_refund_reason
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_refund_reason`;
CREATE TABLE `fa_service_refund_reason`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(80) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '原因',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '退款原因表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_rule
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_rule`;
CREATE TABLE `fa_service_rule`  (
  `id` int(11) NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `switch` tinyint(1) NULL DEFAULT 1,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_score_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_score_log`;
CREATE TABLE `fa_service_score_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `score` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更分数',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `before` int(10) NOT NULL DEFAULT 0 COMMENT '变更前分数',
  `after` int(10) NOT NULL DEFAULT 0 COMMENT '变更后分数',
  `e_id` int(11) NULL DEFAULT NULL COMMENT '事件ID',
  `type` tinyint(1) NULL DEFAULT NULL COMMENT '分数类型:1=准时,2=拒单,3=评价,4=工装,5=其他',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 158 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服务人员分数日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_search_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_search_log`;
CREATE TABLE `fa_service_search_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '搜索内容',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21757 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '搜索日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_shensu
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_shensu`;
CREATE TABLE `fa_service_shensu`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NULL DEFAULT NULL COMMENT '订单ID',
  `user_id` int(11) NOT NULL COMMENT '服务用户ID',
  `shensu_reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申诉原因',
  `shensu_text` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申诉内容',
  `shensu_images` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申诉图片',
  `status` enum('1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '状态:1=待回复,2=已回复',
  `hf_user_id` int(11) NULL DEFAULT NULL COMMENT '回复人',
  `hf_text` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '回复内容',
  `hf_time` int(11) NULL DEFAULT NULL COMMENT '回复时间',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 23 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '申诉记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_shop
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_shop`;
CREATE TABLE `fa_service_shop`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '商家用户id',
  `to_shop` tinyint(1) NOT NULL DEFAULT 0 COMMENT '开启到店:0=否,1=是',
  `ensure_id` int(10) NULL DEFAULT NULL COMMENT '保证金id',
  `ensure_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '保证金',
  `ensure_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保证金等级名称',
  `accept_nums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '每日可接单数量',
  `service_nums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '服务人员数量',
  `already_service_nums` int(10) NOT NULL DEFAULT 0 COMMENT '已拥有人员数量',
  `name` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家名称',
  `abbr` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商户缩写',
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类型:0=企业,1=个体工商户',
  `code` int(10) NOT NULL COMMENT '商家码',
  `percent` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单分成',
  `credit_code` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '统一社会信用代码',
  `license_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '营业执照',
  `username` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '法人姓名',
  `mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '法人手机号',
  `idcard` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号',
  `front_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证正面',
  `opposite_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证反面',
  `intro` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商家简介',
  `logo_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'logo',
  `category_ids` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经营分类',
  `goods_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务项目',
  `leader_name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '负责人姓名',
  `leader_mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '负责人手机号',
  `withdrawtype` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提现方式:0=月,1=周',
  `day` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '具体提现日期',
  `settle_day` int(5) UNSIGNED NOT NULL DEFAULT 1 COMMENT '订单结算日期',
  `trade_hour` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '营业时间',
  `province` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '市',
  `district` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '县',
  `address` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `lng` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经度',
  `lat` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '纬度',
  `is_train` tinyint(1) NOT NULL DEFAULT 0 COMMENT '岗前培训:0=未培训,1=已培训',
  `rebate_price` float(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '总佣金收入',
  `salenums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '服务数量',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `state` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `name`(`name`) USING BTREE,
  INDEX `weigh`(`weigh`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_shop_ensure
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_shop_ensure`;
CREATE TABLE `fa_service_shop_ensure`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '保证金金额',
  `accept_nums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '日接单数量',
  `service_nums` int(10) NOT NULL DEFAULT 0 COMMENT '服务人员数量',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家服务金' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_shop_ensure_pay
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_shop_ensure_pay`;
CREATE TABLE `fa_service_shop_ensure_pay`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `orderId` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '订单编号',
  `trade_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易单号',
  `paytype` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付类型:0=微信小程序,1=微信APP,2=公众号支付,3=支付宝支付',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待支付,1=支付成功',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `paytime` bigint(16) NULL DEFAULT NULL COMMENT '支付时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id`(`user_id`, `createtime`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商家保证金支付表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_shop_money_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_shop_money_log`;
CREATE TABLE `fa_service_shop_money_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更余额',
  `order_id` int(10) NULL DEFAULT NULL COMMENT '订单id',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服务者商户余额记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_shop_user_money_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_shop_user_money_log`;
CREATE TABLE `fa_service_shop_user_money_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更金额	',
  `order_id` int(10) NULL DEFAULT NULL COMMENT '订单id',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户平台余额记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_skill
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_skill`;
CREATE TABLE `fa_service_skill`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `shop_id` int(10) NULL DEFAULT NULL COMMENT '商家id',
  `code` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务工号',
  `mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `ensure_id` int(10) NULL DEFAULT NULL COMMENT '保证金id',
  `ensure_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '保证金',
  `ensure_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '保证金等级名称',
  `accept_nums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '每日可接单数量',
  `credit_score` tinyint(3) UNSIGNED NOT NULL DEFAULT 100 COMMENT '信用分',
  `percent` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单分成%',
  `name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '姓名',
  `sex` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '性别:1=男,0=女',
  `idcard` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证号',
  `front_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证正面',
  `opposite_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '身份证反面',
  `user_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '实拍照',
  `certificate_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '从业资格证',
  `health_image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '健康证',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '项目分类id',
  `skill_cate_id` int(10) UNSIGNED NOT NULL COMMENT '职业技能分类id',
  `goods_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '服务项目',
  `image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '工装照',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '生活照',
  `edu` tinyint(1) UNSIGNED NOT NULL COMMENT '学历:0=小学文化,1=初中,2=高中,3=大专,4=本科,5=硕士,6=博士',
  `age` tinyint(1) UNSIGNED NOT NULL COMMENT '年龄',
  `nation` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '民族',
  `height` tinyint(1) NULL DEFAULT NULL COMMENT '身高/cm',
  `weight` tinyint(1) NULL DEFAULT NULL COMMENT '体重/kg',
  `exper` tinyint(1) NOT NULL DEFAULT 0 COMMENT '经验:0=1年以下,1=1-3年,2=3-5年,3=5年以上',
  `province` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '省',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '市',
  `district` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '县',
  `address` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '详细地址',
  `lng` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经度',
  `lat` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '纬度',
  `is_rest` tinyint(1) NOT NULL DEFAULT 0 COMMENT '工作状态:0=接单中,1=休息中',
  `is_train` tinyint(1) NOT NULL DEFAULT 0 COMMENT '岗前培训:0=未培训,1=已培训',
  `rebate_price` float(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '总佣金收入',
  `salenums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '服务数量',
  `state` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=显示,2=冻结',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  `hx_range` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '核心服务区域',
  `zd_range` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '重点服务区域',
  `qt_range` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '其他服务区域',
  `xy_sign` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '协议签署内容',
  `health_sc_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '手持健康证照片',
  `health_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '健康证持卡人',
  `health_card` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '健康证号',
  `health_end` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '健康证结束日期',
  `service_score` int(11) NULL DEFAULT 100 COMMENT '服务分数',
  `user_image_time` bigint(16) NULL DEFAULT NULL COMMENT '头像时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `name`(`name`) USING BTREE,
  INDEX `shop_id`(`shop_id`) USING BTREE,
  INDEX `weigh`(`weigh`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 151 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服务人员信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_skill_cate
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_skill_cate`;
CREATE TABLE `fa_service_skill_cate`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `pid` int(11) NULL DEFAULT NULL COMMENT '上级ID',
  `service_skil_cate_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '所属证书',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缩略图',
  `state` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` int(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 74 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '技师分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_skill_ensure
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_skill_ensure`;
CREATE TABLE `fa_service_skill_ensure`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '保证金金额',
  `accept_nums` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '日接单数量',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商户保证金' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_skill_ensure_pay
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_skill_ensure_pay`;
CREATE TABLE `fa_service_skill_ensure_pay`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '服务者用户id',
  `price` decimal(8, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `orderId` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '订单编号',
  `trade_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '交易单号',
  `paytype` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付类型:0=微信小程序,1=微信APP,2=公众号支付,3=支付宝支付',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待支付,1=支付成功',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `paytime` bigint(16) NULL DEFAULT NULL COMMENT '支付时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id`(`user_id`, `createtime`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 146 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服务者保证金支付' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_skill_time
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_skill_time`;
CREATE TABLE `fa_service_skill_time`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `skill_id` int(10) UNSIGNED NOT NULL COMMENT '服务人员id',
  `buy_id` int(10) NULL DEFAULT NULL COMMENT '下单用户id',
  `timetype` tinyint(1) NOT NULL DEFAULT 0 COMMENT '时间类型:0=今天,1=明天,2=后天',
  `number` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '时间序号',
  `starttime` bigint(16) NOT NULL COMMENT '创建时间',
  `endtime` bigint(16) NOT NULL COMMENT '更新时间',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=空闲,1=已预约,2=不可预约	',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `changetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `skill_id`(`skill_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 22465 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '技师时间表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_thumb
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_thumb`;
CREATE TABLE `fa_service_thumb`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '图片',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '跳转类型:0=不跳转,1=项目详情,2=文本协议,3=其他路由',
  `goods_id` int(10) NULL DEFAULT NULL COMMENT '项目id',
  `config_text_id` int(10) NULL DEFAULT NULL COMMENT '文本协议id',
  `jump_id` int(10) NULL DEFAULT NULL COMMENT '路由链接',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '轮播图表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_total_data
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_total_data`;
CREATE TABLE `fa_service_total_data`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `total_order_nums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '总订单',
  `total_user_nums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '总用户',
  `total_plus_nums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '总会员',
  `total_skill_nums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '总服务人员',
  `total_shop_nums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '总商户',
  `total_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '总金额',
  `rebate_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '总佣金',
  `recharge_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '总充值',
  `withdraw_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '提现总额',
  `skill_ensure_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '服务人员保证金',
  `shop_ensure_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商户保证金',
  `day_order_nums` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '日服务订单数量',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '数据统计表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_train
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_train`;
CREATE TABLE `fa_service_train`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `title` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '标题',
  `desc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '介绍',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型:0=服务端,1=商户端',
  `image` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '封面图',
  `videofile` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '视频',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `state` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '培训表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_use_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_use_log`;
CREATE TABLE `fa_service_use_log`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `order_id` int(11) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `use_num` int(11) NULL DEFAULT NULL,
  `residue_num` int(11) NULL DEFAULT NULL,
  `createtime` bigint(16) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 168 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_user_bfw
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_user_bfw`;
CREATE TABLE `fa_service_user_bfw`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '姓名',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `sex` tinyint(1) NULL DEFAULT 1 COMMENT '性别:1=男,2=女',
  `gx` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '与服务者关系',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `mobile`(`mobile`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 17 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '被服务人员信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_user_coupon
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_user_coupon`;
CREATE TABLE `fa_service_user_coupon`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `coupon_id` int(10) UNSIGNED NOT NULL COMMENT '优惠券id',
  `type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '优惠券类型:0=平台通用券,1=平台项目券,2=商户通用券,3=商户项目券,4=口令券,5=分享券',
  `goods_id` int(10) NULL DEFAULT NULL COMMENT '项目id',
  `shop_id` int(10) NULL DEFAULT NULL COMMENT '商户id',
  `code` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '口令',
  `achieve` int(10) UNSIGNED NOT NULL COMMENT '满',
  `reduce` int(10) UNSIGNED NOT NULL COMMENT '减',
  `exittime` bigint(16) UNSIGNED NOT NULL COMMENT '截止时间',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '状态:0=待使用,1=已使用,-1=已过期	',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 58 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户优惠券表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_user_info
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_user_info`;
CREATE TABLE `fa_service_user_info`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `mobile` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '手机号',
  `is_skill` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否服务人员:0=否,1=是',
  `is_shop` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '是否商家:0=否,1=是',
  `unionid` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联合id',
  `user_openid` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户openid',
  `skill_openid` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '服务openid',
  `shop_openid` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '商家openid',
  `alipay_name` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付宝姓名',
  `alipay_account` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '支付宝账号',
  `bank_name` varchar(122) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户行',
  `bank_user` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '开户人',
  `bank_card` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '银行卡号',
  `money` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '服务者平台可提现余额',
  `shop_money` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商户可提现余额',
  `shop_user_money` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商户平台可提现余额',
  `is_plus` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员:0=否,1=是',
  `is_update` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否更新:0=否,1=是',
  `discount` tinyint(1) UNSIGNED NOT NULL DEFAULT 100 COMMENT '享受折扣%',
  `plusname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '会员名称',
  `plustime` bigint(16) NULL DEFAULT NULL COMMENT '会员有效期',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE,
  INDEX `mobile`(`mobile`) USING BTREE,
  INDEX `is_skill`(`is_skill`) USING BTREE,
  INDEX `is_shop`(`is_shop`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1846 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_user_money_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_user_money_log`;
CREATE TABLE `fa_service_user_money_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户id',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更金额',
  `order_id` int(10) NULL DEFAULT NULL COMMENT '订单id',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '服务者平台余额明细表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_service_user_rule
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_user_rule`;
CREATE TABLE `fa_service_user_rule`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL,
  `qydl_id` int(10) NULL DEFAULT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `job` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `rule_ids` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_user_score
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_user_score`;
CREATE TABLE `fa_service_user_score`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) NULL DEFAULT NULL COMMENT '用户id',
  `type` tinyint(1) NULL DEFAULT NULL COMMENT '分数类型:1=准时,2=拒单,3=评价,4=工装',
  `score` int(10) NULL DEFAULT NULL COMMENT '分数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_user_skill
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_user_skill`;
CREATE TABLE `fa_service_user_skill`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL,
  `skill_ids` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  `createtime` bigint(16) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户技能' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_service_withdraw
-- ----------------------------
DROP TABLE IF EXISTS `fa_service_withdraw`;
CREATE TABLE `fa_service_withdraw`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'id',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '提现用户id',
  `type` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '金额来源:0=服务者平台余额提现,1=服务者商户余额提现,2=商户平台余额提现,3=服务者保证金提现,4=商户保证金提现',
  `shop_id` int(10) NULL DEFAULT NULL COMMENT '商户id',
  `withdrawtype` tinyint(1) NOT NULL DEFAULT 0 COMMENT '提现方式:0=支付宝,1=银行卡',
  `withdraw_info` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '提现信息',
  `num` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '提现金额',
  `images` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '转账证明',
  `state` tinyint(1) NOT NULL DEFAULT 0 COMMENT '提现状态:-1=已拒绝,0=待审核,1=已审核,2=已转账',
  `note` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '拒绝原因',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id`(`user_id`, `createtime`) USING BTREE,
  INDEX `shop_id`(`shop_id`) USING BTREE,
  INDEX `user_index`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '提现' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_share_course_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_share_course_goods`;
CREATE TABLE `fa_share_course_goods`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NULL DEFAULT NULL COMMENT '商品',
  `floor_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最大减价',
  `people_num` int(11) NULL DEFAULT NULL COMMENT '人数',
  `valid_time` int(10) NULL DEFAULT NULL COMMENT '有效时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `switch` tinyint(1) NULL DEFAULT 1 COMMENT '状态:1=上架,0=下架',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分享商品' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_share_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_share_goods`;
CREATE TABLE `fa_share_goods`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NULL DEFAULT NULL COMMENT '商品',
  `floor_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '总金额',
  `min` decimal(10, 2) NULL DEFAULT NULL COMMENT '最小金额',
  `max` decimal(10, 2) NULL DEFAULT NULL COMMENT '最大金额',
  `people_num` int(11) NULL DEFAULT NULL COMMENT '人数',
  `valid_time` int(10) NULL DEFAULT NULL COMMENT '有效时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `switch` tinyint(1) NULL DEFAULT 1 COMMENT '状态:1=上架,0=下架',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分享商品' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_share_goods_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_share_goods_log`;
CREATE TABLE `fa_share_goods_log`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `share_id` int(11) NULL DEFAULT NULL,
  `goods_id` int(11) NULL DEFAULT NULL,
  `user_id` int(11) NULL DEFAULT NULL,
  `reduce` decimal(10, 2) NULL DEFAULT NULL,
  `goods_type` tinyint(1) NULL DEFAULT 1 COMMENT '1=商城,2=服务,3=课程',
  `createtime` bigint(16) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_share_goods_money
-- ----------------------------
DROP TABLE IF EXISTS `fa_share_goods_money`;
CREATE TABLE `fa_share_goods_money`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `share_id` int(11) NULL DEFAULT NULL,
  `money` decimal(10, 2) NULL DEFAULT NULL,
  `goods_type` tinyint(1) NULL DEFAULT 1,
  `createtime` bigint(16) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1121 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_share_goods_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_share_goods_order`;
CREATE TABLE `fa_share_goods_order`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL,
  `goods_id` int(11) NULL DEFAULT NULL,
  `end_time` bigint(16) NULL DEFAULT NULL,
  `reduce` decimal(10, 2) NULL DEFAULT 0.00,
  `createtime` bigint(16) NULL DEFAULT NULL,
  `status` tinyint(1) NULL DEFAULT 1 COMMENT '1=未完成，2=已完成，3=已使用',
  `join_people` int(10) NULL DEFAULT 0,
  `people_num` int(10) NULL DEFAULT 0,
  `order_id` int(11) NULL DEFAULT NULL,
  `goods_type` tinyint(1) NULL DEFAULT 1 COMMENT '1=商城2=服务3=课程',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 71 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_share_service_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_share_service_goods`;
CREATE TABLE `fa_share_service_goods`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NULL DEFAULT NULL COMMENT '商品',
  `floor_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '最大减价',
  `people_num` int(11) NULL DEFAULT NULL COMMENT '人数',
  `valid_time` int(10) NULL DEFAULT NULL COMMENT '有效时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `switch` tinyint(1) NULL DEFAULT 1 COMMENT '状态:1=上架,0=下架',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分享商品' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shared_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_shared_goods`;
CREATE TABLE `fa_shared_goods`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `goods_id` int(11) NULL DEFAULT NULL COMMENT '商品',
  `money` decimal(10, 2) NULL DEFAULT NULL COMMENT '立减金额',
  `user_num` int(10) NULL DEFAULT NULL COMMENT '用户数',
  `switch` tinyint(1) NULL DEFAULT NULL COMMENT '状态1=上架,0=下架',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_shop_module
-- ----------------------------
DROP TABLE IF EXISTS `fa_shop_module`;
CREATE TABLE `fa_shop_module`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `goods_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '商品',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '模块名称',
  `status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=显示',
  `createtime` bigint(16) NULL DEFAULT NULL,
  `updatetime` bigint(16) NULL DEFAULT NULL,
  `deletetime` bigint(16) NULL DEFAULT NULL,
  `type` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '类型:0=横版,1=竖版',
  `weigh` int(11) NULL DEFAULT 1 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商城首页模块' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_sms
-- ----------------------------
DROP TABLE IF EXISTS `fa_sms`;
CREATE TABLE `fa_sms`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `event` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '事件',
  `mobile` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号',
  `code` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证码',
  `times` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '验证次数',
  `ip` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'IP',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 954 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '短信验证码表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_test
-- ----------------------------
DROP TABLE IF EXISTS `fa_test`;
CREATE TABLE `fa_test`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NULL DEFAULT 0 COMMENT '会员ID',
  `admin_id` int(10) NULL DEFAULT 0 COMMENT '管理员ID',
  `category_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '分类ID(单选)',
  `category_ids` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类ID(多选)',
  `tags` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '标签',
  `week` enum('monday','tuesday','wednesday') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '星期(单选):monday=星期一,tuesday=星期二,wednesday=星期三',
  `flag` set('hot','index','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '标志(多选):hot=热门,index=首页,recommend=推荐',
  `genderdata` enum('male','female') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'male' COMMENT '性别(单选):male=男,female=女',
  `hobbydata` set('music','reading','swimming') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '爱好(多选):music=音乐,reading=读书,swimming=游泳',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图片',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '图片组',
  `attachfile` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '附件',
  `keywords` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '关键字',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '描述',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '省市',
  `array` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '数组:value=值',
  `json` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '配置:key=名称,value=值',
  `multiplejson` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '二维数组:title=标题,intro=介绍,author=作者,age=年龄',
  `price` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '价格',
  `views` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '点击',
  `workrange` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '时间区间',
  `startdate` date NULL DEFAULT NULL COMMENT '开始日期',
  `activitytime` datetime NULL DEFAULT NULL COMMENT '活动时间(datetime)',
  `year` year NULL DEFAULT NULL COMMENT '年',
  `times` time NULL DEFAULT NULL COMMENT '时间',
  `refreshtime` bigint(16) NULL DEFAULT NULL COMMENT '刷新时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  `weigh` int(10) NULL DEFAULT 0 COMMENT '权重',
  `switch` tinyint(1) NULL DEFAULT 0 COMMENT '开关',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'normal' COMMENT '状态',
  `state` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '状态值:0=禁用,1=正常,2=推荐',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '测试表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_user
-- ----------------------------
DROP TABLE IF EXISTS `fa_user`;
CREATE TABLE `fa_user`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组别ID',
  `username` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户名',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '昵称',
  `password` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
  `salt` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码盐',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '电子邮箱',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '等级',
  `gender` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '性别',
  `birthday` date NOT NULL COMMENT '生日',
  `bio` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '格言',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '余额',
  `score` int(10) NOT NULL DEFAULT 0 COMMENT '积分',
  `successions` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '连续登录天数',
  `maxsuccessions` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '最大连续登录天数',
  `prevtime` bigint(16) NULL DEFAULT NULL COMMENT '上次登录时间',
  `logintime` bigint(16) NULL DEFAULT NULL COMMENT '登录时间',
  `loginip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录IP',
  `loginfailure` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '失败次数',
  `joinip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '加入IP',
  `jointime` bigint(16) NULL DEFAULT NULL COMMENT '加入时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `token` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'Token',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '状态',
  `verification` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证',
  `wechat` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '微信号',
  `bm_num` int(11) NULL DEFAULT 0 COMMENT '解锁保姆数量',
  `invit_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '邀请码',
  `parent_id` int(11) NULL DEFAULT 0 COMMENT '上级ID',
  `is_order` tinyint(1) NULL DEFAULT 0 COMMENT '是否下过单，1=下过',
  `is_sqdl` tinyint(1) NULL DEFAULT 0 COMMENT '社区代理:0=否,1=是',
  `is_qydl` tinyint(1) NULL DEFAULT 0 COMMENT '区域代理:0=否,1=是',
  `last_active` int(11) NULL DEFAULT 0 COMMENT '最近一次在线时间',
  `today_active` int(11) NULL DEFAULT 0 COMMENT '今日第一次在线时间',
  `province` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '市',
  `district` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '县',
  `lng` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '经度',
  `lat` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '纬度',
  `hidden_time` int(11) NULL DEFAULT 0 COMMENT '禁用时间',
  `is_shop` tinyint(1) NULL DEFAULT 0 COMMENT '提交店铺:0=否,1=是',
  `qy_mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '区域客服电话',
  `qrcode` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '小程序分享码',
  `card_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '身份证号',
  `realname` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '真实姓名',
  `ylgw_number_no` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '养老院长编号',
  `addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `skill_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '',
  `qydl_staff` int(10) NULL DEFAULT 0 COMMENT '区域代理id',
  `is_service` tinyint(1) NULL DEFAULT 0,
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '服务者的注销时间',
  `ylgw_xy` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '养老院长协议',
  `ylgw_zs` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '证书',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `email`(`email`) USING BTREE,
  INDEX `mobile`(`mobile`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1940 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_user_area
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_area`;
CREATE TABLE `fa_user_area`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '区域用户ID',
  `province` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '省',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '市',
  `district` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '县',
  `lng` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '经度',
  `lat` char(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '纬度',
  `mobile` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '咨询电话',
  `addr` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区域',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 40 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '区域代理配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_user_area_price
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_area_price`;
CREATE TABLE `fa_user_area_price`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '区域用户ID',
  `goods_id` int(11) NULL DEFAULT NULL COMMENT '商品ID',
  `sku_id` int(11) NULL DEFAULT NULL COMMENT '规格ID',
  `user_area_id` int(11) NULL DEFAULT NULL COMMENT '区域ID',
  `city` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '改价城市',
  `old_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '原价',
  `price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '价格',
  `status` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态:0=审核中,1=审核通过,2=审核驳回',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '调价记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_user_group
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_group`;
CREATE TABLE `fa_user_group`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '组名',
  `rules` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '权限节点',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '添加时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员组表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_user_menu
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_menu`;
CREATE TABLE `fa_user_menu`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '图标',
  `type` enum('0','1','2','') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '类型:0=更多工具,1=社区代理,2=区域代理',
  `weigh` int(11) NULL DEFAULT 1 COMMENT '权重',
  `herf` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '跳转链接',
  `status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=显示',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '个人中心菜单配置' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_user_money_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_money_log`;
CREATE TABLE `fa_user_money_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更余额',
  `before` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更前余额',
  `after` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '变更后余额',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `type` enum('pay','groups','groups_refund','recharge','withdraw','refund','sys','fenyong','ins') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'sys' COMMENT '业务类型:pay=商品交易,groups=拼团,groups_refund=拼团退款,recharge=充值,withdraw=提现,refund=退款,sys=系统业务,fenyong=分佣',
  `service_type` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '类型:0=商城,1=课程,2=家政',
  `service_ids` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '业务ID',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1558 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员余额变动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_user_rule
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_rule`;
CREATE TABLE `fa_user_rule`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(10) NULL DEFAULT NULL COMMENT '父ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '标题',
  `remark` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `ismenu` tinyint(1) NULL DEFAULT NULL COMMENT '是否菜单',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NULL DEFAULT 0 COMMENT '权重',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 13 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员规则表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_user_score_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_score_log`;
CREATE TABLE `fa_user_score_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `score` int(10) NOT NULL DEFAULT 0 COMMENT '变更积分',
  `before` int(10) NOT NULL DEFAULT 0 COMMENT '变更前积分',
  `after` int(10) NOT NULL DEFAULT 0 COMMENT '变更后积分',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员积分变动表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_user_token
-- ----------------------------
DROP TABLE IF EXISTS `fa_user_token`;
CREATE TABLE `fa_user_token`  (
  `token` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'Token',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `expiretime` bigint(16) NULL DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`token`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '会员Token表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_version
-- ----------------------------
DROP TABLE IF EXISTS `fa_version`;
CREATE TABLE `fa_version`  (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `oldversion` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '旧版本号',
  `newversion` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '新版本号',
  `packagesize` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '包大小',
  `content` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '升级内容',
  `downloadurl` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '下载地址',
  `enforce` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '强制更新',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '版本表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_address
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_address`;
CREATE TABLE `fa_wanlshop_address`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `name` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '收货人',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `default` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '默认:1=是,0=否',
  `country` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '国家',
  `province` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '省份',
  `city` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市',
  `citycode` int(6) NOT NULL COMMENT '市级代码',
  `district` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '区/县',
  `adcode` int(6) NOT NULL COMMENT '县级代码',
  `formatted_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地区',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `address_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地址名称',
  `location` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '经纬度',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `sex` tinyint(4) NULL DEFAULT 1 COMMENT '性别:1=男,2=女',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2258 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_advert
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_advert`;
CREATE TABLE `fa_wanlshop_advert`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` int(10) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `module` enum('open','page','category','first','other') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '投放模块:open=开屏广告,page=自定义页面广告,category=类目广告,first=首次欢迎广告,other=其他广告',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '投放类目',
  `type` enum('banner','image','video') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '媒体类型:banner=幻灯片,image=图片,video=视频',
  `media` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '媒体文件',
  `url` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '广告链接',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '广告标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '广告内容',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '投放城市',
  `startdate` date NULL DEFAULT NULL COMMENT '开始日期',
  `enddate` date NULL DEFAULT NULL COMMENT '结束日期',
  `views` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击次数',
  `show` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '展示次数',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '广告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_article
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_article`;
CREATE TABLE `fa_wanlshop_article`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` int(10) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID(单选)',
  `flag` set('hot','index','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标志(多选):hot=热门,index=首页,recommend=推荐',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片组',
  `attachfile` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '附件',
  `keywords` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '关键字',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '描述',
  `views` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '文章表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_auth
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_auth`;
CREATE TABLE `fa_wanlshop_auth`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '会员ID',
  `state` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '店铺类型:0=个人,1=企业,2=旗舰',
  `shopname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '店铺名称',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '企业名/姓名',
  `number` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '统一信用/身份证号',
  `mobile` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '证件图片',
  `trademark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '商标证书',
  `ruzhu_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '入驻协议图片',
  `wechat` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '微信号',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '店铺头像',
  `bio` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '店铺简介',
  `refuse` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '拒绝理由',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '店铺介绍',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '省市',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '详细地址',
  `verify` enum('0','1','2','3','4') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '审核:0=提交资质,1=提交店铺,2=提交审核,3=通过,4=未通过',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创店时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopname`(`shopname`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 88 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '认证表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_brand
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_brand`;
CREATE TABLE `fa_wanlshop_brand`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` int(10) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '商家ID',
  `category_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '品牌名',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容介绍',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `switch` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  `state` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '状态值:0=审核中,1=已审核',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '品牌表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_captcha_image
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_captcha_image`;
CREATE TABLE `fa_wanlshop_captcha_image`  (
  `id` int(20) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `file` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '验证码图片',
  `times` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '使用次数',
  `num` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '成功次数',
  `createtime` bigint(16) NULL DEFAULT NULL,
  `md5` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '验证码原图' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_captcha_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_captcha_log`;
CREATE TABLE `fa_wanlshop_captcha_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `captcha_id` int(10) NULL DEFAULT 0 COMMENT '验证码图片ID',
  `angle` int(3) UNSIGNED NULL DEFAULT 0 COMMENT '旋转角度',
  `ip` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '添加ip',
  `times` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '错误次数',
  `updatetime` bigint(10) NULL DEFAULT NULL COMMENT '验证成功日期',
  `succeedtime` decimal(6, 3) UNSIGNED NULL DEFAULT 0.000 COMMENT '验证成功所用时间',
  `createtime` bigint(10) NULL DEFAULT NULL COMMENT '添加日期',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '验证码生成日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_cart
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_cart`;
CREATE TABLE `fa_wanlshop_cart`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `goods_id` int(10) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `number` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '购物车数量',
  `sku_id` int(10) NOT NULL DEFAULT 0 COMMENT 'SKU ID',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `is_service` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 428 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '购物车表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_category
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_category`;
CREATE TABLE `fa_wanlshop_category`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父ID',
  `type` enum('article','goods','station') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '类型',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
  `name_spacer` varchar(0) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '存放目录结构',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片',
  `flag` set('hot','new','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '标志',
  `isnav` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否导航显示',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `weigh`(`weigh`) USING BTREE,
  INDEX `parent_id`(`pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 220 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品、文章类目表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_category_attribute
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_category_attribute`;
CREATE TABLE `fa_wanlshop_category_attribute`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `admin_id` int(10) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分类ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '属性名',
  `value` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '配置:key=名称,value=值2',
  `switch` tinyint(1) NOT NULL DEFAULT 0 COMMENT '开关',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '类目属性表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_chat
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_chat`;
CREATE TABLE `fa_wanlshop_chat`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `form_uid` int(10) NULL DEFAULT 0 COMMENT '发信息人ID',
  `to_id` int(10) NULL DEFAULT 0 COMMENT '收信息人ID',
  `form` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '发送人信息',
  `message` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '消息数据',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '消息类型',
  `online` tinyint(1) NULL DEFAULT 0 COMMENT '在线状态:0=离线消息,1=在线消息',
  `isread` tinyint(1) NULL DEFAULT 0 COMMENT '是否已读:0=未读,1=已读',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '聊天记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_complaint
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_complaint`;
CREATE TABLE `fa_wanlshop_complaint`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '举报类型:0=用户举报,1=商品举报,2=店铺举报',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '举报人',
  `complaint_user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '被举报会员ID',
  `complaint_shop_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '被举报店铺ID',
  `complaint_goods_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '被举报商品ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片组',
  `reason` enum('0','1','2','3','4','5','6','7','8','9','10','11','12','13') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '举报理由:0=虚假交易,1=诈骗欺诈,2=收到空包裹,3=假冒盗版,4=泄露信息,5=违禁物品,6=未按成交价交易,7=未按约定时间发货,8=垃圾营销,9=涉黄信息,10=不实信息,11=人身攻击,12=违法信息,13=诈骗信息',
  `receipt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '处理回执',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `state` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '状态:normal=未受理,hidden=已受理',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '举报表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_coupon
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_coupon`;
CREATE TABLE `fa_wanlshop_coupon`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `type` enum('reduction','discount','shipping','vip') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'reduction' COMMENT '优惠券类型:reduction=满减券,discount=折扣券,shipping=包邮券,vip=会员赠券',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
  `userlevel` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '会员等级',
  `usertype` enum('reduction','discount') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'reduction' COMMENT '赠券类型:reduction=满减券,discount=折扣券',
  `price` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '面值',
  `discount` decimal(2, 1) UNSIGNED NULL DEFAULT 0.0 COMMENT '折扣率',
  `limit` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '消费限制',
  `rangetype` enum('all','goods','category') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'all' COMMENT '可用范围:all=全部商品,goods=指定商品,category=指定分类',
  `range` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '范围',
  `pretype` enum('appoint','fixed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'appoint' COMMENT '时效类型:appoint=领后天数,fixed=固定时间',
  `validity` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '有效天数',
  `startdate` date NULL DEFAULT NULL COMMENT '开始日期',
  `enddate` date NULL DEFAULT NULL COMMENT '结束时间',
  `drawlimit` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '领取限制',
  `grant` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '-1' COMMENT '发放总数量',
  `alreadygrant` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '已领取数量',
  `surplus` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '剩余数量',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '使用说明',
  `usenum` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '已使用数量',
  `invalid` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '是否失效',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `is_service` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 15 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '卡券表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_coupon_receive
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_coupon_receive`;
CREATE TABLE `fa_wanlshop_coupon_receive`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `coupon_id` int(10) NOT NULL DEFAULT 0 COMMENT '原始优惠券ID',
  `coupon_no` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '优惠券编码',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `order_id` int(10) NULL DEFAULT 0 COMMENT '使用订单ID',
  `type` enum('reduction','discount','shipping','vip') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'reduction' COMMENT '优惠券类型:reduction=满减券,discount=折扣券,shipping=包邮券,vip=会员赠券',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
  `userlevel` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '会员等级',
  `usertype` enum('reduction','discount') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'reduction' COMMENT '赠券类型:reduction=满减券,discount=折扣券',
  `price` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '面值',
  `discount` decimal(2, 1) UNSIGNED NULL DEFAULT 0.0 COMMENT '折扣率',
  `limit` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '消费限制',
  `rangetype` enum('all','goods','category') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'all' COMMENT '可用范围:all=全部商品,goods=指定商品,category=指定分类',
  `range` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '范围',
  `pretype` enum('appoint','fixed') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'appoint' COMMENT '时效类型:appoint=领后天数,fixed=固定时间',
  `validity` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '有效天数',
  `startdate` date NULL DEFAULT NULL COMMENT '开始日期',
  `enddate` date NULL DEFAULT NULL COMMENT '活动时间(datetime)',
  `state` enum('1','2','3') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '使用状态:1=未使用,2=已使用,3=已过期',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `is_service` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1402 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '领取卡券历史表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_feedback
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_feedback`;
CREATE TABLE `fa_wanlshop_feedback`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片组',
  `contact` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '联系方式',
  `device` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '设备详情',
  `score` int(1) NOT NULL DEFAULT 0 COMMENT '评分',
  `receipt` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '处理回执',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '状态:normal=未受理,hidden=已受理',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '意见反馈表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_find
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_find`;
CREATE TABLE `fa_wanlshop_find`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL COMMENT '所属用户ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '商家ID',
  `user_no` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '账户号',
  `type` enum('new','live','video','want','show','activity') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'new' COMMENT '类型:new=上新,live=直播,video=短视频,want=种草,show=买家秀,activity=活动',
  `goods_ids` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品ID',
  `comments_id` int(10) NULL DEFAULT NULL COMMENT '评论ID',
  `activity_id` int(10) NULL DEFAULT NULL COMMENT '活动ID',
  `live_id` int(10) NULL DEFAULT NULL COMMENT '直播ID',
  `video_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '视频ID',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '图片组',
  `views` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '浏览量',
  `likes` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞',
  `state` enum('publish','examine','hazard','transcoding','screenshot','normal') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'publish' COMMENT '发现状态:publish=发布中,examine=待审核,hazard=危险内容,transcoding=转码中,screenshot=截图中,normal=发布成功',
  `comments` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '评论',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '发现动态表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_find_comments
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_find_comments`;
CREATE TABLE `fa_wanlshop_find_comments`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'PID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'UID',
  `find_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'FIND_ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `like` int(10) NULL DEFAULT 0 COMMENT '喜欢',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '关注评论表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_find_comments_like
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_find_comments_like`;
CREATE TABLE `fa_wanlshop_find_comments_like`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '用户ID',
  `comments_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '评论ID',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '评论点赞表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_find_like
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_find_like`;
CREATE TABLE `fa_wanlshop_find_like`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '用户ID',
  `find_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '店铺ID',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '点赞发现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_find_user
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_find_user`;
CREATE TABLE `fa_wanlshop_find_user`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `user_no` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '用户号',
  `follow` int(10) NULL DEFAULT 0 COMMENT '关注',
  `fans` int(10) NULL DEFAULT 0 COMMENT '粉丝',
  `likes` int(10) NULL DEFAULT 0 COMMENT '喜欢',
  `praised` int(10) NULL DEFAULT 0 COMMENT '获赞',
  `color` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#dbb29d' COMMENT '用户背景色',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '用户主题图',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 79 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '创作账户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_find_user_follow
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_find_user_follow`;
CREATE TABLE `fa_wanlshop_find_user_follow`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_no` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '账户号',
  `user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '关注用户ID',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '关注发现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_goods`;
CREATE TABLE `fa_wanlshop_goods`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '店铺ID',
  `shop_category_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '店铺内类目',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品类目',
  `brand_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '品牌ID',
  `category_attribute` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '类目属性',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品主图',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品相册',
  `videos` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品视频',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '商品描述',
  `flag` set('hot','new','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标志(多选):hot=热门,new=上新,recommend=推荐',
  `stock` enum('porder','payment') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'porder' COMMENT '库存计算方式:porder=下单减库存,payment=付款减库存',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品详情',
  `freight_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '运费模板',
  `grounding` tinyint(1) NOT NULL DEFAULT 0 COMMENT '上架状态',
  `specs` enum('single','multi') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'single' COMMENT '商品规格:single=单规格,multi=多规格',
  `distribution` enum('true','false') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'false' COMMENT '是否独立分销:true=开启,false=关闭',
  `activity` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '活动中:0=没在活动中,1=活动中',
  `activity_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动ID',
  `activity_type` enum('goods','groups','seckill','bargain') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'goods' COMMENT '活动类型:goods=商品,groups=拼团,seckill=秒杀,bargain=砍价',
  `views` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击',
  `cost_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '成本价',
  `price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '产品价格',
  `sales` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `payment` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '付款人数',
  `comment` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '评论',
  `praise` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '好评',
  `moderate` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '中评',
  `negative` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '差评',
  `like` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '收藏',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  `labels` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '标签',
  `verify` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '审核状态:0=审核中,1=审核通过,2=审核驳回',
  `shequ_bili` int(11) NULL DEFAULT 0 COMMENT '社区代理分成比例',
  `quyu_bili` int(11) NULL DEFAULT 0 COMMENT '区域代理分成比例',
  `service_bili` int(11) NULL DEFAULT 0 COMMENT '供货商分成比例',
  `is_tj` tinyint(1) NULL DEFAULT 0 COMMENT '推荐商品:1=推荐,0=否',
  `is_service` tinyint(1) NULL DEFAULT 0 COMMENT '服务商品',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `status`(`status`, `is_service`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 779 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_goods_comment
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_goods_comment`;
CREATE TABLE `fa_wanlshop_goods_comment`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用员ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `order_type` enum('goods','groups','seckill') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'goods' COMMENT '订单类型:goods=普通订单,groups=拼团订单,seckill=秒杀订单',
  `order_goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单商品ID',
  `state` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '评价:0=好评,1=中评,2=差评',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
  `tag` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '评论标签',
  `suk` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '所购买商品SUK',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片组',
  `score` float(3, 1) UNSIGNED NULL DEFAULT 0.0 COMMENT '综合评分',
  `score_describe` float(3, 1) UNSIGNED NULL DEFAULT 0.0 COMMENT '描述相符',
  `score_service` float(3, 1) UNSIGNED NULL DEFAULT 0.0 COMMENT '服务相符',
  `score_deliver` float(3, 1) UNSIGNED NULL DEFAULT 0.0 COMMENT '发货相符',
  `score_logistics` float(3, 1) UNSIGNED NULL DEFAULT 0.0 COMMENT '物流相符',
  `switch` tinyint(1) NULL DEFAULT 0 COMMENT '匿名评论',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品评论' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_goods_follow
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_goods_follow`;
CREATE TABLE `fa_wanlshop_goods_follow`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '用户ID',
  `goods_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '商品ID',
  `goods_type` enum('goods','groups','seckill','station') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'goods' COMMENT '商品类型:goods=普通商品,groups=拼团商品,seckill=秒杀商品',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 427 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '关注商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_goods_sku
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_goods_sku`;
CREATE TABLE `fa_wanlshop_goods_sku`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `thumbnail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '缩略图',
  `difference` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '规格',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商品价格',
  `market_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '市场价格',
  `cost_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '成本价',
  `stock` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品库存',
  `weigh` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '商品重量',
  `sn` char(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '商品编码',
  `sales` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `state` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '状态:0=新版数据,1=旧版数据',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16050 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品SUK表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_goods_spu
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_goods_spu`;
CREATE TABLE `fa_wanlshop_goods_spu`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `name` char(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '规格名称',
  `item` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '规格',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  `images` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '图片',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4424 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商品SPU表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_groups
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_groups`;
CREATE TABLE `fa_wanlshop_groups`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼团号',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '团长ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '所属商家',
  `goods_id` int(10) NOT NULL DEFAULT 0 COMMENT '所属商品',
  `group_type` enum('alone','group','ladder') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼团类型:alone=直购,group=拼团,ladder=阶梯拼团',
  `is_ladder` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否阶梯拼团',
  `ladder_id` int(10) NOT NULL DEFAULT 0 COMMENT '阶梯ID',
  `people_num` int(10) NOT NULL DEFAULT 0 COMMENT '成团人数',
  `join_num` int(10) NOT NULL DEFAULT 0 COMMENT '参团人数',
  `state` enum('ready','start','success','fail','auto') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ready' COMMENT '拼团状态:ready=准备中,start=拼团中,success=已成团,fail=拼团失败,auto=自动成团',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '开团时间',
  `grouptime` int(10) NULL DEFAULT NULL COMMENT '成团时间',
  `validitytime` int(10) NULL DEFAULT NULL COMMENT '有效时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 36 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '拼团表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_groups_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_groups_goods`;
CREATE TABLE `fa_wanlshop_groups_goods`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '店铺ID',
  `shop_category_id` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '店铺内类目',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品类目',
  `brand_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '品牌ID',
  `category_attribute` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '类目属性',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品标题',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品主图',
  `images` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '产品相册',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品描述',
  `is_alone` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否显示单购是:0=否,1=是',
  `people_num` int(3) NULL DEFAULT 0 COMMENT '组团人数',
  `groups_num` int(3) NULL DEFAULT 0 COMMENT '成团人数',
  `is_ladder` tinyint(1) NOT NULL DEFAULT 0 COMMENT '开启阶梯团:0=否,1=是',
  `group_hour` int(10) NOT NULL DEFAULT 24 COMMENT '组团限时(小时)',
  `purchase_limit` int(6) NOT NULL DEFAULT 0 COMMENT '个人购买次数限制，默认是0，没有次数限制',
  `is_discount` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示团长优惠是:0=否,1=是',
  `discount_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '团长优惠类型:0=金额,1=百分百',
  `discount` float(10, 2) NULL DEFAULT 0.00 COMMENT '优惠金额/折扣',
  `flag` set('hot','index','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '标志(多选):hot=热门,index=首页,recommend=推荐',
  `stock` enum('porder','payment') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'porder' COMMENT '库存计算方式:porder=下单减库存,payment=付款减库存',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品详情',
  `freight_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '运费模板',
  `grounding` tinyint(1) NOT NULL DEFAULT 0 COMMENT '上架状态',
  `specs` enum('single','multi') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'single' COMMENT '商品规格:single=单规格,multi=多规格',
  `distribution` enum('true','false') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'false' COMMENT '是否独立分销:true=开启,false=关闭',
  `activity` enum('true','false') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'false' COMMENT '是否活动中:true=是,false=否',
  `views` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点击',
  `price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '产品价格',
  `cost_price` decimal(10, 2) NULL DEFAULT NULL COMMENT '成本价',
  `sales` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `payment` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '付款人数',
  `comment` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '评论',
  `praise` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '好评',
  `moderate` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '中评',
  `negative` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '差评',
  `like` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '收藏',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  `verify` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '审核状态:0=审核中,1=审核通过,2=审核驳回',
  `is_tj` tinyint(1) NULL DEFAULT 0 COMMENT '推荐商品:1=推荐,0=否',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 57 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '拼团商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_groups_goods_sku
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_groups_goods_sku`;
CREATE TABLE `fa_wanlshop_groups_goods_sku`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `thumbnail` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '缩略图',
  `difference` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '规格',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '商品价格',
  `market_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '市场价格',
  `stock` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品库存',
  `weigh` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '商品重量',
  `sn` char(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '商品编码',
  `sales` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '销量',
  `state` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '状态:0=新版数据,1=旧版数据',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  `cost_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '成本价',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 380 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '拼团商品SUK表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_groups_goods_spu
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_groups_goods_spu`;
CREATE TABLE `fa_wanlshop_groups_goods_spu`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `name` char(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '规格名称',
  `item` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '规格',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 101 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '拼团商品SPU表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_groups_ladder
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_groups_ladder`;
CREATE TABLE `fa_wanlshop_groups_ladder`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '商家ID',
  `goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `people_num` int(3) NOT NULL DEFAULT 2 COMMENT '人数',
  `discount` int(3) NOT NULL DEFAULT 0 COMMENT '优惠(%)',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 9 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '拼团阶梯配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_groups_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_groups_order`;
CREATE TABLE `fa_wanlshop_groups_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '所属商家',
  `order_no` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `address_id` int(10) NOT NULL DEFAULT 0 COMMENT '地址ID',
  `coupon_id` int(10) NOT NULL DEFAULT 0 COMMENT '优惠券ID',
  `isaddress` int(10) NOT NULL DEFAULT 0 COMMENT '是否修改过地址，修改过为1',
  `freight_type` int(10) NOT NULL DEFAULT 0 COMMENT '运费组合策略',
  `express_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递公司',
  `express_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递号',
  `state` enum('1','2','3','4','5','6','7') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '订单状态:1=待支付,2=待成团,3=待发货,4=待收货,5=待评论,6=已完成,7=已取消',
  `wechat_shipping_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '微信发货推送状态:0=未推送,1=已推送,2=推送失败',
  `wechat_shipping_time` int(10) NULL DEFAULT NULL COMMENT '微信发货推送时间',
  `wechat_shipping_error` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信发货推送错误信息',
  `remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单备注',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `paymenttime` int(10) NULL DEFAULT NULL COMMENT '付款时间',
  `groupstime` int(10) NULL DEFAULT NULL COMMENT '拼团时间',
  `delivertime` int(10) NULL DEFAULT NULL COMMENT '发货时间',
  `taketime` int(10) NULL DEFAULT NULL COMMENT '收货时间',
  `dealtime` int(10) NULL DEFAULT NULL COMMENT '成交时间(评论时间)',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '拼团订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_groups_order_address
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_groups_order_address`;
CREATE TABLE `fa_wanlshop_groups_order_address`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '商家ID',
  `order_id` int(10) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `isaddress` int(10) NOT NULL DEFAULT 0 COMMENT '是否修改过地址',
  `name` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '收货人',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `address_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地址名称',
  `location` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '经纬度',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '拼团订单地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_groups_order_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_groups_order_goods`;
CREATE TABLE `fa_wanlshop_groups_order_goods`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `goods_id` int(10) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `shop_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '店铺名称',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品标题',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品主图',
  `group_type` enum('alone','group','ladder') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼团类型:alone=直购,group=拼团,ladder=阶梯拼团',
  `group_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '拼团号',
  `is_alone` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否显示单购是:0=否,1=是',
  `is_ladder` tinyint(1) NOT NULL DEFAULT 0 COMMENT '开启阶梯团:0=否,1=是',
  `ladder_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '阶梯ID',
  `purchase_limit` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '个人购买次数限制，默认是0，没有次数限制',
  `people_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组团人数',
  `group_hour` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '组团限时(小时)',
  `goods_sku_sn` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品编码',
  `goods_sku_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `difference` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '选择的sku',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',
  `actual_payment` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际支付',
  `refund_id` int(10) NULL DEFAULT 0 COMMENT '退款ID',
  `refund_status` enum('0','1','2','3','4','5') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '退款状态:0=未退款,1=退款中,2=待退货,3=退款完成,4=退款关闭,5=退款被拒',
  `group_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '拼团价',
  `market_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '市场价',
  `freight_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '快递价格',
  `discount_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '优惠价格',
  `number` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '数量',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 7 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '拼团订单商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_groups_team
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_groups_team`;
CREATE TABLE `fa_wanlshop_groups_team`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `group_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '拼团号',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '所属商家ID',
  `order_id` int(10) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `order_goods_id` int(10) NOT NULL DEFAULT 0 COMMENT '订单商品ID',
  `username` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递号',
  `nickname` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递号',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递号',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '参团时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '拼团团队表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_icon
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_icon`;
CREATE TABLE `fa_wanlshop_icon`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
  `class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '路径',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 253 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '图标表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_kuaidi
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_kuaidi`;
CREATE TABLE `fa_wanlshop_kuaidi`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递名',
  `code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递编码',
  `logo` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递Logo',
  `tel` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递电话',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 29 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '快递表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_kuaidi_sub
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_kuaidi_sub`;
CREATE TABLE `fa_wanlshop_kuaidi_sub`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `sign` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '运单秘钥',
  `express_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '运单编号',
  `returncode` int(10) NULL DEFAULT NULL COMMENT '返回码',
  `message` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '提示消息',
  `status` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '监控状态',
  `state` int(1) NULL DEFAULT 0 COMMENT '监控状态',
  `ischeck` int(1) NULL DEFAULT 0 COMMENT '监控状态',
  `com` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '提示消息',
  `data` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '提示消息',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 306 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '快递运单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_link
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_link`;
CREATE TABLE `fa_wanlshop_link`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `type` enum('system','activity','user','product','page') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'system' COMMENT '页面类型:system=系统,activity=活动,user=用户中心,product=商品,page=自定义页面',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '页面标题',
  `route` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '路径',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '链接表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_live
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_live`;
CREATE TABLE `fa_wanlshop_live`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '商家ID',
  `goods_ids` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '商品ID',
  `image` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '直播封面',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '内容',
  `liveid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '推流流名',
  `liveurl` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推流地址',
  `pushurl` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '播流地址',
  `recordurl` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '录制地址',
  `gestion` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '审核状态',
  `views` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '观看',
  `like` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞',
  `state` enum('0','1','2','3') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '直播状态:0=未开播,1=正在直播,2=直播结束,3=直播封禁',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '直播表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_notice
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_notice`;
CREATE TABLE `fa_wanlshop_notice`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NULL DEFAULT 0 COMMENT '用户ID',
  `shop_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '店铺ID',
  `image` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '消息标题',
  `come` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '来自哪里',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '消息内容',
  `user_type` enum('user','shequ','quyu','all') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'user' COMMENT '用户类型:user=用户端,service=服务端',
  `type` enum('order','notice','service') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类:order=商城订单,notice=系统消息,service=用户端服务消息,service_notice=服务端系统消息,service_order=服务端订单消息,service_comment=服务端评论',
  `modules` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类型:order=订单,refund=退款,groupsorder=拼团订单,groupsrefund=拼团退款,live=直播,goods=商品,service=服务通知,service_notice=服务端系统消息,service_order=服务端订单消息,service_comment=服务端评论',
  `modules_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '所属ID',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  `is_read` tinyint(1) NULL DEFAULT 0 COMMENT '是否已读:0=否,1=是',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 836 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_notice_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_notice_log`;
CREATE TABLE `fa_wanlshop_notice_log`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NULL DEFAULT NULL COMMENT '用户',
  `notice_id` int(11) NULL DEFAULT NULL COMMENT '公告ID',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  `user_type` enum('user','service','shequ','quyu') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '用户类型:user=用户端,service=服务端',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 591 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '消息阅读记录' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_wanlshop_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_order`;
CREATE TABLE `fa_wanlshop_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '所属商家',
  `order_no` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `address_id` int(10) NOT NULL DEFAULT 0 COMMENT '地址ID',
  `coupon_id` int(10) NOT NULL DEFAULT 0 COMMENT '优惠券ID',
  `isaddress` int(10) NOT NULL DEFAULT 0 COMMENT '是否修改过地址，修改过为1',
  `freight_type` int(10) NOT NULL DEFAULT 0 COMMENT '运费组合策略',
  `express_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递公司',
  `express_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递号',
  `state` enum('1','2','3','4','5','6','7') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '订单状态:1=待支付,2=待发货,3=待收货,4=待评论,5=售后订单(已弃用),6=已完成,7=已取消',
  `wechat_shipping_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '微信发货推送状态:0=未推送,1=已推送,2=推送失败',
  `wechat_shipping_time` int(10) NULL DEFAULT NULL COMMENT '微信发货推送时间',
  `wechat_shipping_error` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '微信发货推送错误信息',
  `remarks` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单备注',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `paymenttime` int(10) NULL DEFAULT NULL COMMENT '付款时间',
  `delivertime` int(10) NULL DEFAULT NULL COMMENT '发货时间',
  `taketime` int(10) NULL DEFAULT NULL COMMENT '收货时间',
  `dealtime` int(10) NULL DEFAULT NULL COMMENT '成交时间(评论时间)',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  `is_read` tinyint(1) NULL DEFAULT 0 COMMENT '是否已读',
  `is_service` tinyint(1) NULL DEFAULT 0 COMMENT '0=商城，1=服务',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1527 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_order_address
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_order_address`;
CREATE TABLE `fa_wanlshop_order_address`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '商家ID',
  `order_id` int(10) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `isaddress` int(10) NOT NULL DEFAULT 0 COMMENT '是否修改过地址',
  `name` varchar(15) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '收货人',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '详细地址',
  `address_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '地址名称',
  `location` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '经纬度',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  `city` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '城市',
  `district` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '区/县',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1356 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单地址表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_order_goods
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_order_goods`;
CREATE TABLE `fa_wanlshop_order_goods`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `goods_id` int(10) NOT NULL DEFAULT 0 COMMENT '商品ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `shop_name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '店铺名称',
  `title` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '产品标题',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品主图',
  `goods_sku_sn` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '商品编码',
  `goods_sku_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `difference` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '选择的sku',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',
  `actual_payment` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际支付',
  `refund_id` int(10) NULL DEFAULT 0 COMMENT '退款ID',
  `refund_status` enum('0','1','2','3','4','5') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '退款状态:0=未退款,1=退款中,2=待退货,3=退款完成,4=退款关闭,5=退款被拒',
  `cost_price` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '成本价',
  `market_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '市场价',
  `freight_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '快递价格',
  `discount_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '优惠价格',
  `number` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '数量',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  `shequ_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '社区代理分成金额',
  `quyu_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '区域代理分成金额',
  `service_bili` decimal(10, 2) NULL DEFAULT 0.00 COMMENT '店铺分成比例',
  `shequ_d_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '社区代理待分成金额',
  `quyu_d_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '区域代理待分成金额',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1389 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单商品表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_page
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_page`;
CREATE TABLE `fa_wanlshop_page`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID(id=1首页)',
  `page_token` varchar(16) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '页面Token',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '自定义页面' COMMENT '页面名称',
  `cover` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '页面封面',
  `type` enum('page','shop','index','systpl') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'page' COMMENT '页面类型:page=单页,shop=店铺,index=首页,systpl=首页模板',
  `page` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '页面配置',
  `item` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '项目',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '自定义装修页面' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_pay
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_pay`;
CREATE TABLE `fa_wanlshop_pay`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `pay_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易号',
  `trade_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '交易订单号',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `order_id` int(10) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `order_no` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `pay_type` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '支付类型:0=余额支付,1=微信支付,2=支付宝支付',
  `pay_state` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '支付状态 (支付回调):0=未支付,1=已支付,2=已退款',
  `type` enum('goods','groups') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'goods' COMMENT '订单类型:goods=商品订单,groups=拼团订单',
  `number` int(10) NOT NULL DEFAULT 0 COMMENT '总数',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '支付金额',
  `order_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '订单金额',
  `freight_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '快递金额',
  `coupon_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '优惠券金额',
  `discount_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `refund_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `actual_payment` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '实际支付',
  `total_amount` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '总金额',
  `notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '通知内容',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  `order_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单ids',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1417 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单支付表 日志类' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_pay_account
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_pay_account`;
CREATE TABLE `fa_wanlshop_pay_account`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `username` varchar(4) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '持卡人姓名',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '手机号',
  `bankCode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '类型',
  `bankName` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
  `cardType` int(1) NULL DEFAULT 0 COMMENT '卡片类型',
  `cardCode` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '账户',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 74 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '账号表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_pay_out_trade
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_pay_out_trade`;
CREATE TABLE `fa_wanlshop_pay_out_trade`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `out_trade_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '交易号',
  `pay_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '支付订单号',
  `order_id` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '合并支付总金额',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 914 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '第三方支付合集' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_qun
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_qun`;
CREATE TABLE `fa_wanlshop_qun`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '群名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '缩略图',
  `ewm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '二维码',
  `weigh` int(5) NULL DEFAULT NULL COMMENT '权重',
  `type` tinyint(1) NULL DEFAULT 1 COMMENT '类别:1=商城,2=服务,3=课程',
  `updatetime` int(11) NULL DEFAULT NULL COMMENT '修改时间',
  `createtime` int(11) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '商城企业群' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Table structure for fa_wanlshop_record
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_record`;
CREATE TABLE `fa_wanlshop_record`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `goods_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '商品ID',
  `goods_type` enum('goods','groups','seckill') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'goods' COMMENT '商品类型:goods=普通商品,groups=拼团商品,seckill=秒杀商品',
  `category_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类目ID',
  `category_pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '类目父ID',
  `uuid` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT 'app唯一编码',
  `ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '设备ip',
  `views` int(64) NOT NULL DEFAULT 1 COMMENT '点击',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  `is_service` tinyint(1) NULL DEFAULT 0 COMMENT '服务',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14758 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '足迹、我看过谁，谁看过我、数据统计、友好度分析、类目分析表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_refund
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_refund`;
CREATE TABLE `fa_wanlshop_refund`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `order_id` int(10) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `order_pay_id` int(10) NOT NULL DEFAULT 0 COMMENT '支付ID',
  `order_type` enum('goods','groups','seckill') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'goods' COMMENT '订单类型:goods=普通订单,groups=拼团订单,seckill=秒杀订单',
  `goods_ids` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '退款产品',
  `expressType` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '物流状态:0=未收到货,1=已收到货',
  `price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '退款金额',
  `type` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '退款类型:0=我要退款(无需退货),1=退货退款',
  `reason` enum('0','1','2','3','4','5','6') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '退货理由:0=不喜欢,1=空包裹,2=一直未送达,3=颜色/尺码不符,4=质量问题,5=少件漏发,6=假冒品牌',
  `images` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '图片',
  `refund_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '退款理由',
  `refuse_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '拒绝理由',
  `notice` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '回调数据',
  `express_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递公司',
  `express_no` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '快递号',
  `state` enum('0','1','2','3','4','5','6','7','8') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '退款状态:0=申请退款,1=卖家同意,2=卖家拒绝,3=申请平台介入,4=成功退款,5=退款已关闭,6=已提交物流,7=第三方退款中,8=退款失败',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `agreetime` int(10) NULL DEFAULT NULL COMMENT '同意时间',
  `returntime` int(10) NULL DEFAULT NULL COMMENT '退货时间',
  `rejecttime` int(10) NULL DEFAULT NULL COMMENT '卖家拒绝时间',
  `closingtime` int(10) NULL DEFAULT NULL COMMENT '退款关闭时间',
  `completetime` int(10) NULL DEFAULT NULL COMMENT '完成时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  `express_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `express_desc` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 33 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单退款表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_refund_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_refund_log`;
CREATE TABLE `fa_wanlshop_refund_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) NULL DEFAULT 0 COMMENT '用户ID',
  `refund_id` int(10) NOT NULL DEFAULT 0 COMMENT '退款ID',
  `type` enum('0','1','2','3') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '退款状态:0=买家,1=卖家,2=官方,3=系统',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '退款理由',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `update_time` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 66 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单退款日志表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_search
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_search`;
CREATE TABLE `fa_wanlshop_search`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `keywords` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '关键字',
  `flag` set('hot','index','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '推荐位 :hot=系统热门,index=搜索条,recommend=推荐',
  `views` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '搜索次数',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `switch` tinyint(1) NOT NULL DEFAULT 0 COMMENT '开关',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '搜索表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_shop
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_shop`;
CREATE TABLE `fa_wanlshop_shop`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '会员ID',
  `shopname` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '店铺名称',
  `keywords` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '关键字',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '描述',
  `service_ids` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '服务(多选)',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '店铺头像',
  `state` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '店铺类型:0=个人,1=企业,2=旗舰',
  `level` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺等级',
  `islive` tinyint(1) NOT NULL DEFAULT 0 COMMENT '直播权限',
  `isself` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否自营',
  `bio` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '店铺简介',
  `city` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '省市',
  `return` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '退货地址',
  `like` int(10) NOT NULL DEFAULT 0 COMMENT '收藏/喜欢',
  `score_describe` float(3, 1) NOT NULL DEFAULT 0.0 COMMENT '宝贝描述',
  `score_service` float(3, 1) NOT NULL DEFAULT 0.0 COMMENT '卖家服务',
  `score_deliver` float(3, 1) NOT NULL DEFAULT 0.0 COMMENT '发货相符',
  `score_logistics` float(3, 1) NOT NULL DEFAULT 0.0 COMMENT '物流服务',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `verify` enum('0','1','2','3','4') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '审核:0=提交资质,1=提交店铺,2=提交审核,3=通过,4=未通过',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创店时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  `ruzhu_image` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '入驻协议图片',
  `trademark` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '商品资质',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopname`(`shopname`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 83 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '店铺表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_shop_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_shop_config`;
CREATE TABLE `fa_wanlshop_shop_config`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `freight` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '运费组合策略:0=运费叠加,1=以最低结算,2=以最高结算',
  `category_style` enum('1','2','3','4') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '类目样式:1=一级类目大图,2=一级类目九宫格,3=二级类目,4=多级类目样式',
  `iscloud` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '是否开启云打印:0=关闭,1=开启',
  `isauto` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '是否云打印自动发货:0=关闭,1=开启',
  `secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'Secret',
  `key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '授权KEY',
  `partnerId` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '面单账号',
  `partnerKey` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '面单密码',
  `siid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '打印设备码',
  `tempid` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '模板ID',
  `welcome` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '欢迎消息',
  `sendName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '姓名(店铺名)',
  `sendPhoneNum` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '固话/手机',
  `sendAddr` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '寄件地址',
  `returnName` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '姓名(店铺名)',
  `returnPhoneNum` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '固话/手机',
  `returnAddr` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '退货地址',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 83 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '店铺配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_shop_freight
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_shop_freight`;
CREATE TABLE `fa_wanlshop_shop_freight`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '模板名称',
  `delivery` enum('0','1','2','3','4','5','6','7','8','9','10','11','12','13','14','15','16','17','18') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '5' COMMENT '发货时间:0=4小时内,1=8小时内,2=12小时内,3=16小时内,4=20小时内,5=1天内,6=2天内,7=3天内,8=4天内,9=5天内,10=7天内,11=8天内,12=10天内,13=12天内,14=15天内,15=17天内,16=20天内,17=25天内,18=30天内',
  `isdelivery` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '是否包邮:0=自定义运费,1=卖家包邮',
  `valuation` enum('0','1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '计价方式:0=按件数,1=按重量,2=按体积',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 154 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '关注店铺表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_shop_freight_data
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_shop_freight_data`;
CREATE TABLE `fa_wanlshop_shop_freight_data`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `shop_id` int(10) NOT NULL DEFAULT 0 COMMENT '管理员ID',
  `freight_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '运费模板ID',
  `province` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '省份',
  `citys` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '城市',
  `first` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '首件',
  `first_fee` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '运费',
  `additional` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '续件',
  `additional_fee` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '续件运费',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 193 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '运费模板数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_shop_service
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_shop_service`;
CREATE TABLE `fa_wanlshop_shop_service`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '服务名称',
  `description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '服务描述',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创店时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `shopname`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '店铺服务表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_shop_sort
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_shop_sort`;
CREATE TABLE `fa_wanlshop_shop_sort`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `shop_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '店铺',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '父ID',
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
  `image` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '图片',
  `flag` set('hot','new','recommend') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '标志',
  `isnav` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否导航显示',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '权重',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `weigh`(`weigh`, `id`) USING BTREE,
  INDEX `parent_id`(`pid`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '商家自定义分类表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_theme
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_theme`;
CREATE TABLE `fa_wanlshop_theme`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主题名称',
  `color` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '#dbb29d' COMMENT '主题背景色',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '主题背景图',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  `status` enum('normal','hidden') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'normal' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户主题表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_third
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_third`;
CREATE TABLE `fa_wanlshop_third`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `token` varchar(36) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '令牌',
  `platform` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '第三方应用',
  `openid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '第三方唯一ID',
  `openname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '第三方会员昵称',
  `access_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'AccessToken',
  `refresh_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RefreshToken',
  `unionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'UnionId',
  `expires_in` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '有效期',
  `createtime` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `logintime` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '登录时间',
  `expiretime` int(10) UNSIGNED NULL DEFAULT NULL COMMENT '过期时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`, `platform`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '第三方登录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_version
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_version`;
CREATE TABLE `fa_wanlshop_version`  (
  `id` int(32) NOT NULL AUTO_INCREMENT COMMENT '客户端版本号',
  `title` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '版本标题',
  `versionName` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '版本名称',
  `versionCode` int(10) NOT NULL DEFAULT 100 COMMENT '版本号',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '升级内容',
  `androidLink` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '安卓升级文件',
  `iosLink` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT 'IOS升级文件',
  `type` enum('base','alpha','beta','rc','release') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'beta' COMMENT '版本类型:base=结构版,alpha=内测版,beta=公测版,rc=终测版,release=正式版',
  `enforce` tinyint(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '强制更新',
  `createtime` int(10) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updatetime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '版本升级表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_video
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_video`;
CREATE TABLE `fa_wanlshop_video`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `video_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '视频ID',
  `cover_url` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '封面截图',
  `snapshots` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '截图列表',
  `suggestion` enum('block','review','pass') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '安全审核:block=AI审核违规,review=AI审核疑似,pass=AI审核通过',
  `bitrate` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '视频流码率',
  `definition` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '视频流清晰度',
  `duration` int(5) NULL DEFAULT 0 COMMENT '视频流长度',
  `url` varchar(1500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '视频流的播放地址',
  `format` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '视频流格式',
  `fps` varchar(2) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '视频流帧率 ',
  `height` int(5) UNSIGNED NULL DEFAULT 0 COMMENT '视频流高度',
  `width` int(5) UNSIGNED NULL DEFAULT 0 COMMENT '视频流宽度',
  `size` int(32) NULL DEFAULT 0 COMMENT '视频流大小',
  `fit` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '视频的表现形式 ',
  `state` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '视频状态',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` int(10) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '视频表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_wechat_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_wechat_config`;
CREATE TABLE `fa_wanlshop_wechat_config`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '配置名称',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '配置标题',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '配置值',
  `createtime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updatetime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '微信配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_wanlshop_wechat_response
-- ----------------------------
DROP TABLE IF EXISTS `fa_wanlshop_wechat_response`;
CREATE TABLE `fa_wanlshop_wechat_response`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '资源名',
  `eventkey` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '事件',
  `type` enum('text','image','news','voice','video','music','link','app') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'text' COMMENT '类型',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '内容',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '备注',
  `createtime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updatetime` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
  `status` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `eventkey`(`eventkey`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 3 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '微信资源表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_withdraw
-- ----------------------------
DROP TABLE IF EXISTS `fa_withdraw`;
CREATE TABLE `fa_withdraw`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '会员ID',
  `money` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '金额',
  `handingfee` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '手续费',
  `taxes` decimal(10, 2) UNSIGNED NULL DEFAULT 0.00 COMMENT '税费',
  `type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '类型',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `account` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '提现账户',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `orderid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '订单号',
  `transactionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '流水号',
  `status` enum('created','successed','rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT 'created' COMMENT '状态:created=申请中,successed=成功,rejected=已拒绝',
  `transfertime` int(10) NULL DEFAULT NULL COMMENT '转账时间',
  `createtime` int(10) NULL DEFAULT NULL COMMENT '添加时间',
  `updatetime` int(10) NULL DEFAULT NULL COMMENT '更新时间',
  `is_service` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 62 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '提现表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_article
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_article`;
CREATE TABLE `fa_xiluedu_article`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `category_id` int(11) NULL DEFAULT NULL COMMENT '分类',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `thumb_image` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '封面图',
  `tags` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标签',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `weigh` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '权重',
  `status` enum('0','1') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '状态:1=显示,0=隐藏',
  `view_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '阅读量',
  `favorite_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞量',
  `remarks` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '后台备注',
  `deletetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '删除时间',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '文章管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_article_category
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_article_category`;
CREATE TABLE `fa_xiluedu_article_category`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `weigh` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '权重',
  `status` enum('0','1') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '状态:1=显示,0=隐藏',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '分类管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_banner
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_banner`;
CREATE TABLE `fa_xiluedu_banner`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `minapp_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '小程序链接',
  `thumb_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `group` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '分组',
  `status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '状态:0=隐藏,1=显示',
  `weigh` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `memo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '备注',
  `show_count` int(11) NOT NULL DEFAULT 0,
  `createtime` bigint(16) NULL DEFAULT NULL,
  `updatetime` bigint(16) NULL DEFAULT NULL,
  `deletetime` bigint(16) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '图片banner' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_category
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_category`;
CREATE TABLE `fa_xiluedu_category`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '名称',
  `name_path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL,
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT 'id路径',
  `pid` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '上级',
  `thumb_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '图片',
  `default_status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '默认显示:0=否,1=是',
  `status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=正常',
  `weigh` int(10) NULL DEFAULT 0 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `is_service` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_config
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_config`;
CREATE TABLE `fa_xiluedu_config`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '变量名',
  `group` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '分组',
  `title` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '变量标题',
  `tip` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '变量描述',
  `type` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '类型:string,text,int,bool,array,datetime,date,file',
  `visible` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '可见条件',
  `value` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '变量值',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL COMMENT '变量字典数据',
  `rule` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '验证规则',
  `extend` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '扩展属性',
  `setting` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '配置',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `name`(`name`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '教育配置' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_coupon
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_coupon`;
CREATE TABLE `fa_xiluedu_coupon`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `receive_start_time` bigint(16) NULL DEFAULT NULL COMMENT '领取开始时间',
  `receive_end_time` bigint(16) NULL DEFAULT NULL COMMENT '领取结束时间',
  `use_start_time` bigint(16) NULL DEFAULT NULL COMMENT '使用开始时间',
  `use_end_time` bigint(16) NULL DEFAULT NULL COMMENT '使用结束时间',
  `max_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '发行数量',
  `at_least` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '使用门槛',
  `type` enum('1','2') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '类型:1=满减,2=折扣',
  `discount` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '折扣（折扣类型必填）',
  `money` decimal(10, 0) NOT NULL DEFAULT 0 COMMENT '减免金额',
  `per_count` tinyint(4) NOT NULL DEFAULT 1 COMMENT '每人限领',
  `freight_type` tinyint(2) NOT NULL DEFAULT 1 COMMENT '发放形式:1=手动领取,2=注册赠送,3=下单赠送',
  `range_status` tinyint(2) UNSIGNED NOT NULL DEFAULT 1 COMMENT '使用范围:1=全部,2=指定课程',
  `status` tinyint(2) UNSIGNED NOT NULL DEFAULT 1 COMMENT '状态:0=已下架,1=上架中',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '优惠券兑换码',
  `is_service` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 6 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_coupon_course
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_coupon_course`;
CREATE TABLE `fa_xiluedu_coupon_course`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `coupon_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券id',
  `course_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '课程id',
  `createtime` bigint(16) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_course
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_course`;
CREATE TABLE `fa_xiluedu_course`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '课程类型:1=视频课程,2=音频课程,3=文章课程',
  `category_ids` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '分类层级id',
  `category_id` int(10) NOT NULL DEFAULT 0 COMMENT '课程分类',
  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '课程名',
  `tag_ids` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '课程标签',
  `teacher_id` int(11) NOT NULL DEFAULT 0 COMMENT '课程老师',
  `thumb_image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '缩略图',
  `images` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图集',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '文章内容',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `introduce` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '音视频详情',
  `salesprice` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '购买价格',
  `market_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '市场价格',
  `is_charge` tinyint(4) NOT NULL DEFAULT 1 COMMENT '是否收费:0=免费,1=vip,2=单独购买',
  `is_hot` tinyint(4) NOT NULL DEFAULT 0 COMMENT '热门:0=否,1=是',
  `is_new` tinyint(4) NOT NULL DEFAULT 0 COMMENT '最新:0=否,1=是',
  `basic_num` int(11) NOT NULL DEFAULT 0 COMMENT '基本观看人数',
  `real_num` int(11) NOT NULL DEFAULT 0 COMMENT '实际人数',
  `distribution_one_rate` tinyint(4) NOT NULL DEFAULT 0 COMMENT '一级返佣比例',
  `distribution_two_rate` tinyint(4) NOT NULL DEFAULT 0 COMMENT '二级返佣比例',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=下架,1=上架',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '排序',
  `poster_image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '海报图',
  `poster_video` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '海报视频',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  `view_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '浏览数',
  `fav_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞数',
  `collection_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '收藏数',
  `comment_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '评论数',
  `play_count` int(11) NOT NULL DEFAULT 0 COMMENT '播放数',
  `avg_score` int(11) NULL DEFAULT 0 COMMENT '平均分',
  `shequ_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '社区代理分成比例',
  `quyu_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '区域代理分成比例',
  `service_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '老师分成比例',
  `is_service` tinyint(1) NULL DEFAULT 0,
  `total_time` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `course_type`(`type`) USING BTREE,
  INDEX `course_type_level_ids`(`category_ids`) USING BTREE,
  INDEX `course_type_id`(`category_id`) USING BTREE,
  INDEX `tag_ids`(`tag_ids`(255)) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 16 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '课程管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_course_code
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_course_code`;
CREATE TABLE `fa_xiluedu_course_code`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `course_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '课程id',
  `code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '兑换码',
  `use_status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '0' COMMENT '使用状态:0=未使用,1=已使用',
  `usetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '使用时间',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '使用用户id',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '课程兑换码' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_course_comment
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_course_comment`;
CREATE TABLE `fa_xiluedu_course_comment`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户',
  `course_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '课程',
  `score` tinyint(4) NOT NULL DEFAULT 0 COMMENT '评分',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '评价内容',
  `images` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态:0=隐藏,1=待审核,2=通过',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '发布时间',
  `updatetime` bigint(16) UNSIGNED NULL DEFAULT NULL,
  `deletetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `deletetime`(`deletetime`) USING BTREE,
  INDEX `course_id`(`course_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '课程评价' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_course_directory
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_course_directory`;
CREATE TABLE `fa_xiluedu_course_directory`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `course_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '课程id',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '课程类型:1=视频,2=文档',
  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '课程名',
  `poster_image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '封面图',
  `file` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '视频链接',
  `file_time` int(11) NOT NULL DEFAULT 0 COMMENT '视频长度(s）',
  `is_free` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否免费:0=付费,1=免费',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=下架,1=上架',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `course_type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '课程章节' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_course_file
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_course_file`;
CREATE TABLE `fa_xiluedu_course_file`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `course_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '课程id',
  `course_list_id` int(10) NULL DEFAULT NULL COMMENT '目录ID',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '课程类型:1=视频,2=资料',
  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '课程名',
  `poster_image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '封面图',
  `file` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '视频链接',
  `file_time` int(11) NOT NULL DEFAULT 0 COMMENT '视频长度(s）',
  `is_free` tinyint(4) NOT NULL DEFAULT 0 COMMENT '是否免费:0=付费,1=免费',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=下架,1=上架',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `course_type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '课程章节' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_course_free
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_course_free`;
CREATE TABLE `fa_xiluedu_course_free`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '活动名称',
  `start_time` bigint(16) NULL DEFAULT NULL COMMENT '活动开始时间',
  `end_time` bigint(16) NULL DEFAULT NULL COMMENT '结束时间',
  `course_ids` varchar(2500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '限免课程id',
  `status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '1' COMMENT '状态:0=隐藏,1=正常',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '限免活动' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_course_free_enroll
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_course_free_enroll`;
CREATE TABLE `fa_xiluedu_course_free_enroll`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `free_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '免费活动id',
  `start_time` bigint(16) NULL DEFAULT NULL COMMENT '活动开始时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户预约免费活动' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_course_list
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_course_list`;
CREATE TABLE `fa_xiluedu_course_list`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `course_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '课程id',
  `type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '课程类型:1=视频,2=资料',
  `name` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '目录名',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=下架,1=上架',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `course_type`(`type`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '课程目录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_course_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_course_order`;
CREATE TABLE `fa_xiluedu_course_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `platform` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '下单平台',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `order_no` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `order_trade_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '交易单号',
  `course_id` int(10) NOT NULL DEFAULT 0 COMMENT '课程id',
  `pay_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '支付方式:1=微信,2=余额,3=支付宝',
  `total_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '总价格',
  `pay_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '支付价格',
  `pay_score` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付积分',
  `user_coupon_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券id',
  `favourable_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '优惠金额',
  `wxconfig` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '微信支付配置',
  `pay_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '支付状态:1=待支付,2=已支付',
  `paytime` bigint(16) NULL DEFAULT NULL COMMENT '支付时间',
  `ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '买家ip',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `notify` tinyint(1) NOT NULL DEFAULT 0 COMMENT '进入回调:0=未进入,1=进入',
  `openid` varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '下单openid,仅限h5',
  `is_service` tinyint(1) NULL DEFAULT 0,
  `kt_sqdl` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 543 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '课程订单' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_custom_made
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_custom_made`;
CREATE TABLE `fa_xiluedu_custom_made`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `made_module_id` tinyint(4) NOT NULL DEFAULT 0 COMMENT '模块id',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=禁用,1=启用',
  `weigh` int(10) NULL DEFAULT 0 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '定制内容' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_divide_money
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_divide_money`;
CREATE TABLE `fa_xiluedu_divide_money`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` enum('course','offline','vip','withdraw','refuse') CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型:course=在线课程,offline=线下课程,vip=vip,withdraw=提现,refuse=提现失败',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '佣金获得用户id',
  `buy_user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '佣金贡献用户id',
  `target_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '相关表id',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单id',
  `money_log_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '佣金id',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '佣金日志关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_feedback_type
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_feedback_type`;
CREATE TABLE `fa_xiluedu_feedback_type`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `status` enum('1','0') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '1' COMMENT '状态:0=禁用,1=启用',
  `weigh` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 4 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '意见反馈类型' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_footer_view
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_footer_view`;
CREATE TABLE `fa_xiluedu_footer_view`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(1) UNSIGNED NOT NULL DEFAULT 1 COMMENT '类型:1=课程,2=线下课程',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `footer_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'ID',
  `view_count` int(10) UNSIGNED NOT NULL DEFAULT 1 COMMENT '看的总次数',
  `view_time` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '上一次看的时间',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 657 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户足迹' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_lottery_active
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_lottery_active`;
CREATE TABLE `fa_xiluedu_lottery_active`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `day_count` tinyint(4) NOT NULL DEFAULT 0 COMMENT '每天参与次数',
  `join_type` tinyint(1) NOT NULL DEFAULT 1 COMMENT '参与方式:1=免费,2=积分',
  `score` int(11) NOT NULL DEFAULT 0 COMMENT '积分方式时的每次消耗积分',
  `rule_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '抽奖规则',
  `introduce` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '活动介绍',
  `start_time` bigint(16) NOT NULL DEFAULT 0 COMMENT '开始时间',
  `end_time` bigint(16) NOT NULL DEFAULT 0 COMMENT '结束时间',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=关闭,1=开启',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '抽奖活动' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_lottery_awards
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_lottery_awards`;
CREATE TABLE `fa_xiluedu_lottery_awards`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `lottery_active_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '抽奖主题id',
  `name` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `reward` int(10) NOT NULL DEFAULT 0 COMMENT '积分奖励',
  `rate` int(10) NOT NULL DEFAULT 0 COMMENT '概率(数字越小概率越小)',
  `num` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '数量(0为不限量)',
  `ready_num` int(11) NOT NULL DEFAULT 0 COMMENT '已抽到次数',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=下架,1=上架',
  `weigh` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '奖品库' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_lottery_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_lottery_log`;
CREATE TABLE `fa_xiluedu_lottery_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `lottery_active_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '抽奖活动id',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `lottery_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '转盘奖励id',
  `score` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '积分奖励',
  `awards_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '奖励名',
  `award_time` bigint(16) NULL DEFAULT NULL COMMENT '中奖时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '抽奖日志' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_maxim
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_maxim`;
CREATE TABLE `fa_xiluedu_maxim`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `teacher_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '金句老师',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '内容',
  `images` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=禁用,1=启用',
  `recom_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '推荐:0=不推荐,1=推荐',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `fav_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞量',
  `comment_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '评价数',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '导师箴言' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_maxim_comment
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_maxim_comment`;
CREATE TABLE `fa_xiluedu_maxim_comment`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户',
  `maxim_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '课程',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '评价内容',
  `images` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态:0=隐藏,1=上架',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '发布时间',
  `updatetime` bigint(16) UNSIGNED NULL DEFAULT NULL,
  `deletetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `deletetime`(`deletetime`) USING BTREE,
  INDEX `maxim_id`(`maxim_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '箴言评价' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_offline_course
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_offline_course`;
CREATE TABLE `fa_xiluedu_offline_course`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '课程名',
  `introduce` longtext CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '详情',
  `thumb_image` varchar(300) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '缩略图',
  `salesprice` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '报名价格',
  `market_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '市场价',
  `teacher_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '老师',
  `content` text CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '课程说明',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=禁用,1=启用',
  `weigh` int(10) NOT NULL DEFAULT 0 COMMENT '排序',
  `enroll_end_time` bigint(16) NULL DEFAULT NULL COMMENT '报名结束时间',
  `start_time` bigint(16) NULL DEFAULT NULL COMMENT '开始时间',
  `end_time` bigint(16) NULL DEFAULT NULL COMMENT '结束时间',
  `address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '开课地点',
  `total_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '总人数',
  `enroll_count` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '报名人数',
  `mobile` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '咨询电话',
  `distribution_one_rate` tinyint(4) NOT NULL DEFAULT 0 COMMENT '一级返佣比例',
  `distribution_two_rate` tinyint(4) NOT NULL DEFAULT 0 COMMENT '二级返佣比例',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `deletetime` bigint(16) NULL DEFAULT NULL COMMENT '删除时间',
  `poster_image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '海报图',
  `poster_video` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '海报视频',
  `is_ylgw` tinyint(1) NULL DEFAULT 0 COMMENT '养老院长:0=否,1=是',
  `shequ_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '社区代理分成比例',
  `quyu_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '区域代理分成比例',
  `service_bili` decimal(11, 2) NULL DEFAULT 0.00 COMMENT '老师分成比例',
  `is_service` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '线下课程课期报名' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_offline_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_offline_order`;
CREATE TABLE `fa_xiluedu_offline_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `platform` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单平台',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `order_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `order_trade_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '交易单号',
  `pay_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '支付方式:1=微信,2=支付宝支付',
  `pay_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '支付价格',
  `pay_status` tinyint(2) NOT NULL DEFAULT 0 COMMENT '支付状态:0=待支付,1=已支付',
  `paytime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '支付时间',
  `wxconfig` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '微信支付配置',
  `transaction_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '三方交易号',
  `username` varchar(125) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '预约姓名',
  `mobile` varchar(11) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '预约手机号',
  `course_id` int(10) NULL DEFAULT NULL COMMENT '课程id',
  `course_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `course_introduce` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '详情',
  `course_image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '核销二维码',
  `course_salesprice` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '价格',
  `course_teacher_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '老师id',
  `course_teacher_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '老师名',
  `course_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '说明',
  `course_start_time` bigint(16) NULL DEFAULT NULL COMMENT '上课开始时间',
  `course_end_time` bigint(16) NULL DEFAULT NULL COMMENT '上课结束时间',
  `course_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '开课地址',
  `course_mobile` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '咨询电话',
  `course_is_ylgw` tinyint(1) NULL DEFAULT 0 COMMENT '养老院长:0=否,1=是',
  `ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '买家ip',
  `refund_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '退费状态:0=无,1=待退款,2=申请审核中,3=已退款,4=已拒绝',
  `refund_time` bigint(16) NULL DEFAULT NULL COMMENT '退款时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `openid` varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '下单openid,仅限h5',
  `remark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL COMMENT '备注',
  `is_fy` tinyint(1) NULL DEFAULT 0,
  `is_service` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '线下课程报名' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_order_divide
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_order_divide`;
CREATE TABLE `fa_xiluedu_order_divide`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` enum('course','offline','vip') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'course' COMMENT '类型:course=在线课程,offline=线下课程,vip=vip',
  `distribution_module` tinyint(1) UNSIGNED NULL DEFAULT 0 COMMENT '分销比例使用模块:1=全局比例,2=产品设置',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `order_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '订单ID',
  `order_divide_money` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '订单金额',
  `first_rate` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '一级比例',
  `first_money` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '一级佣金',
  `first_user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '一级用户ID',
  `second_rate` tinyint(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '二级比例',
  `second_money` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '二级佣金',
  `second_user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '二级用户ID',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `status` enum('0','1','2','3') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '状态:0=失效,1=订单创建,2=支付完成,3=已分配',
  `unfreezetime` bigint(16) NULL DEFAULT NULL COMMENT '解冻时间',
  `canceltime` bigint(16) NULL DEFAULT NULL COMMENT '取消时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '佣金关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_search_keyword
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_search_keyword`;
CREATE TABLE `fa_xiluedu_search_keyword`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `keyword` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `search_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '搜索次数',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `keyword`(`keyword`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1163 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_bin COMMENT = '搜索记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_service_study
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_service_study`;
CREATE TABLE `fa_xiluedu_service_study`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(2) NULL DEFAULT 1 COMMENT '类型:1=线上课程,2=线下课程',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `course_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '课程id',
  `course_file_id` int(10) NOT NULL DEFAULT 0 COMMENT '课程章节',
  `view_time` bigint(16) UNSIGNED NOT NULL DEFAULT 0 COMMENT '观看视频时间',
  `is_complete` tinyint(1) NULL DEFAULT 0 COMMENT '完成',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `course_type` tinyint(1) NULL DEFAULT 1 COMMENT '类型:1=视频,2=音频,3=学分',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `date` date NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '服务者学习记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_sign_log
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_sign_log`;
CREATE TABLE `fa_xiluedu_sign_log`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `score` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '获得积分',
  `series_days` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '连续签到天数',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '签到时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户签到' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_sign_poster
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_sign_poster`;
CREATE TABLE `fa_xiluedu_sign_poster`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '背景图',
  `propagate` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '宣传语',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=禁用,1=启用',
  `weigh` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '签到海报' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_sign_rule
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_sign_rule`;
CREATE TABLE `fa_xiluedu_sign_rule`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `day` smallint(6) NOT NULL DEFAULT 0 COMMENT '连续签到天数',
  `score` smallint(6) UNSIGNED NOT NULL DEFAULT 0 COMMENT '普通用户打卡积分',
  `vip_score` smallint(6) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'vip会员打卡积分',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '签到规则' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_sign_sentence
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_sign_sentence`;
CREATE TABLE `fa_xiluedu_sign_sentence`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '语句',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=禁用,1=启用',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '海报语录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_singlepage
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_singlepage`;
CREATE TABLE `fa_xiluedu_singlepage`  (
  `id` int(10) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `mark` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '标示',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '名称',
  `type` tinyint(1) NULL DEFAULT 1 COMMENT '内容类别',
  `thumb_image` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT '' COMMENT '封面图',
  `description` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '描述',
  `content` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '内容',
  `weigh` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '权重',
  `status` enum('0','1') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '0' COMMENT '状态:1=显示,0=隐藏',
  `view_num` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '阅读量',
  `deletetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '删除时间',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 43 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '单页管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_system_message
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_system_message`;
CREATE TABLE `fa_xiluedu_system_message`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '标题',
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '类型:1=系统消息,2=活动消息',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '内容',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统消息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_tags
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_tags`;
CREATE TABLE `fa_xiluedu_tags`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '标签类型:1=课程标签,2=商城标签',
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '标签名',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=禁用,1=启用',
  `weigh` int(11) NOT NULL DEFAULT 0 COMMENT '排序',
  `createtime` bigint(16) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updatetime` bigint(16) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 12 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '标签管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_teacher
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_teacher`;
CREATE TABLE `fa_xiluedu_teacher`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '名称',
  `thumb_image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '头像',
  `image` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '宣传图',
  `job` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '职位',
  `description` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '简介',
  `weigh` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `labels` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '标签',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=正常',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 8 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '老师管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_third
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_third`;
CREATE TABLE `fa_xiluedu_third`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员ID',
  `platform` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '第三方应用',
  `openid` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '第三方唯一ID',
  `openname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '第三方会员昵称',
  `avatar` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '头像',
  `access_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'AccessToken',
  `refresh_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'RefreshToken',
  `unionid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT 'UnionId',
  `expires_in` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '有效期',
  `auth_userinfo` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '0' COMMENT '授权用户信息',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '更新时间',
  `logintime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '登录时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `user_id`(`user_id`, `platform`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '第三方登录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_ticket
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_ticket`;
CREATE TABLE `fa_xiluedu_ticket`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `company_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '单位名称',
  `tax_registration_number` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '纳税人识别号',
  `register_address` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '注册地址',
  `register_mobile` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '注册电话',
  `open_bank` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '开户银行',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '开票信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_topic
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_topic`;
CREATE TABLE `fa_xiluedu_topic`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '话题名',
  `image` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '缩略图',
  `description` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '描述',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=禁用,1=启用',
  `top_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '置顶:0=不置顶,1=置顶',
  `forum_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '数量',
  `weigh` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '话题管理' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_topic_forum
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_topic_forum`;
CREATE TABLE `fa_xiluedu_topic_forum`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户',
  `topic_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '话题',
  `content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '帖子内容',
  `images` varchar(2000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '图片',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=禁用,1=启用',
  `hot_status` tinyint(1) NOT NULL DEFAULT 0 COMMENT '热门:0=否,1=是',
  `check_status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '审核状态:1=待审核,2=已通过,3=已拒绝',
  `fav_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '点赞量',
  `share_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '转发数量',
  `comment_count` int(11) NOT NULL DEFAULT 0 COMMENT '评价数量',
  `collection_count` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '收藏量',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `weigh` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `uid`(`user_id`) USING BTREE,
  INDEX `topic_id`(`topic_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '话题帖子' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_topic_forum_comment
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_topic_forum_comment`;
CREATE TABLE `fa_xiluedu_topic_forum_comment`  (
  `id` int(10) NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户',
  `forum_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '帖子',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '评价内容',
  `status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '状态:0=隐藏,1=待审核,2=通过',
  `comment_count` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '评论数',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '发布时间',
  `updatetime` bigint(16) UNSIGNED NULL DEFAULT NULL,
  `deletetime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `deletetime`(`deletetime`) USING BTREE,
  INDEX `course_id`(`forum_id`) USING BTREE,
  INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '帖子评价' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_collection
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_collection`;
CREATE TABLE `fa_xiluedu_user_collection`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '用户id',
  `collection_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型:0=老师,1=课程',
  `collection_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'id',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '收藏时间',
  `is_service` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id_2`(`user_id`, `collection_type`, `collection_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 81 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户收藏表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_coupon
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_coupon`;
CREATE TABLE `fa_xiluedu_user_coupon`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `coupon_id` int(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '优惠券id',
  `start_time` bigint(16) NULL DEFAULT NULL COMMENT '使用开始时间',
  `end_time` bigint(16) NULL DEFAULT NULL COMMENT '使用结束时间',
  `use_status` enum('0','1') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '0' COMMENT '使用状态:0=未使用,1=已使用',
  `usetime` bigint(16) NULL DEFAULT NULL COMMENT '使用时间',
  `pop_status` enum('1','0') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '首页弹出:1=未弹,0=已弹',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '领取时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `is_service` tinyint(1) NULL DEFAULT 0,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 19 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_course
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_course`;
CREATE TABLE `fa_xiluedu_user_course`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `from_type` tinyint(4) NOT NULL DEFAULT 1 COMMENT '来源:1=自主购买,2=兑换码兑换,3=积分兑换',
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `course_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '课程id',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 169 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户课程' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_custom_made
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_custom_made`;
CREATE TABLE `fa_xiluedu_user_custom_made`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `made_module_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '定制组别',
  `made_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '定制id',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户定制表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_favourite
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_favourite`;
CREATE TABLE `fa_xiluedu_user_favourite`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` int(10) UNSIGNED NULL DEFAULT 0 COMMENT '用户id',
  `favourite_type` tinyint(1) NOT NULL DEFAULT 0 COMMENT '类型:1=导师箴言,2=帖子,3=课程文章',
  `favourite_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'id',
  `createtime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '收藏时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id_2`(`user_id`, `favourite_type`, `favourite_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户点赞表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_feedback
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_feedback`;
CREATE TABLE `fa_xiluedu_user_feedback`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '反馈内容',
  `images` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '图片',
  `contact` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '联系方式',
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '姓名',
  `reason` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '理由',
  `status` tinyint(3) NOT NULL DEFAULT 0 COMMENT '状态:0=未处理,1=已采纳,2=不予采纳',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 18 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_focus
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_focus`;
CREATE TABLE `fa_xiluedu_user_focus`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `focus_user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '关注的用户id',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户关注表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_message
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_message`;
CREATE TABLE `fa_xiluedu_user_message`  (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户id',
  `title` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '内容',
  `type` tinyint(3) NOT NULL DEFAULT 0 COMMENT '类型: 多样',
  `read` tinyint(1) NOT NULL DEFAULT 0 COMMENT '读取状态:0=未读,1=已读',
  `read_time` bigint(16) NULL DEFAULT NULL COMMENT '读取时间',
  `extra` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 183 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '简历消息; 非简历模块数据' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_message_account
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_message_account`;
CREATE TABLE `fa_xiluedu_user_message_account`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `total_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '累计佣金',
  `withdraw_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '累计提现金额',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '余额',
  `activity_message` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '活动消息',
  `system_message` int(11) NOT NULL DEFAULT 0 COMMENT '系统消息',
  `user_message` int(11) NULL DEFAULT 0 COMMENT '个人消息',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 2 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用会消息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_relation
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_relation`;
CREATE TABLE `fa_xiluedu_user_relation`  (
  `id` int(11) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户ID',
  `first_user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '一级上级',
  `second_user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '二级上级ID',
  `third_user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '三级上级ID',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户上级关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_study
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_study`;
CREATE TABLE `fa_xiluedu_user_study`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `type` tinyint(2) NULL DEFAULT 1 COMMENT '类型:1=线上课程,2=线下课程',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `course_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '课程id',
  `course_file_id` int(10) NOT NULL DEFAULT 0 COMMENT '课程章节',
  `view_time` bigint(16) UNSIGNED NOT NULL DEFAULT 0 COMMENT '观看视频时间',
  `is_complete` tinyint(1) NULL DEFAULT 0 COMMENT '完成',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `course_type` tinyint(1) NULL DEFAULT 1 COMMENT '类型:1=视频,2=音频,3=学分',
  `date` date NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 21 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '用户学习记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_user_vip
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_user_vip`;
CREATE TABLE `fa_xiluedu_user_vip`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL COMMENT '用户ID',
  `vip_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最后续费vipId',
  `expire_time` bigint(16) NULL DEFAULT NULL COMMENT '过期时间',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `user_id`(`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户会员卡' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_vip
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_vip`;
CREATE TABLE `fa_xiluedu_vip`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '会员卡名',
  `salesprice` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',
  `expire_in` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '有效期',
  `unit` tinyint(1) NOT NULL DEFAULT 1 COMMENT '单位:1=天,2=月,3=年',
  `privilege_content` text CHARACTER SET utf8 COLLATE utf8_general_ci NULL COMMENT '会员权益',
  `distribution_one_rate` tinyint(4) NOT NULL DEFAULT 0 COMMENT '一级返佣比例',
  `distribution_two_rate` tinyint(4) NOT NULL DEFAULT 0 COMMENT '二级返佣比例',
  `status` tinyint(1) NOT NULL DEFAULT 1 COMMENT '状态:0=隐藏,1=正常',
  `weigh` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '排序',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = '会员卡' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_vip_order
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_vip_order`;
CREATE TABLE `fa_xiluedu_vip_order`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `platform` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单平台',
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '用户id',
  `order_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '订单号',
  `order_trade_no` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '交易单号',
  `total_price` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '总价格',
  `pay_price` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '支付价格',
  `pay_type` int(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付类型:支付方式:1=微信,2=支付宝,3=余额',
  `pay_status` tinyint(2) UNSIGNED NOT NULL DEFAULT 0 COMMENT '支付状态:0=待支付,1=已支付',
  `paytime` bigint(16) UNSIGNED NULL DEFAULT NULL COMMENT '支付时间',
  `wxconfig` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '微信支付配置',
  `transaction_id` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '三方交易号',
  `vip_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '会员卡id',
  `vip_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '名称',
  `vip_salesprice` decimal(10, 2) UNSIGNED NOT NULL DEFAULT 0.00 COMMENT '价格',
  `vip_expire` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '时间',
  `vip_unit` tinyint(1) UNSIGNED NULL DEFAULT 1 COMMENT '单位:1=天,2=月,3=年',
  `ip` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '买家ip',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  `openid` varchar(80) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '下单openid,仅限h5',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8 COLLATE = utf8_general_ci COMMENT = 'VIP购买' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for fa_xiluedu_withdraw
-- ----------------------------
DROP TABLE IF EXISTS `fa_xiluedu_withdraw`;
CREATE TABLE `fa_xiluedu_withdraw`  (
  `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` int(10) UNSIGNED NOT NULL DEFAULT 0 COMMENT '提现用户',
  `money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '提现金额',
  `real_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '实际到账',
  `rate` double(10, 2) NOT NULL DEFAULT 0.00 COMMENT '手续费率（*）',
  `rate_money` decimal(10, 2) NOT NULL DEFAULT 0.00 COMMENT '手续费',
  `state` tinyint(4) NOT NULL DEFAULT 1 COMMENT '状态:1=审核中,2=处理中,3=已处理,4=已拒绝',
  `reason` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '拒绝理由',
  `certificate` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '打款凭证',
  `order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '提现订单号',
  `createtime` bigint(16) NULL DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '提现金额' ROW_FORMAT = DYNAMIC;

SET FOREIGN_KEY_CHECKS = 1;
