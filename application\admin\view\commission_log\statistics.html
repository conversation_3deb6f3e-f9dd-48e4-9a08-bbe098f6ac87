<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <h3 class="panel-title">分佣统计分析</h3>
    </div>
    <div class="panel-body">
        <!-- 统计类型选择 -->
        <div class="row">
            <div class="col-md-12">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-primary active" data-type="role">按角色统计</button>
                    <button type="button" class="btn btn-default" data-type="order_type">按订单类型统计</button>
                    <button type="button" class="btn btn-default" data-type="daily">按日期统计</button>
                </div>
            </div>
        </div>

        <hr>

        <!-- 统计结果显示区域 -->
        <div class="row">
            <div class="col-md-12">
                <div id="statistics-content">
                    <div class="text-center">
                        <i class="fa fa-spinner fa-spin"></i> 正在加载统计数据...
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(function() {
    // 统计类型切换
    $('.btn-group button').click(function() {
        $('.btn-group button').removeClass('active').addClass('btn-default').removeClass('btn-primary');
        $(this).addClass('active').removeClass('btn-default').addClass('btn-primary');

        var type = $(this).data('type');
        loadStatistics(type);
    });

    // 默认加载角色统计
    loadStatistics('role');

    function loadStatistics(type) {
        $('#statistics-content').html('<div class="text-center"><i class="fa fa-spinner fa-spin"></i> 正在加载统计数据...</div>');

        $.ajax({
            url: '{:url("commission_log/statistics")}',
            type: 'POST',
            data: {type: type},
            dataType: 'json',
            success: function(res) {
                if (res.code === 1) {
                    renderStatistics(type, res.data);
                } else {
                    $('#statistics-content').html('<div class="alert alert-warning">加载统计数据失败：' + (res.msg || '未知错误') + '</div>');
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX请求失败:', xhr, status, error);
                $('#statistics-content').html('<div class="alert alert-danger">请求失败，请检查网络连接或联系管理员</div>');
            }
        });
    }

    function renderStatistics(type, data) {
        var html = '';

        if (!data || data.length === 0) {
            html = '<div class="alert alert-info">暂无统计数据</div>';
        } else {
            if (type === 'role') {
                html = renderRoleStatistics(data);
            } else if (type === 'order_type') {
                html = renderOrderTypeStatistics(data);
            } else if (type === 'daily') {
                html = renderDailyStatistics(data);
            }
        }

        $('#statistics-content').html(html);
    }

    function renderRoleStatistics(data) {
        var html = '<div class="table-responsive">';
        html += '<table class="table table-striped table-bordered">';
        html += '<thead><tr>';
        html += '<th>角色</th>';
        html += '<th>分佣次数</th>';
        html += '<th>总分佣金额</th>';
        html += '<th>已发放金额</th>';
        html += '<th>待发放金额</th>';
        html += '<th>发放率</th>';
        html += '</tr></thead>';
        html += '<tbody>';

        var totalAmount = 0;
        var totalDistributed = 0;

        $.each(data, function(index, item) {
            var pendingAmount = parseFloat(item.total_amount) - parseFloat(item.distributed_amount);
            var distributionRate = item.total_amount > 0 ? ((item.distributed_amount / item.total_amount) * 100).toFixed(2) : 0;

            totalAmount += parseFloat(item.total_amount);
            totalDistributed += parseFloat(item.distributed_amount);

            html += '<tr>';
            html += '<td>' + (item.commission_user_role_text || item.commission_user_role) + '</td>';
            html += '<td>' + item.count + '</td>';
            html += '<td>¥' + parseFloat(item.total_amount).toFixed(2) + '</td>';
            html += '<td>¥' + parseFloat(item.distributed_amount).toFixed(2) + '</td>';
            html += '<td>¥' + pendingAmount.toFixed(2) + '</td>';
            html += '<td>' + distributionRate + '%</td>';
            html += '</tr>';
        });

        // 添加汇总行
        var totalPending = totalAmount - totalDistributed;
        var totalRate = totalAmount > 0 ? ((totalDistributed / totalAmount) * 100).toFixed(2) : 0;

        html += '<tr class="info"><td><strong>汇总</strong></td>';
        html += '<td><strong>' + data.length + '</strong></td>';
        html += '<td><strong>¥' + totalAmount.toFixed(2) + '</strong></td>';
        html += '<td><strong>¥' + totalDistributed.toFixed(2) + '</strong></td>';
        html += '<td><strong>¥' + totalPending.toFixed(2) + '</strong></td>';
        html += '<td><strong>' + totalRate + '%</strong></td></tr>';

        html += '</tbody></table></div>';

        return html;
    }

    function renderOrderTypeStatistics(data) {
        var html = '<div class="table-responsive">';
        html += '<table class="table table-striped table-bordered">';
        html += '<thead><tr>';
        html += '<th>订单类型</th>';
        html += '<th>分佣次数</th>';
        html += '<th>总分佣金额</th>';
        html += '<th>已发放金额</th>';
        html += '<th>待发放金额</th>';
        html += '</tr></thead>';
        html += '<tbody>';

        $.each(data, function(index, item) {
            var pendingAmount = parseFloat(item.total_amount) - parseFloat(item.distributed_amount);
            var orderTypeName = getOrderTypeName(item.order_type);

            html += '<tr>';
            html += '<td>' + orderTypeName + '</td>';
            html += '<td>' + item.count + '</td>';
            html += '<td>¥' + parseFloat(item.total_amount).toFixed(2) + '</td>';
            html += '<td>¥' + parseFloat(item.distributed_amount).toFixed(2) + '</td>';
            html += '<td>¥' + pendingAmount.toFixed(2) + '</td>';
            html += '</tr>';
        });

        html += '</tbody></table></div>';

        return html;
    }

    function renderDailyStatistics(data) {
        var html = '<div class="table-responsive">';
        html += '<table class="table table-striped table-bordered">';
        html += '<thead><tr>';
        html += '<th>日期</th>';
        html += '<th>分佣次数</th>';
        html += '<th>总分佣金额</th>';
        html += '<th>已发放金额</th>';
        html += '<th>待发放金额</th>';
        html += '</tr></thead>';
        html += '<tbody>';

        $.each(data, function(index, item) {
            var pendingAmount = parseFloat(item.total_amount) - parseFloat(item.distributed_amount);

            html += '<tr>';
            html += '<td>' + item.date + '</td>';
            html += '<td>' + item.count + '</td>';
            html += '<td>¥' + parseFloat(item.total_amount).toFixed(2) + '</td>';
            html += '<td>¥' + parseFloat(item.distributed_amount).toFixed(2) + '</td>';
            html += '<td>¥' + pendingAmount.toFixed(2) + '</td>';
            html += '</tr>';
        });

        html += '</tbody></table></div>';

        return html;
    }

    function getOrderTypeName(orderType) {
        var typeMap = {
            'course': '线上课程',
            'offline_course': '线下课程',
            'course_package': '课程套餐',
            'wanlshop': '商城商品',
            'service': '服务订单'
        };

        return typeMap[orderType] || orderType;
    }
});
</script>