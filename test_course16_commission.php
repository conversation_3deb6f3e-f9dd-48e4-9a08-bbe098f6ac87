<?php
/**
 * 测试课程16分佣逻辑
 */

// 模拟订单数据
$order = (object)[
    'id' => 660,
    'user_id' => 2074,
    'total_price' => 365.00,
    'order_no' => 'test_order'
];

// 模拟用户数据
$user = [
    'id' => 2074,
    'parent_id' => 2065,
    'is_sqdl' => 0,
    'is_qydl' => 0
];

$ylgw_user = [
    'id' => 2065,
    'is_ylgw' => 1
];

// 分佣配置
$sq_rate = 50; // 养老院长分佣比例
$qy_rate = 10; // 城市负责人分佣比例
$ylgw_rate = 50; // 养老顾问推荐分佣比例

// 计算分佣金额
function truncateDecimal($number, $decimals = 2) {
    $factor = pow(10, $decimals);
    return floor($number * $factor) / $factor;
}

$sq_money = truncateDecimal($order->total_price * ($sq_rate / 100));
$qy_money = truncateDecimal($order->total_price * ($qy_rate / 100));
$ylgw_money = truncateDecimal($sq_money * ($ylgw_rate / 100));

echo "=== 课程16分佣逻辑测试 ===\n";
echo "订单金额: {$order->total_price}\n";
echo "养老院长分佣比例: {$sq_rate}%\n";
echo "城市负责人分佣比例: {$qy_rate}%\n";
echo "养老顾问分佣比例: {$ylgw_rate}%\n\n";

echo "=== 计算结果 ===\n";
echo "养老院长分佣金额: {$sq_money}\n";
echo "城市负责人分佣金额: {$qy_money}\n";
echo "养老顾问分佣金额: {$ylgw_money}\n\n";

echo "=== 分佣逻辑分析 ===\n";

// 1. 养老院长分佣
echo "1. 养老院长分佣:\n";
if ($sq_money > 0 && $user['parent_id'] > 0) {
    echo "   条件满足: 分佣金额 > 0 且用户有上级\n";
    echo "   需要调用 getOneSq({$order->user_id}) 查找养老院长\n";
    echo "   问题: 用户{$user['parent_id']}是养老顾问，不是养老院长，getOneSq可能返回空\n";
} else {
    echo "   条件不满足\n";
}

// 2. 城市负责人分佣
echo "\n2. 城市负责人分佣:\n";
echo "   依赖于养老院长分佣的结果\n";

// 3. 养老顾问分佣
echo "\n3. 养老顾问分佣:\n";
if ($ylgw_money > 0) {
    echo "   条件满足: 分佣金额 > 0\n";
    echo "   调用 getOneYlgw({$order->user_id}) 查找养老顾问\n";
    echo "   预期返回: 用户{$ylgw_user['id']} (直接上级)\n";
    
    $is_referral_commission = ($user['parent_id'] == $ylgw_user['id']);
    echo "   推荐分佣判断: {$user['parent_id']} == {$ylgw_user['id']} = " . ($is_referral_commission ? 'true' : 'false') . "\n";
    
    if ($is_referral_commission) {
        echo "   结果: 应该直接发放分佣 {$ylgw_money} 元\n";
    } else {
        echo "   结果: 按普通分佣逻辑处理\n";
    }
} else {
    echo "   条件不满足: 分佣金额为 0\n";
}

echo "\n=== 问题分析 ===\n";
echo "1. 养老院长分佣可能失败，因为直接上级是养老顾问\n";
echo "2. 养老顾问分佣应该成功，因为是推荐分佣\n";
echo "3. 需要检查实际的 getOneSq() 和 getOneYlgw() 方法返回值\n";

echo "\n=== 建议修复 ===\n";
echo "1. 检查分佣逻辑是否正确执行\n";
echo "2. 添加调试日志记录分佣过程\n";
echo "3. 验证 getOneYlgw() 方法是否正确返回上级养老顾问\n";
?>
