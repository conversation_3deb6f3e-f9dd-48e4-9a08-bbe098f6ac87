<?php
/**
 * 测试佣金记录接口
 * 用于验证修复后的whereOr语法是否正确
 */

// 模拟ThinkPHP5的whereOr语法测试
echo "=== ThinkPHP5 whereOr语法测试 ===\n\n";

// 错误的语法（会报错）
echo "❌ 错误的语法：\n";
echo '$query->whereOr([
    [\'type\', \'=\', \'pending_commission\'],
    [\'type\', \'=\', \'offline_commission\'],
    [\'type\', \'=\', \'fenyong\', \'memo\', \'like\', \'%线下分佣%\']
]);' . "\n\n";
echo "问题：ThinkPHP5的whereOr不支持这种多维数组格式\n\n";

// 正确的语法
echo "✅ 正确的语法：\n";
echo '$query->where(function($query) {
    $query->where(\'type\', \'pending_commission\')
          ->whereOr(\'type\', \'offline_commission\')
          ->whereOr(function($subQuery) {
              $subQuery->where(\'type\', \'fenyong\')
                       ->where(\'memo\', \'like\', \'%线下分佣%\');
          })
          ->whereOr(function($subQuery) {
              $subQuery->where(\'type\', \'fenyong\')
                       ->where(\'memo\', \'like\', \'%应得分佣%\');
          });
});' . "\n\n";

// 生成的SQL示例
echo "=== 生成的SQL示例 ===\n\n";

echo "有上级的养老顾问查询条件：\n";
echo "SELECT * FROM fa_user_money_log 
WHERE user_id = ? 
AND money > 0 
AND (
    type = 'pending_commission' 
    OR type = 'offline_commission' 
    OR (type = 'fenyong' AND memo LIKE '%线下分佣%')
    OR (type = 'fenyong' AND memo LIKE '%应得分佣%')
)
ORDER BY createtime DESC;\n\n";

echo "无上级的养老顾问查询条件：\n";
echo "SELECT * FROM fa_user_money_log 
WHERE user_id = ? 
AND money > 0 
AND type = 'fenyong' 
AND memo NOT LIKE '%线下分佣%' 
AND memo NOT LIKE '%应得分佣%'
ORDER BY createtime DESC;\n\n";

// 接口测试用例
echo "=== 接口测试用例 ===\n\n";

$test_cases = [
    [
        'name' => '有上级的养老顾问',
        'user_id' => 2065,
        'parent_id' => 2001,
        'expected_records' => [
            'pending_commission',
            'offline_commission',
            'fenyong (memo包含"线下分佣")',
            'fenyong (memo包含"应得分佣")'
        ]
    ],
    [
        'name' => '无上级的养老顾问',
        'user_id' => 2066,
        'parent_id' => 0,
        'expected_records' => [
            'fenyong (memo不包含"线下分佣"和"应得分佣")'
        ]
    ]
];

foreach ($test_cases as $case) {
    echo "测试用例：{$case['name']}\n";
    echo "用户ID：{$case['user_id']}\n";
    echo "上级ID：{$case['parent_id']}\n";
    echo "预期记录类型：\n";
    foreach ($case['expected_records'] as $record) {
        echo "  - {$record}\n";
    }
    echo "\n";
}

// API调用示例
echo "=== API调用示例 ===\n\n";

echo "1. 获取佣金记录列表：\n";
echo "GET /api/ylgw/commissionRecords?page=1&limit=20\n";
echo "Headers: Authorization: Bearer {token}\n\n";

echo "2. 获取佣金概览：\n";
echo "GET /api/ylgw/commissionOverview\n";
echo "Headers: Authorization: Bearer {token}\n\n";

// 错误排查指南
echo "=== 错误排查指南 ===\n\n";

echo "如果仍然报错，请检查：\n";
echo "1. ThinkPHP版本是否为5.x\n";
echo "2. 数据库表结构是否正确\n";
echo "3. 字段名是否存在\n";
echo "4. 语法是否符合ThinkPHP5规范\n\n";

echo "常见错误及解决方案：\n";
echo "❌ 查询表达式错误:type\n";
echo "   原因：whereOr语法不正确\n";
echo "   解决：使用正确的whereOr语法\n\n";

echo "❌ 字段不存在\n";
echo "   原因：数据库表中没有对应字段\n";
echo "   解决：检查表结构，确保字段存在\n\n";

echo "❌ 语法错误\n";
echo "   原因：SQL语法不符合规范\n";
echo "   解决：检查查询条件的语法\n\n";

// 数据验证SQL
echo "=== 数据验证SQL ===\n\n";

echo "检查用户的分佣记录：\n";
echo "SELECT 
    id,
    user_id,
    type,
    money,
    memo,
    createtime,
    FROM_UNIXTIME(createtime) as create_date
FROM fa_user_money_log 
WHERE user_id = 2065 
AND money > 0 
ORDER BY createtime DESC 
LIMIT 10;\n\n";

echo "检查记录类型分布：\n";
echo "SELECT 
    type,
    COUNT(*) as count,
    SUM(money) as total_money
FROM fa_user_money_log 
WHERE user_id = 2065 
AND money > 0 
GROUP BY type;\n\n";

echo "检查备注关键词：\n";
echo "SELECT 
    memo,
    COUNT(*) as count
FROM fa_user_money_log 
WHERE user_id = 2065 
AND money > 0 
AND type = 'fenyong'
GROUP BY memo;\n\n";

echo "=== 修复完成 ===\n";
echo "现在接口应该可以正常工作了！\n";

?>
