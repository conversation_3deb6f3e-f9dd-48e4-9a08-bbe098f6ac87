-- 服务者APP新接口相关SQL

-- 1. 创建协议模板配置表（如果不存在）
CREATE TABLE IF NOT EXISTS `fa_service_agreement_template` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `name` varchar(50) NOT NULL DEFAULT '' COMMENT '模板名称',
  `type` varchar(20) NOT NULL DEFAULT '' COMMENT '模板类型',
  `image` varchar(255) NOT NULL DEFAULT '' COMMENT '模板图片路径',
  `width` int(11) NOT NULL DEFAULT 0 COMMENT '图片宽度',
  `height` int(11) NOT NULL DEFAULT 0 COMMENT '图片高度',
  `signature_x` int(11) NOT NULL DEFAULT 0 COMMENT '签名X坐标',
  `signature_y` int(11) NOT NULL DEFAULT 0 COMMENT '签名Y坐标',
  `signature_width` int(11) NOT NULL DEFAULT 0 COMMENT '签名区域宽度',
  `signature_height` int(11) NOT NULL DEFAULT 0 COMMENT '签名区域高度',
  `status` enum('normal','hidden') NOT NULL DEFAULT 'normal' COMMENT '状态',
  `createtime` int(10) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `updatetime` int(10) NOT NULL DEFAULT 0 COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `type` (`type`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='协议模板配置表';

-- 2. 插入默认协议模板数据
INSERT INTO `fa_service_agreement_template` (`name`, `type`, `image`, `width`, `height`, `signature_x`, `signature_y`, `signature_width`, `signature_height`, `status`, `createtime`, `updatetime`) VALUES
('默认服务协议', 'default', '/assets/img/agreement/default_agreement.png', 750, 1000, 0, 950, 750, 50, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('服务协议模板', 'service', '/assets/img/agreement/service_agreement.png', 750, 1000, 0, 950, 750, 50, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
('隐私协议模板', 'privacy', '/assets/img/agreement/privacy_agreement.png', 750, 1000, 0, 950, 750, 50, 'normal', UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 3. 为服务者APP版本管理表添加字段（如果不存在）
ALTER TABLE `fa_wanlshop_version` 
ADD COLUMN IF NOT EXISTS `app_type` enum('user','service','shop') NOT NULL DEFAULT 'user' COMMENT 'APP类型:user=用户端,service=服务者端,shop=商家端' AFTER `type`;

-- 4. 插入服务者APP版本示例数据
INSERT INTO `fa_wanlshop_version` (`title`, `versionName`, `versionCode`, `content`, `androidLink`, `iosLink`, `packgesize`, `type`, `app_type`, `enforce`, `createtime`, `updatetime`) VALUES
('服务者APP v1.0.0', 'v1.0.0', 100, '1. 新增手签板签名功能\n2. 优化订单搜索体验\n3. 修复已知问题', 'https://example.com/service-app-v1.0.0.apk', 'https://example.com/service-app-v1.0.0.wgt', '25.6MB', 'release', 'service', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 5. 为订单表添加搜索索引（如果不存在）
ALTER TABLE `fa_service_order` 
ADD INDEX IF NOT EXISTS `idx_orderid` (`orderId`),
ADD INDEX IF NOT EXISTS `idx_createtime` (`createtime`),
ADD INDEX IF NOT EXISTS `idx_starttime` (`starttime`),
ADD INDEX IF NOT EXISTS `idx_payprice` (`payprice`),
ADD INDEX IF NOT EXISTS `idx_status_createtime` (`status`, `createtime`);

-- 6. 创建签名记录表
CREATE TABLE IF NOT EXISTS `fa_service_signature_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `order_id` int(11) NOT NULL DEFAULT 0 COMMENT '订单ID',
  `template_type` varchar(20) NOT NULL DEFAULT '' COMMENT '模板类型',
  `signature_image` varchar(255) NOT NULL DEFAULT '' COMMENT '签名图片',
  `agreement_image` varchar(255) NOT NULL DEFAULT '' COMMENT '协议图片',
  `merged_image` varchar(255) NOT NULL DEFAULT '' COMMENT '合成后图片',
  `ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(500) NOT NULL DEFAULT '' COMMENT '用户代理',
  `createtime` int(10) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `order_id` (`order_id`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='签名记录表';

-- 7. 创建APP更新日志表
CREATE TABLE IF NOT EXISTS `fa_service_app_update_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `platform` varchar(20) NOT NULL DEFAULT '' COMMENT '平台类型',
  `old_version` varchar(20) NOT NULL DEFAULT '' COMMENT '旧版本号',
  `new_version` varchar(20) NOT NULL DEFAULT '' COMMENT '新版本号',
  `update_type` enum('check','download','install') NOT NULL DEFAULT 'check' COMMENT '更新类型',
  `ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(500) NOT NULL DEFAULT '' COMMENT '用户代理',
  `createtime` int(10) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `platform` (`platform`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='APP更新日志表';

-- 8. 为用户表添加搜索索引（如果不存在）
ALTER TABLE `fa_user` 
ADD INDEX IF NOT EXISTS `idx_mobile` (`mobile`),
ADD INDEX IF NOT EXISTS `idx_nickname` (`nickname`);

-- 9. 为服务者表添加搜索索引（如果不存在）
ALTER TABLE `fa_service_skill` 
ADD INDEX IF NOT EXISTS `idx_name` (`name`);

-- 10. 为商家表添加搜索索引（如果不存在）
ALTER TABLE `fa_service_shop` 
ADD INDEX IF NOT EXISTS `idx_name` (`name`);

-- 11. 为服务项目表添加搜索索引（如果不存在）
ALTER TABLE `fa_service_goods` 
ADD INDEX IF NOT EXISTS `idx_name` (`name`);

-- 12. 创建订单搜索日志表
CREATE TABLE IF NOT EXISTS `fa_service_order_search_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
  `search_params` text COMMENT '搜索参数JSON',
  `result_count` int(11) NOT NULL DEFAULT 0 COMMENT '搜索结果数量',
  `search_time` decimal(10,3) NOT NULL DEFAULT 0.000 COMMENT '搜索耗时(秒)',
  `ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP地址',
  `user_agent` varchar(500) NOT NULL DEFAULT '' COMMENT '用户代理',
  `createtime` int(10) NOT NULL DEFAULT 0 COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单搜索日志表';

-- 13. 插入配置项
INSERT INTO `fa_config` (`name`, `group`, `title`, `tip`, `type`, `value`, `content`, `rule`, `extend`) VALUES
('service_signature_enable', 'service', '启用签名功能', '是否启用手签板签名功能', 'radio', '1', '[\"1\",\"0\"]', '', ''),
('service_signature_max_size', 'service', '签名图片最大尺寸', '签名图片最大文件大小(MB)', 'number', '5', '', 'require|number', ''),
('service_app_update_check', 'service', '启用APP更新检查', '是否启用APP版本更新检查', 'radio', '1', '[\"1\",\"0\"]', '', ''),
('service_order_search_log', 'service', '记录订单搜索日志', '是否记录订单搜索操作日志', 'radio', '1', '[\"1\",\"0\"]', '', '');

-- 注意事项：
-- 1. 请确保在 public/assets/img/agreement/ 目录下放置对应的协议模板图片
-- 2. 协议模板图片建议尺寸为 750x1000 像素
-- 3. 签名区域默认位置为图片底部，距离底部50像素
-- 4. 如果使用OSS存储，请确保OSS配置正确
-- 5. 建议定期清理临时文件和日志记录
