<?php

namespace app\api\controller;

use app\common\controller\Api;
use app\common\model\User;
use app\common\model\YlgwWithdraw;
use app\common\model\MoneyLog;
use think\Db;
use think\Config;

/**
 * 养老顾问对账接口
 */
class Ylgw extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = '*';

    /**
     * 获取对账信息
     */
    public function accountInfo()
    {
        $user = $this->auth->getUserInfo();
        
        // 检查是否为养老顾问
        if ($user['is_ylgw'] != 1) {
            $this->error('您不是养老顾问');
        }
        
        // 检查是否有上级且上级不是平台
        if (!$user['parent_id'] || $user['parent_id'] == 1) {
            $this->error('您没有上级或上级为平台，无法使用对账功能');
        }
        
        // 获取上级信息
        $parent = User::field('id,username,nickname,mobile,avatar')->find($user['parent_id']);
        
        // 计算佣金统计
        $totalCommission = $user['ylgw_total_commission'] ?: 0;
        $withdrawnAmount = $user['ylgw_withdraw_amount'] ?: 0;
        $availableAmount = YlgwWithdraw::getAvailableAmount($user['id']);
        
        // 获取最近的佣金记录
        $commissionRecords = MoneyLog::where([
            'user_id' => $user['id'],
            'type' => 'fenyong'
        ])
        ->where('money', '>', 0)
        ->order('createtime desc')
        ->limit(10)
        ->select();
        
        $data = [
            'parent_info' => $parent,
            'commission_stats' => [
                'total_commission' => $totalCommission,
                'withdrawn_amount' => $withdrawnAmount,
                'available_amount' => $availableAmount
            ],
            'recent_records' => $commissionRecords
        ];
        
        $this->success('获取成功', $data);
    }
    
    /**
     * 申请提现
     */
    public function applyWithdraw()
    {
        $money = $this->request->post('money', 0);
        
        if ($money <= 0) {
            $this->error('提现金额必须大于0');
        }
       
        $withdraw = YlgwWithdraw::applyWithdraw($this->auth->id, $money);
        if($withdraw===true){
            $this->success('申请提现成功，请等待上级审核');
        }else{
            $this->error($withdraw);
        }
     
    }
    
    /**
     * 获取提现申请列表
     */
    public function withdrawList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        
        $list = YlgwWithdraw::where('user_id', $this->auth->id)
            ->with(['parent' => function($query) {
                $query->field('id,username,nickname,mobile,avatar');
            }])
            ->order('createtime desc')
            ->paginate($limit, false, ['page' => $page]);
            
        $this->success('获取成功', $list);
    }
    
    /**
     * 获取下级提现申请列表（上级查看）
     */
    public function subordinateWithdrawList()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        $status = $this->request->get('status', '');
        
        $where = ['parent_id' => $this->auth->id];
        if ($status !== '') {
            $where['status'] = $status;
        }
        
        $list = YlgwWithdraw::where($where)
            ->with(['user' => function($query) {
                $query->field('id,username,nickname,mobile,avatar');
            }])
            ->order('createtime desc')
            ->paginate($limit, false, ['page' => $page]);
            
        $this->success('获取成功', $list);
    }
    
    /**
     * 审核提现申请
     */
    public function auditWithdraw()
    {
        $withdraw_id = $this->request->post('withdraw_id');
        $status = $this->request->post('status'); // 1=通过, 2=拒绝
        $transfer_amount = $this->request->post('transfer_amount', 0);
        $transfer_image = $this->request->post('transfer_image', '');
        $remark = $this->request->post('remark', '');
        $reject_reason = $this->request->post('reject_reason', '');
        
        if (!$withdraw_id) {
            $this->error('提现申请ID不能为空');
        }
        
        if (!in_array($status, [1, 2])) {
            $this->error('审核状态错误');
        }
        
        $withdraw = YlgwWithdraw::find($withdraw_id);
        if (!$withdraw) {
            $this->error('提现申请不存在');
        }
        
        // 检查权限
        if ($withdraw->parent_id != $this->auth->id) {
            $this->error('您没有权限审核此申请');
        }
        
        $result=$withdraw->auditWithdraw($status, $transfer_amount, $transfer_image, $remark, $reject_reason);
        if($result===true){
            $this->success('审核成功');
        }else{
            $this->error($result);
        }
    }
    
    /**
     * 获取佣金记录
     */
    public function commissionRecords()
    {
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 20);
        $start_time = $this->request->get('start_time', '');
        $end_time = $this->request->get('end_time', '');

        $user = $this->auth->getUserInfo();

        // 检查用户是否有上级（且上级不是平台）
        $has_superior = false;
        if ($user['parent_id'] && $user['parent_id'] != 1) {
            $parent = User::where('id', $user['parent_id'])->field('is_sqdl,is_qydl')->find();
            if ($parent && ($parent['is_sqdl'] == 1 || $parent['is_qydl'] == 1)) {
                $has_superior = true;
            }
        }

        $query = MoneyLog::where('user_id', $this->auth->id)->where('money', '>', 0);

        if ($has_superior) {
            // 有上级：显示应得分佣记录（待线下分账）
            $query->where(function($query) {
                $query->where('type', 'pending_commission') // 新的应得分佣类型
                      ->whereOr('type', 'offline_commission') // 兼容之前的类型
                      ->whereOr(function($subQuery) {
                          $subQuery->where('type', 'fenyong')
                                   ->where('memo', 'like', '%线下分佣%');
                      })
                      ->whereOr(function($subQuery) {
                          $subQuery->where('type', 'fenyong')
                                   ->where('memo', 'like', '%应得分佣%');
                      });
            });
        } else {
            // 无上级：只显示已进入余额的分佣记录
            $query->where('type', 'fenyong')
                  ->where('memo', 'not like', '%线下分佣%')
                  ->where('memo', 'not like', '%应得分佣%');
        }

        if ($start_time) {
            $query->where('createtime', '>=', strtotime($start_time));
        }

        if ($end_time) {
            $query->where('createtime', '<=', strtotime($end_time . ' 23:59:59'));
        }

        $list = $query->order('createtime desc')
            ->paginate($limit, false, ['page' => $page]);

        // 为每条记录添加状态说明
        $items = $list->items();
        foreach ($items as &$item) {
            if ($has_superior) {
                // 有上级的用户：显示应得分佣状态
                $item['commission_type'] = 'pending';
                $item['commission_status'] = '待线下分账';
                $item['status_desc'] = '此佣金已分给上级，您可通过对账系统申请线下分账';

                // 根据记录类型显示更详细的状态
                if ($item['type'] == 'pending_commission') {
                    $item['detail_status'] = '已分给上级，等待线下分账';
                } else {
                    $item['detail_status'] = '应得分佣记录';
                }
            } else {
                // 无上级的用户：显示已到账状态
                $item['commission_type'] = 'received';
                $item['commission_status'] = '已到账';
                $item['status_desc'] = '此佣金已直接进入您的账户余额，可直接提现';
                $item['detail_status'] = '已到账';
            }

            // 格式化时间
            $item['createtime_formatted'] = date('Y-m-d H:i:s', $item['createtime']);

            // 格式化金额
            $item['money_formatted'] = number_format($item['money'], 2);
        }

        $result = [
            'list' => $items,
            'total' => $list->total(),
            'page' => $page,
            'limit' => $limit,
            'has_superior' => $has_superior,
            'commission_type' => $has_superior ? 'offline' : 'online',
            'type_desc' => $has_superior ? '线下分佣记录' : '在线分佣记录'
        ];

        $this->success('获取成功', $result);
    }
    
    /**
     * 获取佣金统计概览
     */
    public function commissionOverview()
    {
        $user = $this->auth->getUserInfo();

        // 检查用户是否有上级（且上级不是平台）
        $has_superior = false;
        $parent_info = null;
        if ($user['parent_id'] && $user['parent_id'] != 1) {
            $parent = User::where('id', $user['parent_id'])->field('id,nickname,mobile,is_sqdl,is_qydl')->find();
            if ($parent && ($parent['is_sqdl'] == 1 || $parent['is_qydl'] == 1)) {
                $has_superior = true;
                $parent_info = [
                    'id' => $parent['id'],
                    'nickname' => $parent['nickname'],
                    'mobile' => $parent['mobile'],
                    'role' => $parent['is_sqdl'] == 1 ? '养老院长' : '城市负责人'
                ];
            }
        }

        if ($has_superior) {
            // 有上级：统计线下分佣
            $total_offline_commission = $user['ylgw_total_commission'] ?: 0;
            $withdrawn_amount = $user['ylgw_withdraw_amount'] ?: 0;
            $available_amount = $total_offline_commission - $withdrawn_amount;

            // 统计线下分佣记录数量
            $offline_records_count = MoneyLog::where('user_id', $user['id'])
                ->where(function($query) {
                    $query->where('type', 'pending_commission') // 新的应得分佣类型
                          ->whereOr('type', 'offline_commission') // 兼容之前的类型
                          ->whereOr(function($subQuery) {
                              $subQuery->where('type', 'fenyong')
                                       ->where('memo', 'like', '%线下分佣%');
                          })
                          ->whereOr(function($subQuery) {
                              $subQuery->where('type', 'fenyong')
                                       ->where('memo', 'like', '%应得分佣%');
                          });
                })
                ->count();

            $data = [
                'commission_type' => 'offline',
                'type_desc' => '线下分佣模式',
                'has_superior' => true,
                'parent_info' => $parent_info,
                'stats' => [
                    'total_commission' => $total_offline_commission,
                    'withdrawn_amount' => $withdrawn_amount,
                    'available_amount' => $available_amount,
                    'records_count' => $offline_records_count
                ],
                'description' => '您的佣金需要通过对账系统申请给上级，由上级线下转账给您'
            ];
        } else {
            // 无上级：统计在线分佣
            $online_commission = MoneyLog::where([
                'user_id' => $user['id'],
                'type' => 'fenyong'
            ])
            ->where('memo', 'not like', '%线下分佣%')
            ->where('money', '>', 0)
            ->sum('money');

            $online_records_count = MoneyLog::where([
                'user_id' => $user['id'],
                'type' => 'fenyong'
            ])
            ->where('memo', 'not like', '%线下分佣%')
            ->where('money', '>', 0)
            ->count();

            $data = [
                'commission_type' => 'online',
                'type_desc' => '在线分佣模式',
                'has_superior' => false,
                'parent_info' => null,
                'stats' => [
                    'total_commission' => $online_commission,
                    'current_balance' => $user['money'] ?: 0,
                    'records_count' => $online_records_count
                ],
                'description' => '您的佣金直接进入账户余额，可以直接提现'
            ];
        }

        $this->success('获取成功', $data);
    }

    /**
     * 获取上级佣金统计
     */
    public function parentCommissionStats()
    {
        $user = $this->auth->getUserInfo();

        if (!$user['parent_id'] || $user['parent_id'] == 1) {
            $this->error('您没有上级或上级为平台');
        }

        // 获取上级信息
        $parent = User::find($user['parent_id']);
        if (!$parent) {
            $this->error('上级用户不存在');
        }

        // 计算上级从当前用户获得的佣金
        $parentCommission = MoneyLog::where([
            'user_id' => $user['parent_id'],
            'type' => 'fenyong'
        ])
        ->where('memo', 'like', '%' . $user['id'] . '%')
        ->where('money', '>', 0)
        ->sum('money');
        
        $data = [
            'parent_info' => [
                'id' => $parent->id,
                'username' => $parent->username,
                'nickname' => $parent->nickname,
                'mobile' => $parent->mobile,
                'avatar' => $parent->avatar
            ],
            'commission_from_me' => $parentCommission ?: 0
        ];
        
        $this->success('获取成功', $data);
    }
}
