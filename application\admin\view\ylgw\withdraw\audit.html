<form id="audit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">

    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">申请用户:</label>
        <div class="col-xs-12 col-sm-8">
            <p class="form-control-static">{$row.user.nickname|default=$row.user.username} ({$row.user.mobile})</p>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">申请金额:</label>
        <div class="col-xs-12 col-sm-8">
            <p class="form-control-static">¥{$row.money}</p>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">申请时间:</label>
        <div class="col-xs-12 col-sm-8">
            <p class="form-control-static">{$row.createtime_text}</p>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">审核状态:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="radio">
                <label for="row[status]-1"><input id="row[status]-1" name="row[status]" type="radio" value="1" data-rule="required" /> 审核通过</label>
            </div>
            <div class="radio">
                <label for="row[status]-2"><input id="row[status]-2" name="row[status]" type="radio" value="2" data-rule="required" /> 拒绝申请</label>
            </div>
        </div>
    </div>
    
    <div class="form-group" id="transfer-fields" style="display:none;">
        <label class="control-label col-xs-12 col-sm-2">转账金额:</label>
        <div class="col-xs-12 col-sm-8">
            <input id="c-transfer_amount" class="form-control" step="0.01" name="row[transfer_amount]" type="number" value="{$row.money}">
        </div>
    </div>
    
    <div class="form-group" id="transfer-image-field" style="display:none;">
        <label class="control-label col-xs-12 col-sm-2">转账截图:</label>
        <div class="col-xs-12 col-sm-8">
            <div class="input-group">
                <input id="c-transfer_image" class="form-control" size="50" name="row[transfer_image]" type="text" value="">
                <div class="input-group-addon no-border no-padding">
                    <span><button type="button" id="plupload-transfer_image" class="btn btn-danger plupload" data-input-id="c-transfer_image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-transfer_image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                    <span><button type="button" id="fachoose-transfer_image" class="btn btn-primary fachoose" data-input-id="c-transfer_image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                </div>
                <span class="msg-box n-right" for="c-transfer_image"></span>
            </div>
            <ul class="row list-inline plupload-preview" id="p-transfer_image"></ul>
        </div>
    </div>
    
    <div class="form-group">
        <label class="control-label col-xs-12 col-sm-2">备注:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-remark" class="form-control" rows="3" name="row[remark]" cols="50"></textarea>
        </div>
    </div>
    
    <div class="form-group" id="reject-reason-field" style="display:none;">
        <label class="control-label col-xs-12 col-sm-2">拒绝原因:</label>
        <div class="col-xs-12 col-sm-8">
            <textarea id="c-reject_reason" class="form-control" rows="3" name="row[reject_reason]" cols="50"></textarea>
        </div>
    </div>
    
    <div class="form-group layer-footer">
        <label class="control-label col-xs-12 col-sm-2"></label>
        <div class="col-xs-12 col-sm-8">
            <button type="submit" class="btn btn-success btn-embossed disabled">{:__('OK')}</button>
            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
        </div>
    </div>
</form>

<script>
$(function() {
    // 监听审核状态变化
    $('input[name="row[status]"]').change(function() {
        var status = $(this).val();
        if (status == '1') {
            $('#transfer-fields').show();
            $('#transfer-image-field').show();
            $('#reject-reason-field').hide();
            $('#c-transfer_amount').attr('data-rule', 'required');
            $('#c-reject_reason').removeAttr('data-rule');
        } else if (status == '2') {
            $('#transfer-fields').hide();
            $('#transfer-image-field').hide();
            $('#reject-reason-field').show();
            $('#c-transfer_amount').removeAttr('data-rule');
            $('#c-reject_reason').attr('data-rule', 'required');
        }
    });
});
</script>
