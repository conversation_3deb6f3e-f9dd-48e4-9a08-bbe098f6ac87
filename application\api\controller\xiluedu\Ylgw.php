<?php

namespace app\api\controller\xiluedu;

use app\common\controller\Api;
use app\common\library\Sms;
use app\common\model\MoneyLog;
use app\common\model\User;
use app\common\model\xiluedu\Course;
use app\common\model\xiluedu\CourseOrder;
use app\common\model\xiluedu\UserCourse;
use think\Db;
use think\Exception;
use think\Validate;

/**
 * 养老顾问管理控制器
 */
class Ylgw extends Api
{
    protected $noNeedLogin = [];
    protected $noNeedRight = ['*'];

    /**
     * 绑定下级用户为养老顾问
     * @ApiTitle (绑定下级用户为养老顾问)
     * @ApiRoute (/api/xiluedu.ylgw/bind_user)
     * @ApiMethod (POST)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     * @ApiParams (name="name", type="string", required=true, description="姓名")
     */
    public function bind_user()
    {
        $mobile = $this->request->post('mobile');
        $name = $this->request->post('name');
        
        if (!$mobile || !$name) {
            $this->error('手机号和姓名不能为空');
        }
        
        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error('手机号格式错误');
        }
        
        $current_user = $this->auth->getUser();
        
        // 检查当前用户是否为养老顾问
        if ($current_user->is_ylgw != 1) {
            $this->error('您不是养老顾问，无法绑定下级用户');
        }
        
        // 查找目标用户
        $target_user = User::where('mobile', $mobile)->find();
        if (!$target_user) {
            $this->error('未找到该手机号对应的用户');
        }
        
        // 检查目标用户是否已经有上级
        if ($target_user->parent_id != 0) {
            $this->error('该用户已有上级，无法重复绑定');
        }
        
        // 检查目标用户是否已经是养老顾问或更高级别
        if ($target_user->is_ylgw == 1 || $target_user->is_sqdl == 1 || $target_user->is_qydl == 1) {
            $this->error('该用户已是养老顾问或更高级别，无法绑定');
        }
        
        Db::startTrans();
        try {
            // 绑定上下级关系
            $target_user->parent_id = $current_user->id;
            $target_user->save();
            
            Db::commit();
            $this->success('绑定成功');
            
        } catch (Exception $e) {
            Db::rollback();
            $this->error('绑定失败：' . $e->getMessage());
        }
    }

    /**
     * 获取下级用户列表
     * @ApiTitle (获取下级用户列表)
     * @ApiRoute (/api/xiluedu.ylgw/sub_users)
     * @ApiMethod (GET)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     * @ApiParams (name="page", type="int", required=false, description="页码")
     * @ApiParams (name="limit", type="int", required=false, description="每页数量")
     */
    public function sub_users()
    {
        $current_user = $this->auth->getUser();
        
        // 检查当前用户是否为养老顾问
        if ($current_user->is_ylgw != 1) {
            $this->error('您不是养老顾问');
        }
        
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        
        // 获取直接下级用户（非养老顾问、非养老院长、非城市负责人）
        $list = User::where('parent_id', $current_user->id)
            ->where('is_ylgw', 0)
            ->where('is_sqdl', 0)
            ->where('is_qydl', 0)
            ->field('id,nickname,mobile,avatar,createtime,money')
            ->order('createtime desc')
            ->paginate($limit, false, ['page' => $page]);
        
        foreach ($list as &$user) {
            $user->avatar = $user->avatar ? cdnurl($user->avatar, true) : '';
            $user->createtime_text = date('Y-m-d H:i:s', $user->createtime);
            $user->mobile = substr_replace($user->mobile, '****', 3, 4);
        }
        
        $this->success('查询成功', $list);
    }

    /**
     * 获取收益统计
     * @ApiTitle (获取收益统计)
     * @ApiRoute (/api/xiluedu.ylgw/income_stats)
     * @ApiMethod (GET)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     * @ApiParams (name="date", type="string", required=false, description="统计日期类型：today,week,month")
     */
    public function income_stats()
    {
        $current_user = $this->auth->getUser();
        
        // 检查当前用户是否为养老顾问
        if ($current_user->is_ylgw != 1) {
            $this->error('您不是养老顾问');
        }
        
        $date = $this->request->get('date', 'today');
        
        // 计算时间范围
        switch ($date) {
            case 'today':
                $start_time = strtotime(date('Y-m-d'));
                $end_time = strtotime(date('Y-m-d 23:59:59'));
                break;
            case 'week':
                $start_time = strtotime('-7 days');
                $end_time = time();
                break;
            case 'month':
                $start_time = strtotime('-30 days');
                $end_time = time();
                break;
            default:
                $start_time = strtotime(date('Y-m-d'));
                $end_time = strtotime(date('Y-m-d 23:59:59'));
        }
        
        // 统计收益
        $income = MoneyLog::where('user_id', $current_user->id)
            ->where('type', 'fenyong')
            ->where('memo', 'like', '%养老顾问分成%')
            ->where('createtime', 'between', [$start_time, $end_time])
            ->sum('money');
        
        // 统计总收益
        $total_income = MoneyLog::where('user_id', $current_user->id)
            ->where('type', 'fenyong')
            ->where('memo', 'like', '%养老顾问分成%')
            ->sum('money');
        
        // 统计下级用户数量
        $sub_user_count = User::where('parent_id', $current_user->id)
            ->where('is_ylgw', 0)
            ->where('is_sqdl', 0)
            ->where('is_qydl', 0)
            ->count();
        
        $data = [
            'period_income' => $income ?: 0,
            'total_income' => $total_income ?: 0,
            'sub_user_count' => $sub_user_count,
            'current_balance' => $current_user->money,
            'date_type' => $date
        ];
        
        $this->success('查询成功', $data);
    }

    /**
     * 获取收益明细
     * @ApiTitle (获取收益明细)
     * @ApiRoute (/api/xiluedu.ylgw/income_detail)
     * @ApiMethod (GET)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     * @ApiParams (name="page", type="int", required=false, description="页码")
     * @ApiParams (name="limit", type="int", required=false, description="每页数量")
     */
    public function income_detail()
    {
        $current_user = $this->auth->getUser();
        
        // 检查当前用户是否为养老顾问
        if ($current_user->is_ylgw != 1) {
            $this->error('您不是养老顾问');
        }
        
        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);
        
        $list = MoneyLog::alias('a')
            ->field('a.*,c.nickname,c.avatar')
            ->join('xiluedu_divide_money b', 'b.money_log_id=a.id', 'LEFT')
            ->join('user c', 'c.id=b.buy_user_id', "LEFT")
            ->where('a.user_id', $current_user->id)
            ->where('a.type', 'fenyong')
            ->where('a.memo', 'like', '%养老顾问分成%')
            ->order('a.id', 'desc')
            ->paginate($limit, false, ['page' => $page]);
        
        foreach ($list as &$item) {
            $item->avatar = $item->avatar ? cdnurl($item->avatar, true) : '';
            $item->createtime_text = date("Y-m-d H:i:s", $item->createtime);
        }
        
        $this->success('查询成功', $list);
    }

    /**
     * 开通下级用户为养老顾问
     * @ApiTitle (开通下级用户为养老顾问)
     * @ApiRoute (/api/xiluedu.ylgw/upgrade_user)
     * @ApiMethod (POST)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     * @ApiParams (name="user_id", type="int", required=true, description="用户ID")
     */
    public function upgrade_user()
    {
        $user_id = $this->request->post('user_id');
        
        if (!$user_id) {
            $this->error('用户ID不能为空');
        }
        
        $current_user = $this->auth->getUser();
        
        // 检查当前用户是否为养老顾问
        if ($current_user->is_ylgw != 1) {
            $this->error('您不是养老顾问，无法开通下级用户');
        }
        
        // 查找目标用户
        $target_user = User::where('id', $user_id)->find();
        if (!$target_user) {
            $this->error('用户不存在');
        }
        
        // 检查是否为下级用户
        if ($target_user->parent_id != $current_user->id) {
            $this->error('该用户不是您的下级用户');
        }
        
        // 检查目标用户是否已经是养老顾问
        if ($target_user->is_ylgw == 1) {
            $this->error('该用户已经是养老顾问');
        }
        
        // 获取课程16的价格
        $course = Course::where('id', 16)->find();
        if (!$course) {
            $this->error('养老顾问课程不存在');
        }
        
        // 检查当前用户余额是否充足
        if ($current_user->money < $course->salesprice) {
            $this->error('余额不足，需要' . $course->salesprice . '元');
        }
        
        Db::startTrans();
        try {
            // 扣除当前用户余额
            User::money(-$course->salesprice, $current_user->id, '开通下级养老顾问-' . $target_user->nickname, 1, '', 'open_ylgw');
            
            // 开通目标用户的养老顾问身份
            $target_user->is_ylgw = 1;
            $target_user->save();
            
            // 创建课程订单记录
            $order_data = [
                'platform' => 'ylgw',
                'user_id' => $target_user->id,
                'order_no' => "Y" . date("YmdHis") . mt_rand(10, 9999),
                'order_trade_no' => "YO" . date("YmdHis") . mt_rand(10, 9999),
                'course_id' => 16,
                'total_price' => $course->salesprice,
                'pay_price' => $course->salesprice,
                'pay_status' => 2,
                'paytime' => time(),
                'ip' => request()->ip(),
                'is_service' => 0,
            ];
            
            CourseOrder::create($order_data);
            
            // 创建用户课程关联
            UserCourse::create([
                'user_id' => $target_user->id,
                'course_id' => 16,
            ]);
            
            Db::commit();
            $this->success('开通养老顾问身份成功');

        } catch (Exception $e) {
            Db::rollback();
            $this->error('开通失败：' . $e->getMessage());
        }
    }

    /**
     * 获取养老顾问信息
     * @ApiTitle (获取养老顾问信息)
     * @ApiRoute (/api/xiluedu.ylgw/info)
     * @ApiMethod (GET)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     */
    public function info()
    {
        $current_user = $this->auth->getUser();

        // 检查当前用户是否为养老顾问
        if ($current_user->is_ylgw != 1) {
            $this->error('您不是养老顾问');
        }

        // 统计数据
        $sub_user_count = User::where('parent_id', $current_user->id)
            ->where('is_ylgw', 0)
            ->where('is_sqdl', 0)
            ->where('is_qydl', 0)
            ->count();

        $total_income = MoneyLog::where('user_id', $current_user->id)
            ->where('type', 'fenyong')
            ->where('memo', 'like', '%养老顾问分成%')
            ->sum('money');

        $today_income = MoneyLog::where('user_id', $current_user->id)
            ->where('type', 'fenyong')
            ->where('memo', 'like', '%养老顾问分成%')
            ->where('createtime', 'between', [strtotime(date('Y-m-d')), strtotime(date('Y-m-d 23:59:59'))])
            ->sum('money');

        $data = [
            'user_info' => [
                'id' => $current_user->id,
                'nickname' => $current_user->nickname,
                'mobile' => $current_user->mobile,
                'avatar' => $current_user->avatar ? cdnurl($current_user->avatar, true) : '',
                'money' => $current_user->money,
                'is_ylgw' => $current_user->is_ylgw,
            ],
            'stats' => [
                'sub_user_count' => $sub_user_count,
                'total_income' => $total_income ?: 0,
                'today_income' => $today_income ?: 0,
            ]
        ];

        $this->success('查询成功', $data);
    }

    /**
     * 搜索用户（用于绑定）
     * @ApiTitle (搜索用户)
     * @ApiRoute (/api/xiluedu.ylgw/search_user)
     * @ApiMethod (GET)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     * @ApiParams (name="mobile", type="string", required=true, description="手机号")
     */
    public function search_user()
    {
        $mobile = $this->request->get('mobile');

        if (!$mobile) {
            $this->error('手机号不能为空');
        }

        if (!Validate::regex($mobile, "^1\d{10}$")) {
            $this->error('手机号格式错误');
        }

        $current_user = $this->auth->getUser();

        // 检查当前用户是否为养老顾问
        if ($current_user->is_ylgw != 1) {
            $this->error('您不是养老顾问');
        }

        // 查找用户
        $user = User::where('mobile', $mobile)
            ->field('id,nickname,mobile,avatar,createtime,parent_id,is_ylgw,is_sqdl,is_qydl')
            ->find();

        if (!$user) {
            $this->error('未找到该用户');
        }

        // 检查用户状态
        $status = '';
        if ($user->parent_id != 0) {
            $status = '已有上级';
        } elseif ($user->is_qydl == 1) {
            $status = '城市负责人';
        } elseif ($user->is_sqdl == 1) {
            $status = '养老院长';
        } elseif ($user->is_ylgw == 1) {
            $status = '养老顾问';
        } else {
            $status = '可绑定';
        }

        $user->avatar = $user->avatar ? cdnurl($user->avatar, true) : '';
        $user->createtime_text = date('Y-m-d H:i:s', $user->createtime);
        $user->status = $status;
        $user->can_bind = ($user->parent_id == 0 && $user->is_ylgw == 0 && $user->is_sqdl == 0 && $user->is_qydl == 0);

        $this->success('查询成功', $user);
    }

    /**
     * 解绑下级用户
     * @ApiTitle (解绑下级用户)
     * @ApiRoute (/api/xiluedu.ylgw/unbind_user)
     * @ApiMethod (POST)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     * @ApiParams (name="user_id", type="int", required=true, description="用户ID")
     */
    public function unbind_user()
    {
        $user_id = $this->request->post('user_id');

        if (!$user_id) {
            $this->error('用户ID不能为空');
        }

        $current_user = $this->auth->getUser();

        // 检查当前用户是否为养老顾问
        if ($current_user->is_ylgw != 1) {
            $this->error('您不是养老顾问');
        }

        // 查找目标用户
        $target_user = User::where('id', $user_id)->find();
        if (!$target_user) {
            $this->error('用户不存在');
        }

        // 检查是否为下级用户
        if ($target_user->parent_id != $current_user->id) {
            $this->error('该用户不是您的下级用户');
        }

        // 检查用户是否为养老顾问或更高级别（不能解绑）
        if ($target_user->is_ylgw == 1 || $target_user->is_sqdl == 1 || $target_user->is_qydl == 1) {
            $this->error('该用户已是养老顾问或更高级别，无法解绑');
        }

        Db::startTrans();
        try {
            // 解绑上下级关系
            $target_user->parent_id = 0;
            $target_user->save();

            Db::commit();
            $this->success('解绑成功');

        } catch (Exception $e) {
            Db::rollback();
            $this->error('解绑失败：' . $e->getMessage());
        }
    }

    /**
     * 获取团队统计
     * @ApiTitle (获取团队统计)
     * @ApiRoute (/api/xiluedu.ylgw/team_stats)
     * @ApiMethod (GET)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     */
    public function team_stats()
    {
        $current_user = $this->auth->getUser();

        // 检查当前用户是否为养老顾问
        if ($current_user->is_ylgw != 1) {
            $this->error('您不是养老顾问');
        }

        // 获取所有下级用户ID（递归）
        $all_sub_users = User::getAllNonAgentDescendantsYlgw($current_user->id);

        // 统计团队数据
        $team_count = count($all_sub_users);
        $direct_count = User::where('parent_id', $current_user->id)
            ->where('is_ylgw', 0)
            ->where('is_sqdl', 0)
            ->where('is_qydl', 0)
            ->count();

        // 统计今日新增
        $today_new = User::where('parent_id', $current_user->id)
            ->where('createtime', 'between', [strtotime(date('Y-m-d')), strtotime(date('Y-m-d 23:59:59'))])
            ->count();

        // 统计本月新增
        $month_new = User::where('parent_id', $current_user->id)
            ->where('createtime', 'between', [strtotime(date('Y-m-01')), time()])
            ->count();

        $data = [
            'team_count' => $team_count,
            'direct_count' => $direct_count,
            'today_new' => $today_new,
            'month_new' => $month_new,
        ];

        $this->success('查询成功', $data);
    }

    /**
     * 获取下级养老顾问列表
     * @ApiTitle (获取下级养老顾问列表)
     * @ApiRoute (/api/xiluedu.ylgw/sub_ylgw_list)
     * @ApiMethod (GET)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     * @ApiParams (name="page", type="int", required=false, description="页码")
     * @ApiParams (name="limit", type="int", required=false, description="每页数量")
     */
    public function sub_ylgw_list()
    {
        $current_user = $this->auth->getUser();

        // 检查当前用户是否为养老顾问
        if ($current_user->is_ylgw != 1) {
            $this->error('您不是养老顾问');
        }

        $page = $this->request->get('page', 1);
        $limit = $this->request->get('limit', 10);

        // 获取下级养老顾问
        $list = User::where('parent_id', $current_user->id)
            ->where('is_ylgw', 1)
            ->field('id,nickname,mobile,avatar,createtime,money')
            ->order('createtime desc')
            ->paginate($limit, false, ['page' => $page]);

        foreach ($list as &$user) {
            $user->avatar = $user->avatar ? cdnurl($user->avatar, true) : '';
            $user->createtime_text = date('Y-m-d H:i:s', $user->createtime);
            $user->mobile = substr_replace($user->mobile, '****', 3, 4);

            // 统计该养老顾问的下级用户数量
            $user->sub_count = User::where('parent_id', $user->id)->count();

            // 统计该养老顾问的收益
            $user->total_income = MoneyLog::where('user_id', $user->id)
                ->where('type', 'fenyong')
                ->where('memo', 'like', '%养老顾问分成%')
                ->sum('money') ?: 0;
        }

        $this->success('查询成功', $list);
    }

    /**
     * 获取收益排行榜
     * @ApiTitle (获取收益排行榜)
     * @ApiRoute (/api/xiluedu.ylgw/income_rank)
     * @ApiMethod (GET)
     * @ApiHeaders (name=token, type=string, require=true, description="Token")
     * @ApiParams (name="type", type="string", required=false, description="排行类型：today,month,total")
     * @ApiParams (name="limit", type="int", required=false, description="排行数量")
     */
    public function income_rank()
    {
        $current_user = $this->auth->getUser();

        // 检查当前用户是否为养老顾问
        if ($current_user->is_ylgw != 1) {
            $this->error('您不是养老顾问');
        }

        $type = $this->request->get('type', 'total');
        $limit = $this->request->get('limit', 10);

        // 计算时间范围
        $time_condition = '';
        switch ($type) {
            case 'today':
                $start_time = strtotime(date('Y-m-d'));
                $end_time = strtotime(date('Y-m-d 23:59:59'));
                $time_condition = " AND createtime BETWEEN {$start_time} AND {$end_time}";
                break;
            case 'month':
                $start_time = strtotime(date('Y-m-01'));
                $end_time = time();
                $time_condition = " AND createtime BETWEEN {$start_time} AND {$end_time}";
                break;
            case 'total':
            default:
                $time_condition = '';
                break;
        }

        // 获取下级用户ID
        $sub_user_ids = User::where('parent_id', $current_user->id)->column('id');

        if (empty($sub_user_ids)) {
            $this->success('查询成功', []);
        }

        // 查询收益排行
        $sql = "SELECT user_id, SUM(money) as total_income
                FROM fa_money_log
                WHERE user_id IN (" . implode(',', $sub_user_ids) . ")
                AND type = 'fenyong'
                AND memo LIKE '%养老顾问分成%'
                {$time_condition}
                GROUP BY user_id
                ORDER BY total_income DESC
                LIMIT {$limit}";

        $rank_data = Db::query($sql);

        $list = [];
        foreach ($rank_data as $index => $item) {
            $user = User::where('id', $item['user_id'])
                ->field('id,nickname,avatar')
                ->find();

            if ($user) {
                $list[] = [
                    'rank' => $index + 1,
                    'user_id' => $user->id,
                    'nickname' => $user->nickname,
                    'avatar' => $user->avatar ? cdnurl($user->avatar, true) : '',
                    'income' => $item['total_income']
                ];
            }
        }

        $this->success('查询成功', $list);
    }
}
