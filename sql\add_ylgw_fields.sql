-- 添加养老顾问相关字段

-- 在用户表中添加is_ylgw字段
ALTER TABLE fa_user ADD COLUMN is_ylgw tinyint(1) DEFAULT 0 COMMENT '是否为养老顾问';

-- 在课程表中添加养老顾问分成比例字段
ALTER TABLE fa_xiluedu_course ADD COLUMN ylgw_bili decimal(11,2) DEFAULT 0.00 COMMENT '养老顾问分成比例';

-- 在线下课程表中添加养老顾问分成比例字段
ALTER TABLE fa_xiluedu_offline_course ADD COLUMN ylgw_bili decimal(11,2) DEFAULT 0.00 COMMENT '养老顾问分成比例';

-- 查看添加结果
SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'service_jiaqingf' 
AND TABLE_NAME = 'fa_user' 
AND COLUMN_NAME = 'is_ylgw';

SELECT COLUMN_NAME, DATA_TYPE, COLUMN_DEFAULT, COLUMN_COMMENT 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'service_jiaqingf' 
AND TABLE_NAME = 'fa_xiluedu_course' 
AND COLUMN_NAME = 'ylgw_bili';
