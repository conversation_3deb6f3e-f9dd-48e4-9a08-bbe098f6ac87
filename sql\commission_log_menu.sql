-- 分佣日志系统管理后台菜单配置

-- 1. 添加分佣日志管理菜单
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', 0, 'commissionlog', '分佣管理', 'fa fa-money', '', '分佣日志管理', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 'normal');

-- 获取刚插入的父菜单ID
SET @parent_id = LAST_INSERT_ID();

-- 2. 添加分佣日志子菜单
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', @parent_id, 'commissionlog/index', '分佣日志', 'fa fa-list', '', '查看分佣日志列表', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 'normal'),
('file', @parent_id, 'commissionlog/detail', '分佣详情', 'fa fa-eye', '', '查看分佣详情', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 99, 'normal'),
('file', @parent_id, 'commissionlog/statistics', '分佣统计', 'fa fa-bar-chart', '', '分佣统计分析', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 98, 'normal'),
('file', @parent_id, 'commissionlog/add', '添加分佣记录', 'fa fa-plus', '', '手动添加分佣记录', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 97, 'normal'),
('file', @parent_id, 'commissionlog/edit', '编辑分佣记录', 'fa fa-edit', '', '编辑分佣记录', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 96, 'normal'),
('file', @parent_id, 'commissionlog/del', '删除分佣记录', 'fa fa-trash', '', '删除分佣记录', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 95, 'normal'),
('file', @parent_id, 'commissionlog/multi', '批量操作', 'fa fa-cogs', '', '批量操作分佣记录', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 94, 'normal');

-- 3. 为超级管理员组添加权限（假设超级管理员组ID为1）
INSERT INTO `fa_auth_group_access` (`uid`, `group_id`) 
SELECT 1, 1 FROM DUAL 
WHERE NOT EXISTS (SELECT 1 FROM `fa_auth_group_access` WHERE `uid` = 1 AND `group_id` = 1);

-- 4. 更新超级管理员组的权限规则
UPDATE `fa_auth_group` 
SET `rules` = CONCAT(IFNULL(`rules`, ''), ',', @parent_id, ',', @parent_id + 1, ',', @parent_id + 2, ',', @parent_id + 3, ',', @parent_id + 4, ',', @parent_id + 5, ',', @parent_id + 6, ',', @parent_id + 7)
WHERE `id` = 1;

-- 5. 清理可能的重复逗号
UPDATE `fa_auth_group` 
SET `rules` = TRIM(BOTH ',' FROM REPLACE(REPLACE(`rules`, ',,', ','), ',,', ','))
WHERE `id` = 1;

-- 6. 查看添加的菜单
SELECT 
    r.id,
    r.pid,
    r.name,
    r.title,
    r.icon,
    r.ismenu,
    r.status,
    CASE 
        WHEN r.pid = 0 THEN '顶级菜单'
        ELSE CONCAT('子菜单 (父ID: ', r.pid, ')')
    END as menu_type
FROM `fa_auth_rule` r 
WHERE r.name LIKE 'commissionlog%' 
ORDER BY r.pid, r.weigh DESC;

-- 7. 验证权限配置
SELECT 
    g.id as group_id,
    g.name as group_name,
    g.rules,
    CASE 
        WHEN FIND_IN_SET(@parent_id, g.rules) > 0 THEN '已配置'
        ELSE '未配置'
    END as commission_permission
FROM `fa_auth_group` g 
WHERE g.id = 1;
