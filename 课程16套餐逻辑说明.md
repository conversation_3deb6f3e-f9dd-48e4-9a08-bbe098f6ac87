# 课程16套餐逻辑说明

## 核心概念

### 套餐数量的含义
- **套餐数量** = 购买此套餐可获得的**养老顾问开通名额数量**
- **不是**购买的套餐个数
- **不影响**套餐的价格计算

### 价格逻辑
- 套餐价格是**固定的**，不按数量计算
- 无论套餐包含10个名额还是50个名额，同一用户类型的价格都是固定的

## 具体示例

### 套餐配置示例
```
套餐名称：套餐购买(10个)
套餐数量：10
原价：¥3650.00
城市负责人价格：¥1000.00
养老院长价格：¥1300.00
```

### 价格计算
- **城市负责人**购买：支付 ¥1000.00，获得 10个 养老顾问开通名额
- **养老院长**购买：支付 ¥1300.00，获得 10个 养老顾问开通名额
- **普通用户**购买：支付 ¥3650.00，获得 10个 养老顾问开通名额

### 如果修改为50个名额
```
套餐名称：套餐购买(50个)
套餐数量：50
原价：¥3650.00
城市负责人价格：¥1000.00
养老院长价格：¥1300.00
```

- **城市负责人**购买：仍然支付 ¥1000.00，但获得 50个 养老顾问开通名额
- **养老院长**购买：仍然支付 ¥1300.00，但获得 50个 养老顾问开通名额

## 技术实现

### 1. 价格计算修改
```php
// 旧逻辑（错误）
public function getTotalPrice($userType = 'user')
{
    return $this->getPriceByUserType($userType) * $this->quantity;
}

// 新逻辑（正确）
public function getTotalPrice($userType = 'user')
{
    return $this->getPriceByUserType($userType); // 价格固定，不乘以数量
}
```

### 2. 订单创建修改
```php
// 价格计算
$unit_price = $package->getPriceByUserType($userType);
$quantity = 1; // 套餐购买数量固定为1
$total_price = $unit_price; // 套餐价格固定

// 订单数据
$order_data = [
    'package_quantity' => $package->quantity, // 存储名额数量
    'total_price' => $total_price,
    // ...
];
```

### 3. 支付成功后处理
```php
// 增加开通额度（package_quantity表示名额数量）
$user->ylgw_quota = $user->ylgw_quota + $order->package_quantity;
$user->save();
```

## 数据库字段说明

### fa_xiluedu_course_package 表
- `quantity`: 套餐包含的名额数量（不是购买数量）
- `original_price`: 原价（固定价格）
- `qydl_price`: 城市负责人价格（固定价格）
- `sqdl_price`: 养老院长价格（固定价格）

### fa_xiluedu_course_order 表
- `package_quantity`: 存储套餐的名额数量
- `total_price`: 实际支付的固定价格

### fa_user 表
- `ylgw_quota`: 用户拥有的养老顾问开通名额数量

## 业务流程

### 1. 套餐配置
管理员在后台配置套餐：
- 设置套餐名称和描述
- 设置名额数量（如10个、50个）
- 设置各用户类型的固定价格

### 2. 用户购买
用户选择套餐并支付：
- 系统根据用户身份显示对应的固定价格
- 用户支付固定价格
- 系统增加用户的开通名额

### 3. 名额使用
用户使用名额为他人开通养老顾问身份：
- 每次开通消耗1个名额
- 名额用完后需要重新购买套餐

## 注意事项

1. **价格固定性**：套餐价格不会因为名额数量变化而变化
2. **名额独立性**：名额数量和价格是独立的两个概念
3. **用户体验**：界面上需要清楚说明名额数量的含义
4. **数据一致性**：确保订单记录的package_quantity字段正确存储名额数量
