-- 简化养老顾问分佣系统
-- 删除商品级别的养老顾问分成比例字段，只保留全局配置

-- 1. 删除商品级别的养老顾问分成比例字段
ALTER TABLE `fa_xiluedu_course` DROP COLUMN IF EXISTS `ylgw_bili`;
ALTER TABLE `fa_xiluedu_offline_course` DROP COLUMN IF EXISTS `ylgw_bili`;
ALTER TABLE `fa_wanlshop_goods` DROP COLUMN IF EXISTS `ylgw_bili`;
ALTER TABLE `fa_service_goods` DROP COLUMN IF EXISTS `ylgw_bili`;

-- 2. 删除不需要的全局配置项
DELETE FROM `fa_config` WHERE `name` IN ('goods_ylgw_bili', 'service_ylgw_bili', 'course_ylgw_bili', 'gjia_quyu_bili');

-- 3. 更新主要的养老顾问分成比例配置项
UPDATE `fa_config` SET 
    `title` = '养老顾问分成比例',
    `tip` = '设置养老顾问基于养老院长分佣金额的分成比例，单位为百分比',
    `value` = '50'
WHERE `name` = 'gjia_bili';

-- 4. 确保线下分佣比例配置存在
INSERT IGNORE INTO `fa_config` (`name`, `group`, `title`, `tip`, `type`, `value`, `content`, `rule`, `extend`) VALUES 
('ylgw_offline_rate', 'site', '养老顾问线下分佣比例', '设置有上级的养老顾问的线下分佣比例，单位为百分比', 'number', '50', '', 'required', '');

-- 5. 查看修改结果
SELECT name, title, value FROM fa_config WHERE name IN ('gjia_bili', 'ylgw_offline_rate') ORDER BY name;

-- 6. 检查字段是否已删除
SELECT TABLE_NAME, COLUMN_NAME 
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND COLUMN_NAME = 'ylgw_bili'
AND TABLE_NAME IN ('fa_xiluedu_course', 'fa_xiluedu_offline_course', 'fa_wanlshop_goods', 'fa_service_goods');
