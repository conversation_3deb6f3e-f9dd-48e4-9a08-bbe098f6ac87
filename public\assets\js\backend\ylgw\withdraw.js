define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'ylgw/withdraw/index' + location.search,
                    add_url: 'ylgw/withdraw/add',
                    edit_url: 'ylgw/withdraw/edit',
                    del_url: 'ylgw/withdraw/del',
                    multi_url: 'ylgw/withdraw/multi',
                    import_url: 'ylgw/withdraw/import',
                    table: 'ylgw_withdraw',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                fixedColumns: true,
                fixedRightNumber: 1,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id')},
                        {field: 'user.username', title: '申请用户', operate: 'LIKE'},
                        {field: 'user.mobile', title: '手机号', operate: 'LIKE'},
                        {field: 'money', title: '申请金额', operate:'BETWEEN', formatter: function(value) {
                            return '¥' + parseFloat(value).toFixed(2);
                        }},
                        {field: 'transfer_amount', title: '转账金额', operate:'BETWEEN', formatter: function(value) {
                            return value > 0 ? '¥' + parseFloat(value).toFixed(2) : '-';
                        }},
                        {field: 'status', title: '状态', searchList: {"0":"待审核","1":"已审核通过","2":"已拒绝"}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: '申请时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: Table.api.formatter.datetime},
                        {field: 'audit_time', title: '审核时间', operate:'RANGE', addclass:'datetimerange', autocomplete:false, formatter: function(value) {
                            return value > 0 ? Table.api.formatter.datetime(value) : '-';
                        }},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, 
                            buttons: [
                                {
                                    name: 'detail',
                                    text: '详情',
                                    title: '查看详情',
                                    classname: 'btn btn-xs btn-info btn-dialog',
                                    icon: 'fa fa-list',
                                    url: 'ylgw/withdraw/detail'
                                },
                                {
                                    name: 'audit',
                                    text: '审核',
                                    title: '审核申请',
                                    classname: 'btn btn-xs btn-success btn-dialog',
                                    icon: 'fa fa-check',
                                    url: 'ylgw/withdraw/audit',
                                    visible: function (row) {
                                        return row.status == '0';
                                    }
                                }
                            ], 
                            formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);
            
            // 审核按钮事件
            $(document).on('click', '.btn-audit', function () {
                var ids = Table.api.selectedids(table);
                if (ids.length == 0) {
                    Toastr.warning("请至少选择一条记录");
                    return false;
                }
                if (ids.length > 1) {
                    Toastr.warning("请选择一条记录进行审核");
                    return false;
                }
                var url = 'ylgw/withdraw/audit/ids/' + ids.join(',');
                Fast.api.open(url, '审核申请', {
                    area: ['800px', '600px'],
                    callback: function(data) {
                        table.bootstrapTable('refresh');
                    }
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        audit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
