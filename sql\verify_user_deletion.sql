-- 验证用户删除操作完整性的SQL脚本
-- 使用方法：将 '13800138000' 替换为要验证的手机号

SET @mobile = '13800138000';  -- 替换为要验证的手机号
SET @user_id = (SELECT id FROM fa_user WHERE mobile = @mobile);

-- 显示用户ID
SELECT CONCAT('用户ID: ', IFNULL(@user_id, '用户不存在')) AS user_info;

-- 如果用户存在，检查各表中的数据
SELECT 
    CASE 
        WHEN @user_id IS NULL THEN '用户不存在，无需检查'
        ELSE '开始检查用户相关数据...'
    END AS check_status;

-- 检查用户主表
SELECT 
    'fa_user' AS table_name,
    COUNT(*) AS record_count,
    '用户主表' AS description
FROM fa_user 
WHERE mobile = @mobile;

-- 检查订单相关表
SELECT 
    'fa_service_order' AS table_name,
    COUNT(*) AS record_count,
    '服务订单' AS description
FROM fa_service_order 
WHERE user_id = @user_id;

SELECT 
    'fa_wanlshop_order' AS table_name,
    COUNT(*) AS record_count,
    '商城订单' AS description
FROM fa_wanlshop_order 
WHERE user_id = @user_id;

SELECT 
    'fa_xiluedu_course_order' AS table_name,
    COUNT(*) AS record_count,
    '课程订单' AS description
FROM fa_xiluedu_course_order 
WHERE user_id = @user_id;

SELECT 
    'fa_wanlshop_groups_order' AS table_name,
    COUNT(*) AS record_count,
    '拼团订单' AS description
FROM fa_wanlshop_groups_order 
WHERE user_id = @user_id;

-- 检查日志相关表
SELECT 
    'fa_user_money_log' AS table_name,
    COUNT(*) AS record_count,
    '余额变动日志' AS description
FROM fa_user_money_log 
WHERE user_id = @user_id;

SELECT 
    'fa_user_score_log' AS table_name,
    COUNT(*) AS record_count,
    '积分变动日志' AS description
FROM fa_user_score_log 
WHERE user_id = @user_id;

SELECT 
    'fa_service_order_log' AS table_name,
    COUNT(*) AS record_count,
    '订单日志' AS description
FROM fa_service_order_log 
WHERE user_id = @user_id;

-- 检查地址相关表
SELECT 
    'fa_service_address' AS table_name,
    COUNT(*) AS record_count,
    '服务地址' AS description
FROM fa_service_address 
WHERE user_id = @user_id;

SELECT 
    'fa_wanlshop_address' AS table_name,
    COUNT(*) AS record_count,
    '商城地址' AS description
FROM fa_wanlshop_address 
WHERE user_id = @user_id;

-- 检查购物车
SELECT 
    'fa_wanlshop_cart' AS table_name,
    COUNT(*) AS record_count,
    '购物车' AS description
FROM fa_wanlshop_cart 
WHERE user_id = @user_id;

-- 检查评论相关表
SELECT 
    'fa_service_comment' AS table_name,
    COUNT(*) AS record_count,
    '服务评论' AS description
FROM fa_service_comment 
WHERE user_id = @user_id;

SELECT 
    'fa_wanlshop_goods_comment' AS table_name,
    COUNT(*) AS record_count,
    '商品评论' AS description
FROM fa_wanlshop_goods_comment 
WHERE user_id = @user_id;

-- 检查投诉相关表
SELECT 
    'fa_service_complaint' AS table_name,
    COUNT(*) AS record_count,
    '服务投诉' AS description
FROM fa_service_complaint 
WHERE user_id = @user_id;

SELECT 
    'fa_wanlshop_complaint' AS table_name,
    COUNT(*) AS record_count,
    '商城投诉' AS description
FROM fa_wanlshop_complaint 
WHERE user_id = @user_id OR complaint_user_id = @user_id;

-- 检查附件表
SELECT 
    'fa_attachment' AS table_name,
    COUNT(*) AS record_count,
    '附件' AS description
FROM fa_attachment 
WHERE user_id = @user_id;

-- 检查token表
SELECT 
    'fa_user_token' AS table_name,
    COUNT(*) AS record_count,
    '用户token' AS description
FROM fa_user_token 
WHERE user_id = @user_id;

-- 检查优惠券相关表
SELECT 
    'fa_wanlshop_coupon_user' AS table_name,
    COUNT(*) AS record_count,
    '商城优惠券' AS description
FROM fa_wanlshop_coupon_user 
WHERE user_id = @user_id;

-- 检查聊天记录
SELECT 
    'fa_wanlshop_chat' AS table_name,
    COUNT(*) AS record_count,
    '聊天记录' AS description
FROM fa_wanlshop_chat 
WHERE form_uid = @user_id OR to_id = @user_id;

-- 检查收藏记录
SELECT 
    'fa_wanlshop_collection' AS table_name,
    COUNT(*) AS record_count,
    '收藏记录' AS description
FROM fa_wanlshop_collection 
WHERE user_id = @user_id;

-- 检查浏览记录
SELECT 
    'fa_wanlshop_record' AS table_name,
    COUNT(*) AS record_count,
    '浏览记录' AS description
FROM fa_wanlshop_record 
WHERE user_id = @user_id;

-- 检查申请相关表
SELECT 
    'fa_service_apply_skill' AS table_name,
    COUNT(*) AS record_count,
    '服务者申请' AS description
FROM fa_service_apply_skill 
WHERE user_id = @user_id;

SELECT 
    'fa_service_apply_shop' AS table_name,
    COUNT(*) AS record_count,
    '商户申请' AS description
FROM fa_service_apply_shop 
WHERE user_id = @user_id;

-- 检查提现记录
SELECT 
    'fa_wanlshop_withdraw' AS table_name,
    COUNT(*) AS record_count,
    '商城提现' AS description
FROM fa_wanlshop_withdraw 
WHERE user_id = @user_id;

-- 检查消息相关表
SELECT 
    'fa_wanlshop_message' AS table_name,
    COUNT(*) AS record_count,
    '消息' AS description
FROM fa_wanlshop_message 
WHERE user_id = @user_id;

SELECT 
    'fa_wanlshop_message_read' AS table_name,
    COUNT(*) AS record_count,
    '消息阅读记录' AS description
FROM fa_wanlshop_message_read 
WHERE user_id = @user_id;

-- 汇总检查结果
SELECT 
    '=== 检查完成 ===' AS summary,
    CASE 
        WHEN @user_id IS NULL THEN '用户不存在'
        ELSE '请检查上述各表的记录数量，正常情况下应该都为0'
    END AS result_description;

-- 如果需要查看具体的残留数据，可以使用以下查询
-- 注意：只有在上述检查发现有残留数据时才需要执行

/*
-- 查看残留的用户数据
SELECT * FROM fa_user WHERE mobile = @mobile;

-- 查看残留的订单数据
SELECT * FROM fa_service_order WHERE user_id = @user_id LIMIT 10;
SELECT * FROM fa_wanlshop_order WHERE user_id = @user_id LIMIT 10;

-- 查看残留的日志数据
SELECT * FROM fa_user_money_log WHERE user_id = @user_id LIMIT 10;
SELECT * FROM fa_user_score_log WHERE user_id = @user_id LIMIT 10;
*/
