-- 修复用户上级关系自引用问题
-- 问题：某些用户的 parent_id 等于自己的 id，导致无限递归

-- 1. 查看当前存在自引用的用户
SELECT id, parent_id, is_sqdl, is_qydl, nickname, mobile 
FROM fa_user 
WHERE id = parent_id;

-- 2. 修复自引用问题：将自引用的用户设置为顶级用户（parent_id = 0）
UPDATE fa_user 
SET parent_id = 0 
WHERE id = parent_id;

-- 3. 验证修复结果
SELECT COUNT(*) as remaining_self_references 
FROM fa_user 
WHERE id = parent_id;

-- 4. 检查可能的循环引用（A->B->A）
-- 这个查询会找出可能的二级循环引用
SELECT 
    u1.id as user1_id,
    u1.parent_id as user1_parent,
    u2.id as user2_id,
    u2.parent_id as user2_parent
FROM fa_user u1
JOIN fa_user u2 ON u1.parent_id = u2.id
WHERE u2.parent_id = u1.id
  AND u1.id != u2.id;

-- 5. 添加约束防止未来出现自引用（可选）
-- ALTER TABLE fa_user ADD CONSTRAINT chk_no_self_parent CHECK (id != parent_id);

-- 6. 创建索引优化 parent_id 查询性能
CREATE INDEX idx_parent_id ON fa_user(parent_id) IF NOT EXISTS;

-- 执行说明：
-- 1. 先执行查询语句查看问题数据
-- 2. 执行 UPDATE 语句修复问题
-- 3. 执行验证查询确认修复成功
-- 4. 可选：添加约束防止未来出现类似问题
