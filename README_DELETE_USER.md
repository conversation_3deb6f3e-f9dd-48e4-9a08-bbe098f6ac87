# 根据手机号删除用户所有信息功能

## 功能概述

本功能提供了一个API接口，可以根据用户手机号删除该用户在系统中的所有相关信息，包括但不限于：
- 订单信息（服务订单、商城订单、课程订单等）
- 日志信息（余额变动、积分变动、操作日志等）
- 个人信息（地址、购物车、收藏、浏览记录等）
- 关联信息（评论、投诉、聊天记录、优惠券等）

## 文件结构

```
├── application/api/controller/User.php          # 主要接口实现
├── docs/api/delete_user_by_mobile.md           # 接口文档
├── docs/api/delete_user_by_mobile.apifox.json  # Apifox格式API文档
├── test/delete_user_test.php                   # 测试脚本
├── sql/verify_user_deletion.sql                # 验证SQL脚本
└── README_DELETE_USER.md                       # 本说明文件
```

## 接口信息

- **接口地址**: `POST /api/user/deleteUserByMobile`
- **请求参数**: `mobile` (string, 必填) - 用户手机号
- **权限要求**: 无需登录（建议在生产环境中添加管理员权限验证）

## 使用步骤

### 1. 部署接口

接口代码已经添加到 `application/api/controller/User.php` 文件中，包含以下方法：
- `deleteUserByMobile()` - 主接口方法
- `deleteOrderRelatedData()` - 删除订单相关数据
- `deleteLogRelatedData()` - 删除日志相关数据
- `deleteOtherRelatedData()` - 删除其他相关数据

### 2. 测试接口

使用提供的测试脚本进行测试：

```bash
php test/delete_user_test.php
```

或者使用curl命令测试：

```bash
curl -X POST "https://your-domain.com/api/user/deleteUserByMobile" \
     -d "mobile=13800138000"
```

### 3. 验证删除结果

使用提供的SQL脚本验证删除是否完整：

```sql
-- 修改脚本中的手机号
SET @mobile = '13800138000';
-- 然后执行 sql/verify_user_deletion.sql
```

## API文档

### 请求示例

```bash
POST /api/user/deleteUserByMobile
Content-Type: application/x-www-form-urlencoded

mobile=13800138000
```

### 响应示例

成功响应：
```json
{
    "code": 1,
    "msg": "用户信息删除成功",
    "time": 1640995200,
    "data": null
}
```

失败响应：
```json
{
    "code": 0,
    "msg": "用户不存在",
    "time": 1640995200,
    "data": null
}
```

## 删除的数据范围

### 订单相关（约12个表）
- 服务订单及相关信息
- 商城订单及相关信息
- 拼团订单及相关信息
- 课程订单及相关信息
- 充值订单、保姆订单、代理订单等

### 日志相关（约7个表）
- 用户余额/积分变动日志
- 服务者/商户余额日志
- 返佣记录、佣金日志等

### 其他相关（约30+个表）
- 个人信息（地址、购物车、收藏等）
- 社交信息（聊天记录、消息等）
- 业务信息（申请记录、投诉举报等）
- 系统信息（token、附件等）

## 安全注意事项

⚠️ **重要警告**：此操作会永久删除用户数据，无法恢复！

### 生产环境使用建议

1. **权限控制**：添加管理员权限验证
```php
// 在 deleteUserByMobile 方法开头添加
if (!$this->auth->isLogin() || $this->auth->getUser()->group_id != 1) {
    $this->error('无权限执行此操作');
}
```

2. **操作日志**：记录删除操作
```php
// 记录操作日志
Db::name('admin_log')->insert([
    'admin_id' => $this->auth->id,
    'username' => $this->auth->username,
    'url' => '/api/user/deleteUserByMobile',
    'title' => '删除用户信息',
    'content' => "删除手机号为 {$mobile} 的用户信息",
    'ip' => $this->request->ip(),
    'createtime' => time()
]);
```

3. **二次确认**：添加确认参数
```php
$confirm = $this->request->post('confirm');
if ($confirm !== 'YES_DELETE_ALL_DATA') {
    $this->error('请确认删除操作');
}
```

4. **频率限制**：添加调用频率限制
5. **IP白名单**：限制调用来源IP
6. **备份机制**：删除前备份重要数据

## 故障排除

### 常见错误

1. **手机号格式错误**
   - 确保手机号为11位数字
   - 以1开头的中国大陆手机号

2. **用户不存在**
   - 检查手机号是否正确
   - 确认用户是否已被删除

3. **删除失败**
   - 检查数据库连接
   - 查看错误日志
   - 确认表结构是否完整

### 调试方法

1. 开启调试模式查看详细错误信息
2. 检查数据库日志
3. 使用验证SQL脚本检查删除结果
4. 查看应用日志文件

## 维护说明

### 定期检查

1. 定期检查是否有新增的用户相关表需要添加到删除逻辑中
2. 监控删除操作的性能，必要时优化SQL语句
3. 定期备份重要数据

### 扩展功能

如需扩展功能，可以考虑：
1. 添加软删除选项
2. 添加数据导出功能
3. 添加批量删除功能
4. 添加删除进度显示

## 联系支持

如有问题，请联系开发团队或查看相关文档。
