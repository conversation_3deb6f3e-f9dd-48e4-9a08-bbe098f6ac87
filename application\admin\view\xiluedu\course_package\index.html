<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <div class="panel-lead">
            <em>{$course.name}</em> - 套餐管理
        </div>
        <ul class="nav nav-tabs" data-field="status">
            <li class="active"><a href="#t-all" data-value="" data-toggle="tab">{:__('All')}</a></li>
            <li><a href="#t-1" data-value="1" data-toggle="tab">启用</a></li>
            <li><a href="#t-0" data-value="0" data-toggle="tab">禁用</a></li>
        </ul>
    </div>

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        <a href="javascript:;" class="btn btn-primary btn-refresh" title="{:__('Refresh')}">
                            <i class="fa fa-refresh"></i> 
                        </a>
                        <a href="javascript:;" class="btn btn-success btn-add" title="{:__('Add')}">
                            <i class="fa fa-plus"></i> {:__('Add')}
                        </a>
                        <a href="javascript:;" class="btn btn-success btn-edit btn-disabled disabled" title="{:__('Edit')}">
                            <i class="fa fa-pencil"></i> {:__('Edit')}
                        </a>
                        <a href="javascript:;" class="btn btn-danger btn-del btn-disabled disabled" title="{:__('Delete')}">
                            <i class="fa fa-trash"></i> {:__('Delete')}
                        </a>
                        <div class="dropdown btn-group">
                            <a class="btn btn-primary btn-more dropdown-toggle btn-disabled disabled" data-toggle="dropdown">
                                <i class="fa fa-cog"></i> {:__('More')}
                            </a>
                            <ul class="dropdown-menu text-left" role="menu">
                                <li><a class="btn btn-link btn-multi btn-disabled disabled" href="javascript:;" data-params="status">
                                    <i class="fa fa-eye"></i> 切换状态</a></li>
                            </ul>
                        </div>
                        <a href="javascript:history.back();" class="btn btn-default">
                            <i class="fa fa-arrow-left"></i> 返回课程列表
                        </a>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="true"
                           data-operate-del="true"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
