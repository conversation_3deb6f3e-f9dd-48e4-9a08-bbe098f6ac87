<?php

namespace app\common\model;

use think\Model;

/**
 * 分佣日志模型
 */
class CommissionLog extends Model
{
    protected $name = 'commission_log';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = false;
    
    /**
     * 记录分佣日志
     * @param array $data 分佣数据
     * @return bool
     */
    public static function recordCommission($data)
    {
        try {
            $log = new self();
            $log->data($data);
            return $log->save();
        } catch (\Exception $e) {
            // 记录错误日志，但不影响主流程
            \think\Log::error('分佣日志记录失败: ' . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 生成批次号
     * @param string $orderType 订单类型
     * @param int $orderId 订单ID
     * @return string
     */
    public static function generateBatchNo($orderType, $orderId)
    {
        return $orderType . '_' . $orderId . '_' . time();
    }
    
    /**
     * 记录完整的分佣过程
     * @param array $params 参数
     * @return bool
     */
    public static function recordFullCommissionProcess($params)
    {
        $batchNo = self::generateBatchNo($params['order_type'], $params['order_id']);
        $stepNo = 1;
        $success = true;
        
        // 记录每一步分佣
        foreach ($params['commissions'] as $commission) {
            $logData = [
                'batch_no' => $batchNo,
                'step_no' => $stepNo++,
                'order_type' => $params['order_type'],
                'order_id' => $params['order_id'],
                'order_no' => $params['order_no'] ?? '',
                'goods_id' => $params['goods_id'] ?? 0,
                'goods_name' => $params['goods_name'] ?? '',
                'order_amount' => $params['order_amount'] ?? 0,
                'base_amount' => $commission['base_amount'] ?? 0,
                'commission_user_id' => $commission['user_id'],
                'commission_user_role' => $commission['user_role'],
                'buyer_user_id' => $params['buyer_user_id'],
                'buyer_user_role' => $params['buyer_user_role'] ?? 'regular_user',
                'commission_type' => $commission['commission_type'],
                'commission_rate' => $commission['rate'],
                'commission_amount' => $commission['amount'],
                'calculation_rule' => $commission['calculation_rule'] ?? '',
                'config_source' => $commission['config_source'] ?? 'global',
                'config_value' => $commission['config_value'] ?? '',
                'is_distributed' => $commission['is_distributed'] ?? 0,
                'distribution_type' => $commission['distribution_type'] ?? 'online',
                'distribution_status' => $commission['distribution_status'] ?? 'pending',
                'distribution_memo' => $commission['distribution_memo'] ?? '',
                'money_log_id' => $commission['money_log_id'] ?? 0,
                'parent_user_id' => $commission['parent_user_id'] ?? 0,
                'parent_user_role' => $commission['parent_user_role'] ?? 'platform',
                'has_superior' => $commission['has_superior'] ?? 0,
            ];
            
            if (!self::recordCommission($logData)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * 获取用户的分佣统计
     * @param int $userId 用户ID
     * @param string $orderType 订单类型
     * @return array
     */
    public static function getUserCommissionStats($userId, $orderType = '')
    {
        $where = ['commission_user_id' => $userId];
        if ($orderType) {
            $where['order_type'] = $orderType;
        }
        
        $stats = self::where($where)
            ->field([
                'COUNT(*) as total_count',
                'SUM(commission_amount) as total_amount',
                'SUM(CASE WHEN is_distributed = 1 THEN commission_amount ELSE 0 END) as distributed_amount',
                'SUM(CASE WHEN distribution_type = "online" THEN commission_amount ELSE 0 END) as online_amount',
                'SUM(CASE WHEN distribution_type = "offline" THEN commission_amount ELSE 0 END) as offline_amount'
            ])
            ->find();
            
        return $stats ? $stats->toArray() : [];
    }
    
    /**
     * 获取订单的分佣详情
     * @param string $orderType 订单类型
     * @param int $orderId 订单ID
     * @return array
     */
    public static function getOrderCommissionDetails($orderType, $orderId)
    {
        return self::where([
            'order_type' => $orderType,
            'order_id' => $orderId
        ])
        ->order('step_no ASC')
        ->select();
    }
    
    /**
     * 更新分佣发放状态
     * @param int $id 日志ID
     * @param string $status 状态
     * @param string $memo 备注
     * @param int $moneyLogId 资金记录ID
     * @return bool
     */
    public static function updateDistributionStatus($id, $status, $memo = '', $moneyLogId = 0)
    {
        $updateData = [
            'distribution_status' => $status,
            'distribution_memo' => $memo
        ];
        
        if ($status === 'success') {
            $updateData['is_distributed'] = 1;
        }
        
        if ($moneyLogId > 0) {
            $updateData['money_log_id'] = $moneyLogId;
        }
        
        return self::where('id', $id)->update($updateData);
    }
    
    /**
     * 获取角色中文名称
     * @param string $role 角色英文名
     * @return string
     */
    public static function getRoleNameCn($role)
    {
        $roleMap = [
            'platform' => '平台',
            'city_manager' => '城市负责人',
            'nursing_home_director' => '养老院长',
            'elderly_advisor' => '养老顾问',
            'teacher' => '老师',
            'service_provider' => '服务者',
            'regular_user' => '普通用户'
        ];
        
        return $roleMap[$role] ?? $role;
    }
    
    /**
     * 获取分佣类型中文名称
     * @param string $type 分佣类型
     * @return string
     */
    public static function getCommissionTypeCn($type)
    {
        $typeMap = [
            'direct' => '直接分佣',
            'indirect' => '间接分佣',
            'recommendation' => '推荐分佣',
            'teacher' => '老师分佣',
            'service' => '服务者分佣'
        ];
        
        return $typeMap[$type] ?? $type;
    }
}
