<div class="panel panel-default panel-intro">
    <div class="panel-heading">
        <h3 class="panel-title">分佣详情 - 批次号：{$row.batch_no}</h3>
    </div>
    <div class="panel-body">
        <!-- 订单基本信息 -->
        <div class="row">
            <div class="col-md-12">
                <h4>订单基本信息</h4>
                <table class="table table-bordered">
                    <tr>
                        <td width="15%"><strong>订单类型</strong></td>
                        <td width="35%">{$row.order_type_text}</td>
                        <td width="15%"><strong>订单号</strong></td>
                        <td width="35%">{$row.order_no}</td>
                    </tr>
                    <tr>
                        <td><strong>商品名称</strong></td>
                        <td>{$row.goods_name}</td>
                        <td><strong>订单金额</strong></td>
                        <td>￥{$row.order_amount}</td>
                    </tr>
                    <tr>
                        <td><strong>购买用户ID</strong></td>
                        <td>{$row.buyer_user_id}</td>
                        <td><strong>购买用户角色</strong></td>
                        <td>{$row.buyer_user_role_text}</td>
                    </tr>
                    <tr>
                        <td><strong>创建时间</strong></td>
                        <td colspan="3">{$row.createtime|date='Y-m-d H:i:s',###}</td>
                    </tr>
                </table>
            </div>
        </div>

        <!-- 分佣详细步骤 -->
        <div class="row">
            <div class="col-md-12">
                <h4>分佣详细步骤</h4>
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th width="8%">步骤</th>
                            <th width="12%">分佣角色</th>
                            <th width="10%">用户ID</th>
                            <th width="10%">分佣类型</th>
                            <th width="8%">比例(%)</th>
                            <th width="10%">基数(元)</th>
                            <th width="10%">金额(元)</th>
                            <th width="8%">发放状态</th>
                            <th width="8%">发放方式</th>
                            <th width="16%">说明</th>
                        </tr>
                    </thead>
                    <tbody>
                        {volist name="batch_logs" id="log"}
                        <tr class="{if condition='$log.is_distributed eq 1'}success{elseif condition='$log.commission_amount eq 0'/}warning{else/}info{/if}">
                            <td>{$log.step_no}</td>
                            <td>
                                <span class="label label-{if condition='$log.commission_user_role eq "platform"'}default{elseif condition='$log.commission_user_role eq "city_manager"'}primary{elseif condition='$log.commission_user_role eq "nursing_home_director"'}success{elseif condition='$log.commission_user_role eq "elderly_advisor"'}info{elseif condition='$log.commission_user_role eq "teacher"'}warning{else/}danger{/if}">
                                    {$log.commission_user_role_text}
                                </span>
                            </td>
                            <td>{$log.commission_user_id|default='--'}</td>
                            <td>
                                <span class="label label-{if condition='$log.commission_type eq "direct"'}success{elseif condition='$log.commission_type eq "indirect"'}info{elseif condition='$log.commission_type eq "recommendation"'}warning{else/}default{/if}">
                                    {$log.commission_type_text}
                                </span>
                            </td>
                            <td>{$log.commission_rate}</td>
                            <td>{$log.base_amount}</td>
                            <td class="{if condition='$log.commission_amount gt 0'}text-success{else/}text-muted{/if}">
                                <strong>{$log.commission_amount}</strong>
                            </td>
                            <td>
                                {if condition='$log.is_distributed eq 1'}
                                <span class="label label-success">已发放</span>
                                {else/}
                                <span class="label label-{if condition='$log.commission_amount eq 0'}warning{else/}danger{/if}">
                                    {if condition='$log.commission_amount eq 0'}无需发放{else/}未发放{/if}
                                </span>
                                {/if}
                            </td>
                            <td>
                                <span class="label label-{if condition='$log.distribution_type eq "online"'}primary{else/}info{/if}">
                                    {$log.distribution_type_text}
                                </span>
                            </td>
                            <td>
                                <small title="{$log.calculation_rule}">
                                    {$log.distribution_memo|default=$log.calculation_rule}
                                </small>
                            </td>
                        </tr>
                        {/volist}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分佣汇总 -->
        <div class="row">
            <div class="col-md-12">
                <h4>分佣汇总</h4>
                <div class="row">
                    <div class="col-md-3">
                        <div class="info-box bg-blue">
                            <span class="info-box-icon"><i class="fa fa-money"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">总分佣金额</span>
                                <span class="info-box-number">
                                    ￥{php}
                                        $total = 0;
                                        foreach($batch_logs as $log) {
                                            $total += $log['commission_amount'];
                                        }
                                        echo number_format($total, 2);
                                    {/php}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box bg-green">
                            <span class="info-box-icon"><i class="fa fa-check"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">已发放金额</span>
                                <span class="info-box-number">
                                    ￥{php}
                                        $distributed = 0;
                                        foreach($batch_logs as $log) {
                                            if($log['is_distributed'] == 1) {
                                                $distributed += $log['commission_amount'];
                                            }
                                        }
                                        echo number_format($distributed, 2);
                                    {/php}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box bg-yellow">
                            <span class="info-box-icon"><i class="fa fa-clock-o"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">待发放金额</span>
                                <span class="info-box-number">
                                    ￥{php}
                                        $pending = 0;
                                        foreach($batch_logs as $log) {
                                            if($log['is_distributed'] == 0 && $log['commission_amount'] > 0) {
                                                $pending += $log['commission_amount'];
                                            }
                                        }
                                        echo number_format($pending, 2);
                                    {/php}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="info-box bg-red">
                            <span class="info-box-icon"><i class="fa fa-users"></i></span>
                            <div class="info-box-content">
                                <span class="info-box-text">参与角色数</span>
                                <span class="info-box-number">
                                    {php}
                                        $roles = [];
                                        foreach($batch_logs as $log) {
                                            if($log['commission_amount'] > 0) {
                                                $roles[$log['commission_user_role']] = 1;
                                            }
                                        }
                                        echo count($roles);
                                    {/php}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
