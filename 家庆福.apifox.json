{"apifoxProject": "1.0.0", "$schema": {"app": "apifox", "type": "project", "version": "1.2.0"}, "info": {"name": "家庆福", "description": "", "mockRule": {"rules": [], "enableSystemRule": true}}, "apiCollection": [{"name": "根目录", "id": 58468296, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "methodAndPath", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "SHARED", "moduleId": 5575437, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "接口", "id": 58468342, "auth": {}, "securityScheme": {}, "parentId": 0, "serverId": "", "description": "", "identityPattern": {"httpApi": {"type": "inherit", "bodyType": "", "fields": []}}, "shareSettings": {}, "visibility": "INHERITED", "moduleId": 5575437, "preProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "postProcessors": [{"id": "inheritProcessors", "type": "inheritProcessors", "data": {}}], "inheritPostProcessors": {}, "inheritPreProcessors": {}, "items": [{"name": "提交技能", "api": {"id": "305602323", "method": "post", "path": "/api/service/skill/saveUserSkill", "parameters": {"path": [], "query": [{"id": "9yp8TiSEHT", "name": "token", "required": false, "description": "", "example": "9f39e131-f676-4a18-9e87-d67b9946128b", "type": "string", "schema": {"type": "string", "default": "9f39e131-f676-4a18-9e87-d67b9946128b"}}], "cookie": [], "header": [{"id": "sqAUneeX7F", "name": "token", "required": false, "description": "", "example": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "type": "string", "schema": {"type": "string", "default": "03e1e9cb-8942-4eb5-8587-db1d63a77e19"}}, {"id": "CyFt6aMhpC", "name": "city", "required": false, "description": "", "example": "%E9%83%91%E5%B7%9E%E5%B8%82", "type": "string", "schema": {"type": "string", "default": "%E9%83%91%E5%B7%9E%E5%B8%82"}}]}, "auth": {}, "securityScheme": {}, "commonParameters": {}, "responses": [{"id": "692266189", "code": 200, "name": "成功", "headers": [], "jsonSchema": {"type": "object", "properties": {}, "x-apifox-orders": []}, "description": "", "contentType": "json", "mediaType": "application/json", "oasExtensions": ""}], "responseExamples": [], "requestBody": {"type": "multipart/form-data", "parameters": [{"id": "jtmN46JLWL", "name": "skill_ids", "required": false, "description": "技能，多个逗号分隔", "example": "1", "type": "string", "schema": {"description": "技能，多个逗号分隔", "type": "string", "examples": ["1", "7,16,19"]}}], "oasExtensions": ""}, "description": "", "tags": [], "status": "released", "serverId": "", "operationId": "", "sourceUrl": "", "ordering": 78, "cases": [{"id": 268988225, "type": "http", "path": null, "name": "成功", "responseId": 692266189, "parameters": {"path": [], "query": [{"name": "token", "value": "9f39e131-f676-4a18-9e87-d67b9946128b", "enable": true, "id": "z7hZmQHeiT", "relatedName": "token"}], "cookie": [], "header": [{"name": "token", "value": "03e1e9cb-8942-4eb5-8587-db1d63a77e19", "enable": true, "id": "E5TeDc6Dgm", "relatedName": "token"}, {"name": "city", "value": "%E9%83%91%E5%B7%9E%E5%B8%82", "enable": true, "id": "HLN2GLECkN", "relatedName": "city"}]}, "commonParameters": {}, "requestBody": {"parameters": [{"name": "skill_ids", "value": "1", "enable": true, "id": "5wpjeY7xwx", "relatedName": "skill_ids"}], "type": "multipart/form-data", "generateMode": "normal"}, "auth": {}, "securityScheme": {}, "advancedSettings": {"disabledSystemHeaders": {}}, "requestResult": null, "visibility": "INHERITED", "moduleId": 5575437, "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}], "mocks": [], "customApiFields": "{}", "advancedSettings": {"disabledSystemHeaders": {}}, "mockScript": {}, "codeSamples": [], "commonResponseStatus": {}, "responseChildren": [], "visibility": "INHERITED", "moduleId": 5575437, "oasExtensions": "", "preProcessors": [], "postProcessors": [], "inheritPostProcessors": {}, "inheritPreProcessors": {}}}]}]}], "socketCollection": [], "docCollection": [], "webSocketCollection": [], "socketIOCollection": [], "responseCollection": [{"_databaseId": 6806054, "updatedAt": "2025-06-07T02:06:57.000Z", "name": "根目录", "type": "root", "children": [], "moduleId": 5575437, "parentId": 0, "id": 6806054, "ordering": [], "items": []}], "schemaCollection": [], "securitySchemeCollection": [], "requestCollection": [{"name": "根目录", "children": [], "ordering": ["requestFolder.6916461"], "items": []}], "environments": [], "commonScripts": [], "globalVariables": [], "commonParameters": null, "projectSetting": {"id": "6561092", "auth": {}, "securityScheme": {}, "servers": [{"id": "default", "name": "默认服务"}], "gateway": [], "language": "zh-CN", "apiStatuses": ["developing", "testing", "released", "deprecated"], "mockSettings": {}, "preProcessors": [], "postProcessors": [], "advancedSettings": {"enableJsonc": false, "enableBigint": false, "responseValidate": true, "enableTestScenarioSetting": false, "enableYAPICompatScript": false, "isDefaultUrlEncoding": 2, "publishedDocUrlRules": {"defaultRule": "RESOURCE_KEY_ONLY", "resourceKeyStandard": "NEW"}}, "initialDisabledMockIds": [], "cloudMock": {"security": "free", "enable": false, "tokenKey": "apifoxToken"}}, "customFunctions": [], "projectAssociations": []}