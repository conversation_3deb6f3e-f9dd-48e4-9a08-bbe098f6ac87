<?php
// 创建课程套餐表的脚本

require_once __DIR__ . '/thinkphp/base.php';

use think\Db;

try {
    // 创建课程套餐表
    $sql = "
    CREATE TABLE IF NOT EXISTS `fa_xiluedu_course_package` (
      `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
      `course_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '课程ID',
      `name` varchar(100) NOT NULL DEFAULT '' COMMENT '套餐名称',
      `description` varchar(255) NOT NULL DEFAULT '' COMMENT '套餐描述',
      `quantity` int(10) NOT NULL DEFAULT '1' COMMENT '套餐数量',
      `original_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '原价',
      `qydl_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '城市负责人价格',
      `sqdl_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '养老院长价格',
      `ylgw_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '养老顾问价格',
      `user_types` varchar(100) NOT NULL DEFAULT '' COMMENT '适用人员类型(qydl,sqdl,ylgw,user)',
      `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
      `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0=禁用,1=启用',
      `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
      `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
      PRIMARY KEY (`id`),
      KEY `course_id` (`course_id`),
      KEY `status` (`status`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程套餐表';
    ";
    
    Db::execute($sql);
    echo "课程套餐表创建成功\n";
    
    // 为课程16添加默认套餐数据
    $packages = [
        [
            'course_id' => 16,
            'name' => '单个购买',
            'description' => '课程16单个购买',
            'quantity' => 1,
            'original_price' => 365.00,
            'qydl_price' => 365.00,
            'sqdl_price' => 365.00,
            'ylgw_price' => 365.00,
            'user_types' => 'qydl,sqdl,ylgw,user',
            'sort' => 1,
            'status' => 1,
            'createtime' => time(),
            'updatetime' => time()
        ],
        [
            'course_id' => 16,
            'name' => '套餐购买(10个)',
            'description' => '课程16套餐购买，10个为一套',
            'quantity' => 10,
            'original_price' => 3650.00,
            'qydl_price' => 1000.00,
            'sqdl_price' => 1300.00,
            'ylgw_price' => 3650.00,
            'user_types' => 'qydl,sqdl',
            'sort' => 2,
            'status' => 1,
            'createtime' => time(),
            'updatetime' => time()
        ]
    ];
    
    foreach ($packages as $package) {
        Db::name('xiluedu_course_package')->insert($package);
    }
    
    echo "默认套餐数据插入成功\n";
    echo "课程套餐管理功能创建完成！\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
