-- 快速修复分佣日志显示问题

-- 1. 创建表
CREATE TABLE IF NOT EXISTS `fa_commission_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `batch_no` varchar(32) NOT NULL DEFAULT '',
  `step_no` tinyint(3) unsigned NOT NULL DEFAULT '1',
  `order_type` varchar(20) NOT NULL DEFAULT '',
  `order_id` int(10) unsigned NOT NULL DEFAULT '0',
  `order_no` varchar(50) NOT NULL DEFAULT '',
  `goods_id` int(10) unsigned NOT NULL DEFAULT '0',
  `goods_name` varchar(255) NOT NULL DEFAULT '',
  `order_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `base_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `commission_user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `commission_user_role` varchar(50) NOT NULL DEFAULT '',
  `buyer_user_id` int(10) unsigned NOT NULL DEFAULT '0',
  `buyer_user_role` varchar(50) NOT NULL DEFAULT '',
  `commission_type` varchar(20) NOT NULL DEFAULT '',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00',
  `commission_amount` decimal(10,2) NOT NULL DEFAULT '0.00',
  `calculation_rule` text,
  `is_distributed` tinyint(1) NOT NULL DEFAULT '0',
  `distribution_type` varchar(10) NOT NULL DEFAULT 'online',
  `distribution_status` varchar(10) NOT NULL DEFAULT 'pending',
  `createtime` bigint(16) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- 2. 插入测试数据
INSERT IGNORE INTO `fa_commission_log` VALUES 
(1, 'test_001', 1, 'course', 1001, 'C001', 10, '测试课程', 1000.00, 1000.00, 2, 'nursing_home_director', 1001, 'regular_user', 'direct', 50.00, 500.00, '测试分佣', 1, 'online', 'success', UNIX_TIMESTAMP()),
(2, 'test_002', 1, 'wanlshop', 2001, 'W001', 201, '测试商品', 200.00, 200.00, 3, 'elderly_advisor', 1002, 'regular_user', 'direct', 30.00, 60.00, '测试分佣', 0, 'offline', 'pending', UNIX_TIMESTAMP());

SELECT '修复完成，请刷新页面' as result;
