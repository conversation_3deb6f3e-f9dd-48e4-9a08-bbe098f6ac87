-- 分佣日志系统
-- 创建专门记录分佣过程的日志表

-- 1. 创建分佣日志表
DROP TABLE IF EXISTS `fa_commission_log`;
CREATE TABLE `fa_commission_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_no` varchar(32) NOT NULL DEFAULT '' COMMENT '批次号（同一订单的所有分佣记录）',
  `step_no` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '步骤序号（同批次内的分佣步骤）',
  
  -- 订单信息
  `order_type` enum('course','offline_course','course_package','wanlshop','service') NOT NULL COMMENT '订单类型',
  `order_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
  `goods_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品/课程/服务ID',
  `goods_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品/课程/服务名称',
  `order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `base_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣计算基数',
  
  -- 分佣角色信息
  `commission_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '获得分佣的用户ID',
  `commission_user_role` enum('platform','city_manager','nursing_home_director','elderly_advisor','teacher','service_provider') NOT NULL COMMENT '获得分佣的用户角色',
  `buyer_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '购买用户ID',
  `buyer_user_role` enum('platform','city_manager','nursing_home_director','elderly_advisor','regular_user') NOT NULL COMMENT '购买用户角色',
  
  -- 分佣计算信息
  `commission_type` enum('direct','indirect','recommendation','teacher','service') NOT NULL COMMENT '分佣类型',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '分佣比例（百分比）',
  `commission_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣金额',
  `calculation_rule` text COMMENT '计算规则说明',
  `config_source` enum('global','goods_level','special_rule') NOT NULL DEFAULT 'global' COMMENT '配置来源',
  `config_value` varchar(100) NOT NULL DEFAULT '' COMMENT '使用的配置值',
  
  -- 发放信息
  `is_distributed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否实际发放（0=未发放，1=已发放）',
  `distribution_type` enum('online','offline') NOT NULL DEFAULT 'online' COMMENT '发放方式',
  `distribution_status` enum('pending','success','failed') NOT NULL DEFAULT 'pending' COMMENT '发放状态',
  `distribution_memo` varchar(500) NOT NULL DEFAULT '' COMMENT '发放备注',
  `money_log_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联的资金变动记录ID',
  
  -- 上级关系信息
  `parent_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上级用户ID',
  `parent_user_role` enum('platform','city_manager','nursing_home_director','elderly_advisor') NOT NULL DEFAULT 'platform' COMMENT '上级用户角色',
  `has_superior` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有上级（影响发放方式）',
  
  -- 时间字段
  `createtime` bigint(16) NOT NULL COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_order` (`order_type`, `order_id`),
  KEY `idx_commission_user` (`commission_user_id`),
  KEY `idx_buyer_user` (`buyer_user_id`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分佣日志表';

-- 2. 创建分佣统计视图（可选）
CREATE OR REPLACE VIEW `v_commission_summary` AS
SELECT 
    `order_type`,
    `commission_user_id`,
    `commission_user_role`,
    COUNT(*) as `commission_count`,
    SUM(`commission_amount`) as `total_commission`,
    SUM(CASE WHEN `is_distributed` = 1 THEN `commission_amount` ELSE 0 END) as `distributed_amount`,
    SUM(CASE WHEN `distribution_type` = 'online' THEN `commission_amount` ELSE 0 END) as `online_amount`,
    SUM(CASE WHEN `distribution_type` = 'offline' THEN `commission_amount` ELSE 0 END) as `offline_amount`
FROM `fa_commission_log`
GROUP BY `order_type`, `commission_user_id`, `commission_user_role`;

-- 3. 添加索引优化查询性能
ALTER TABLE `fa_commission_log` ADD INDEX `idx_commission_type` (`commission_type`);
ALTER TABLE `fa_commission_log` ADD INDEX `idx_distribution` (`is_distributed`, `distribution_type`);
ALTER TABLE `fa_commission_log` ADD INDEX `idx_user_role` (`commission_user_id`, `commission_user_role`);
