<?php
namespace app\api\controller\service;

use app\admin\model\service\order\FixedLog;
use app\admin\model\service\order\ServiceInsurance;
use app\admin\model\service\order\UseLog;
use app\admin\model\service\skill\UserSkill;
use app\api\model\service\OrderAddress;
use app\api\model\service\OrderDetail;
use app\api\model\service\OrderLog;
use app\api\model\service\Skill;
use app\common\controller\Api;
use app\common\model\service\UserBfw;
use app\common\model\User as UserModel;
use think\Db;
use think\Exception;
use think\Loader;


/**
 * 首页接口
 */
class Order extends Api
{
    protected $noNeedLogin = ['refundReason','complaintReason','refundInfo','complaintInfo'];
    protected $noNeedRight = ['*'];


    /**
     * 养老院长获取销售订单
     * <AUTHOR>
     * @date 2024/10/7  上午9:39
     * @notes
     */
    public function getDlOrder()
    {
        $type = $this->request->post('type');
        $order_state = $this->request->post('order_state');
        $service_status = $this->request->post('service_status');
        $start_time = $this->request->post('start_time');
        $end_time = $this->request->post('end_time');
        $service_type = $this->request->post('service_type');
        $user_id = $this->request->post('user_id');
        if($user_id){
            $child_user = [$user_id];
        }else{
           
            if($this->auth->is_ylgw == 1){
                $child_user = UserModel::getAllNonAgentDescendantsYlgw($this->auth->id);
            }else{
                 //查询养老院长下级
                 $child_user = UserModel::getAllNonAgentDescendants($this->auth->id);
            }
        }

        $where = [];
        if($type == 1){//商品订单
           $where['order.user_id'] = ['in',$child_user];
           $where['order.state'] = ['not in',[1,7]];
           if($order_state){
               $where['order.state'] = $order_state;
           }
           if($start_time && $end_time){
               $where['order.createtime'] = ['between',[strtotime($start_time),strtotime($end_time)]];
           }
            // 列表
            $list = model('app\api\model\wanlshop\Order')->alias('order')
                ->join('wanlshop_order_address address','address.order_id = order.id','left')
                ->where($where)
                ->field('order.id,order.user_id,order.order_no,order.shop_id,order.state,order.createtime')
                ->order('order.updatetime desc')
                ->paginate()
                ->each(function($order, $key){
                    $goods = model('app\api\model\wanlshop\OrderGoods')
                        ->where(['order_id'=> $order->id])
                        ->field('id,title,goods_id,image,difference,price,market_price,number,refund_status,shequ_bili,quyu_bili,service_bili')
                        ->select();
                    foreach ($goods as &$v){
                        if($this->auth->is_qydl == 1){
                            $v['yongjin'] = $v['quyu_bili'];
                        }else{
                            $v['yongjin'] = $v['shequ_bili'];
                        }
                    }
                    $order['user'] = model('app\common\model\User')->where(['id'=>$order->user_id])->field('id,nickname,avatar')->find();
                    $order['goods'] = $goods;
                    // 获取支付 1.1.2升级
                    $order['pay'] = model('app\api\model\wanlshop\Pay')
                        ->where(['order_id' => $order->id, 'type' => 'goods'])
                        ->field('number, price, order_price, freight_price, discount_price, actual_payment')
                        ->find();
                    // $order['shop'] = $order->shop?$order->shop->visible(['shopname']):[];
                    $order['createtime'] = date('Y-m-d H:i:s',$order->createtime);
                    return $order;
                });
        }else{//服务订单
            $where['order.user_id'] = ['in',$child_user];
            if($service_type == 1){//家政
                $where['order.is_wt'] = 0;
                $where['order.goods_type'] = 1;
            }else if ($service_type == 2){//医护
                $where['detail.is_yh'] = 1;
            }else if($service_type == 3){//委托
                $where['order.is_wt'] = 1;
            }
            if($start_time && $end_time){
                $where['order.createtime'] = ['between',[strtotime($start_time),strtotime($end_time)]];
            }
            $where['order.status'] = ['not in',[0,-1]];
            if($service_status){
                $where['order.status'] = $service_status;
            }
            $list = \app\api\model\service\Order::alias('order')
                ->join('service_order_detail detail','detail.order_id = order.id','left')
                ->field('order.id,order.user_id,detail.name,detail.sku_name,detail.image,detail.num,order.payprice,order.status,order.createtime,order.starttime,order.shequ_money,order.quyu_money,order.service_money')
                ->where($where)
                ->order('order.createtime desc')
                ->paginate()->each(function ($item){
                    $item['createtime'] = date('Y-m-d H:i:s',$item->createtime);
                    $item['starttime'] = $item->starttime?date('Y-m-d H:i:s',$item->starttime):'';
                    $item['user'] = model('app\common\model\User')->where(['id'=>$item->user_id])->field('id,nickname,avatar')->find();
                    $item['address'] = model('app\api\model\service\OrderAddress')->where(['order_id'=>$item->id])->find();
                    if($this->auth->is_qydl == 1){
                        $item['yongjin'] = $item['quyu_money'];
                    }else{
                        $item['yongjin'] = $item['shequ_money'];
                    }

                    return $item;
                });

        }
        $this->success('ok',$list);
    }

    /**
     * 城市负责人获取销售订单
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2024/10/7  下午1:58
     * @notes
     */
    public function getQyOrder()
    {
        $type = $this->request->post('type');
        $order_state = $this->request->post('order_state');
        $service_status = $this->request->post('service_status');
        $start_time = $this->request->post('start_time');
        $end_time = $this->request->post('end_time');
        $service_type = $this->request->post('service_type');

        $user =$this->auth->getUserinfo();
        $user_id2 =$user['qydl_staff']!=0 ? $user['qydl_staff'] :$this->auth->id;
        //获取此城市负责人代理区域
        $district = model('app\common\model\UserArea')->where('user_id',$user_id2)->value('district');
        if(!$district){
            $this->error('您还不是城市负责人1');
        }

        $where = [];
        if($type == 1){//商品订单
            $user_ids = model('app\common\model\User')->getUserChilds($this->auth->id,true);
            // $where['address.district'] = $district;
            $where['order.user_id'] = ['in',$user_ids];
            $where['order.state'] = ['not in',[1,7]];
            if($order_state){
                $where['order.state'] = $order_state;
            }
            if($start_time && $end_time){
                $where['order.createtime'] = ['between',[strtotime($start_time . ' 00:00:00'),strtotime($end_time.' 23:59:59')]];
            }
            // 列表
            $list = model('app\api\model\wanlshop\Order')->alias('order')
                ->join('wanlshop_order_address address','address.order_id = order.id','left')
                ->where($where)
                ->field('order.id,order.user_id,order.order_no,order.shop_id,order.state,order.createtime')
                ->order('order.updatetime desc')
                ->paginate()
                ->each(function($order, $key){
                    $goods = model('app\api\model\wanlshop\OrderGoods')
                        ->where(['order_id'=> $order->id])
                        ->field('id,title,goods_id,image,difference,price,market_price,number,refund_status,shequ_bili,quyu_bili,service_bili')
                        ->select();
                    $order['user'] = model('app\common\model\User')->where(['id'=>$order->user_id])->field('id,nickname,avatar')->find();
                    $order['goods'] = $goods;
                    // 获取支付 1.1.2升级
                    $order['pay'] = model('app\api\model\wanlshop\Pay')
                        ->where(['order_id' => $order->id, 'type' => 'goods'])
                        ->field('number, price, order_price, freight_price, discount_price, actual_payment')
                        ->find();
                    $order['createtime'] = date('Y-m-d H:i:s',$order->createtime);
                    // $order['shop'] = $order->shop?$order->shop->visible(['shopname']):[];
                    return $order;
                });
        }else{//服务订单
            $where['order.district'] = $district;
            if($service_type == 1){//家政
                $where['order.is_wt'] = 0;
                $where['order.goods_type'] = 1;
            }else if ($service_type == 2){//医护
                $where['detail.is_yh'] = 1;
            }else if($service_type == 3){//委托
                $where['order.is_wt'] = 1;
            }
            if($start_time && $end_time){
                $where['order.createtime'] = ['between',[strtotime($start_time),strtotime($end_time)]];
            }
            $where['order.status'] = ['not in',[0,-1]];
            if($service_status){
                $where['order.status'] = $service_status;
            }
            $list = \app\api\model\service\Order::alias('order')
                ->join('service_order_detail detail','detail.order_id = order.id','left')
                ->field('order.id,order.user_id,detail.name,detail.sku_name,detail.image,detail.num,order.payprice,order.status,order.createtime,order.starttime,order.shequ_money,order.quyu_money,order.service_money')
                ->where($where)
                ->order('order.createtime desc')
                ->paginate()->each(function ($item){
                    $item['createtime'] = date('Y-m-d H:i:s',$item->createtime);
                    $item['starttime'] = $item->starttime?date('Y-m-d H:i:s',$item->starttime):'';
                    $item['user'] = model('app\common\model\User')->where(['id'=>$item->user_id])->field('id,nickname,avatar')->find();
                    $item['address'] = model('app\api\model\service\OrderAddress')->where(['order_id'=>$item->id])->find();
                    return $item;
                });

        }
        $this->success('ok',$list);
    }

    /**
     * 获取销售订单统计
     * <AUTHOR>
     * @date 2024/10/31  上午10:59
     * @notes
     */
    public function getOrderTotal()
    {
        $user_id = $this->request->post('user_id');
        if($user_id){
            $child_user = [$user_id];
        }else{
            //查询养老院长下级
            $child_user = UserModel::getAllNonAgentDescendants($this->auth->id);
        }
        //查询订单总数
        $goods_count = model('app\api\model\wanlshop\Order')->where('user_id','in',$child_user)->where('state','not in',[1,7])->count();
        //服务
        $service_count = model('app\api\model\service\Order')->where('user_id','in',$child_user)->where('status','not in',[0,-1])->count();

        //查询订单总金额
        $goods_price = model('app\api\model\wanlshop\Order')->alias('order')
            ->join('wanlshop_order_goods goods','goods.order_id=order.id','left')
            ->where('order.user_id','in',$child_user)->where('order.state','not in',[1,7])->sum('price');
        $service_price = model('app\api\model\service\Order')->where('user_id','in',$child_user)->where('status','not in',[0,-1])->sum('price');

        if($this->auth->is_qydl == 1){
            //查询待下发佣金
            $goods_d_money = model('app\api\model\wanlshop\Order')->alias('order')
                ->join('wanlshop_order_goods goods','goods.order_id=order.id','left')
                ->where('order.user_id','in',$child_user)
                ->where('order.state','not in',[1,7])
                ->sum('goods.quyu_d_bili');
            //已下发佣金
            $goods_money = model('app\api\model\wanlshop\Order')->alias('order')
                ->join('wanlshop_order_goods goods','goods.order_id=order.id','left')
                ->where('order.user_id','in',$child_user)
                ->where('order.state','not in',[1,7])
                ->sum('goods.quyu_bili');
        }else{
            if($this->auth->is_ylgw == 1){
                $child_user = \app\common\model\User::getAllNonAgentDescendantsYlgw($this->auth->id);
            }else{
                $child_user = \app\common\model\User::getAllNonAgentDescendants($this->auth->id);
            }

            //查询待下发佣金
            $goods_d_money = model('app\api\model\wanlshop\Order')->alias('order')
                ->join('wanlshop_order_goods goods','goods.order_id=order.id','left')
                ->where('order.user_id','in',$child_user)
                ->where('order.state','not in',[1,7])
                ->sum('goods.shequ_d_bili');
            $goods_money = model('app\api\model\wanlshop\Order')->alias('order')
                ->join('wanlshop_order_goods goods','goods.order_id=order.id','left')
                ->where('order.user_id','in',$child_user)
                ->where('order.state','not in',[1,7])
                ->sum('goods.shequ_bili');
        }
        $res = [
            'order_count'=>$goods_count+$service_count,
            'order_price'=>$goods_price+$service_price,
            'all_money'=>$goods_d_money+$goods_money,
            'd_money'=>$goods_d_money,
            'succ_money'=>$goods_money,
            'is_ylgw'=> UserModel::where('id',$this->auth->id)->value('is_ylgw')
        ];
        $this->success('ok',$res);

    }

    /**
     * 调度中心-查询订单
     * @return void
     */
    public function ddOrder()
    {
        $status = $this->request->post('status');
        $get = [];
        //获取此城市负责人代理区域
        $district = model('app\common\model\UserArea')->where('user_id',$this->auth->id)->value('district');
        if(!$district){
            $this->error('您还不是城市负责人');
        }
        $get['district'] = $district;
        $get['page'] = $this->request->post('page');
        if($status == 1){//待接单
            $get['diaodu'] = 'djd';
        }elseif($status ==2 ){//待上门
            $get['diaodu'] = 'dsm';
        }else{
            $get['diaodu'] = 'all';
        }
        $re = [];
        try{
            $re = \app\api\model\service\Order::getOrderList($get);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('信息返回成功',$re);
    }

    /**
     * 调度中心-查询订单详情
     * @return void
     */
    public function ddOrderDetail()
    {
        $id = $this->request->post('id');
        $get = [];
        $get['page']=1;
        //获取此城市负责人代理区域
        $district = model('app\common\model\UserArea')->where('user_id',$this->auth->id)->value('district');
        if(!$district){
            $this->error('您还不是城市负责人');
        }
        $get['id'] = $id;
        $re = [];
        try{
            $re = \app\api\model\service\Order::getOrderList($get);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('信息返回成功',$re[0] ?? '');
    }

    /**
     * 服务订单删除
     * <AUTHOR>
     * @date 2024/9/24  上午10:41
     * @notes
     */
    public function delOrder()
    {
        $id = $this->request->post('id');
        $order = \app\api\model\service\Order::get($id);
        if(!$order){
            $this->error('订单不存在');
        }
        if($order->status != 6 && $order->status != 7 && $order->status != -1){
            $this->error('当前订单状态不可删除');
        }
        $order->deletetime = time();
        $order->save();
        $this->success('删除成功');
    }

    /**
     * 调度中心-指派服务人员
     * <AUTHOR>
     * @date 2024/9/19  下午2:17
     * @notes
     */
    public function zpSkill()
    {
        $order_id = $this->request->post('order_id');
        $skill_id = $this->request->post('skill_id');
        $order =  \app\admin\model\service\order\Order::where('id',$order_id)->field('id,user_id,to_shop,starttime,actendtime')->find();
        $skill = \app\admin\model\service\skill\Skill::where('id',$skill_id)->field('id,shop_id,user_id')->find();
        $update = [];
        $update['accepttime']= time();
        $update['skill_id'] = $skill['id'];
        $update['status'] = 2;
        Db::startTrans();
        try{
            $res = db('service_order')->where('id',$order_id)->update($update);
            if(!$res){
                throw new Exception('网络错误');
            }
            $res2 = (new OrderLog())->insert(['order_id'=>$order_id,'user_id'=>$order['user_id'],'type'=>16,'content'=>'分配服务者','createtime'=>time()]);
            if(!$res2){
                throw new Exception('网络错误');
            }
            \app\api\model\wanlshop\Notice::sendUserMsg($skill['user_id'],'系统自动派单','你收到一个新订单，请及时查看并接单。','service_order',$order->id,1);
            Db::commit();
        }catch (\Exception $e){
            Db::rollback();
            $this->error($e->getMessage().$e->get);
        }
        $this->success('提交成功');

    }

    public function settle()
    {
        $user = $this->auth->getUserinfo();
        $post = $this->request->param();
        $data = [];
        try{
            $data['settle'] = \app\api\model\service\Order::settle($post);
            $data['money'] = \app\api\model\service\User::getMoney($user['id']);
            $data['userInfo'] = \app\api\model\service\UserInfo::where(['user_id'=>$user['id']])->field('is_plus,discount')->find();
            $data['skillInfo'] = isset($post['skill_id'])?\app\api\model\service\Skill::getOrderSkill($post['skill_id']):'';
            $data['timeInfo'] = isset($post['skill_id'])?\app\api\model\service\SkillTime::getSettleTime($post['skill_id']):\app\api\model\service\Shop::getShopTime();
        } catch (Exception $e) {
            $this->error('下单结算失败',$e->getMessage());
        }
        $this->success('信息返回成功',$data);
    }

    /**
     * 计算车费
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function travelPrice()
    {
        $get = input('get.','','trim,strip_tags,htmlspecialchars,xss_clean');
        $goodsConfig = \app\api\model\service\Goods::where(['id'=>$get['goods_id']])->value('is_travel');
        !$goodsConfig && $this->error('当前项目未开启出行费用配置');
        $timeInfo = \app\api\model\service\SkillTime::where(['id'=>$get['time_id'],'state'=>0])->field('id,starttime')->find();
        $hour = date("H",$timeInfo['starttime']);
        $cityConfig = \app\api\model\service\CityConfig::where(['city'=>$get['city']])->find();
        if($get['traveltype'] == 2)
        {

            ($hour>=$cityConfig['bus_end'] || $hour<$cityConfig['bus_start']) && $this->error('当前城市'.$cityConfig['bus_start'].'点-'.$cityConfig['bus_end'].'点可选择公交地铁出行方式');
        }
        $re = '';
        try{
            $re = \app\api\model\service\CityConfig::settleTravelPrice($get);
            $re['skillInfo'] = \app\api\model\service\Skill::checkAcceptState($get['skill_id'],$get['goods_id']);
            $re['cityConfig'] = $cityConfig;
        } catch (Exception $e) {
            $this->error('计算出行费用失败',$e->getMessage());
        }
        $this->success('出行费用返回成功',$re);
    }

    /**
     * 创建订单
     * @return void
     */
    public function createOrder()
    {
        $uid = $this->auth->id;
        $post = input('post.','','trim,strip_tags,htmlspecialchars,xss_clean');
        $validate = Loader::validate('service.Order');
        if(!$validate->scene('add')->check($post)){
            $this->error($validate->getError());
        }

        $re = '';
        Db::startTrans();
        try{
            $post['user_id'] = $uid;
            $post['to_shop'] = 'door';
            $post['traveltype'] = 0;//出行方式
            $post['travel_price'] = 0;//出行金额
            $post['distance'] = 0;//距离
            $post['shop_id'] = '';//距离

            $re = \app\api\model\service\Order::createOrder($post);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            // $this->error($e->getMessage());
            // $e->getMessage().'-'.$e->getLine()
            $this->error('请您点击定位按钮，选择地址后再下单');
        }
        $this->success('支付信息返回成功',$re);
    }

    public function pay()
    {
        $uid = $this->auth->id;
        $id = input('id/d','');
        $paytype = input('paytype/d','');
        $re = '';
        Db::startTrans();
        try{
            $re = \app\api\model\service\Order::pay(['id'=>$id,'user_id'=>$uid,'paytype'=>$paytype]);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('支付信息返回成功',$re);
    }

    /**
     * 订单补差价
     * @return void
     */
    public function premiumOrder()
    {
        $uid = $this->auth->id;
        $id = input('id/d','');
        $price = sprintf("%.2f",input('price',''));
        $price<=0 && $this->error('差价金额异常,无法支付');
        $paytype = input('paytype/d',0);
        $orderExist = \app\api\model\service\Order::where(['id'=>$id,'user_id'=>$uid,'status'=>['in',[1,2,3,4,5]]])->value('id');
        !$orderExist && $this->error('当前订单无法补差价,请联系管理员');
        $re = '';
        Db::startTrans();
        try{
            $re = \app\api\model\service\PremiumOrder::createOrder(['order_id'=>$id,'payprice'=>$price,'user_id'=>$uid,'paytype'=>$paytype]);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error('支付拉起失败',$e->getMessage());
        }
        $this->success('支付信息返回成功',$re);
    }


    /**
     * 附加项目创建订单
     * @return void
     */
    public function addOrder()
    {
        $uid = $this->auth->id;
        $post = input('get.','','trim,strip_tags');
        $re = '';
        Db::startTrans();
        try{
            $re = \app\api\model\service\AddOrder::createOrder(['order_id'=>$post['id'],'add_ids'=>$post['add_ids'],'user_id'=>$uid,'paytype'=>$post['paytype']]);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error('支付拉起失败',$e->getMessage());
        }
        $this->success('支付信息返回成功',$re);
    }

    /**
     * 修改订单信息
     * <AUTHOR>
     * @date 2024/8/26  下午4:15
     * @notes
     */
    public function editOrder()
    {
        $order_id = $this->request->post('order_id');
        $address_id = $this->request->post('address_id');
        $service_time_json = $this->request->post('service_time_json');
        $bfw_id = $this->request->post('bfw_id');
        $skill_id = $this->request->post('skill_id');
        $order = \app\api\model\service\Order::get($order_id);
        if($address_id){
            $address = model('app\api\model\wanlshop\Address')
                ->where(['id' => $address_id])
                ->field('name,mobile,province,city,district,address,formatted_address as area,location')
                ->find()->toArray();
            if(!$address){
                $this->error('用户地址错误');
            }
            $location = explode(',',$address['location']);
            $address['lng'] = $location[0];
            $address['lat'] = $location[1];
            unset($address['location']);
            if($order->skill_id)
            {
                $skillCity = Skill::where('id',$order->skill_id)->value('city');
                if($skillCity != $address['city'])
                {
                    $this->error('上门地址请选择服务者所在城市');
                }
            }
            $address['user_id'] = $order->user_id;
            $address['order_id'] = $order->id;
            $address['updatetime'] = time();
            OrderAddress::where('order_id',$order->id)->update($address);
            $order->city = $address['city'];
            $order->district = $address['district'];
            $order->save();
        }
        //修改服务时间
        if($service_time_json){
            $detailData = [];
            $detailData['service_time_json'] = htmlspecialchars_decode($service_time_json);
            OrderDetail::where('order_id',$order->id)->update($detailData);
        }
        //更换被服务者
        if($bfw_id){
            $bfw = UserBfw::get($bfw_id);
            $detailData = [];
            $detailData['bfw_id'] = $bfw_id;
            $detailData['bfw_name'] = $bfw->name;
            $detailData['bfw_mobile'] = $bfw->mobile;
            $detailData['bfw_sex'] = $bfw->sex;
            OrderDetail::where('order_id',$order->id)->update($detailData);
        }
        //更换服务者
        if($skill_id){
            $order->is_pool = 0;
            $order->skill_id = $skill_id;
            $order->save();
        }
        OrderLog::create(['order_id'=>$order->id,'user_id'=>$order->user_id,'type'=>1,'content'=>'用户修改服务包信息']);
        $this->success('修改成功');
    }

    /**
     * 取消订单
     * <AUTHOR>
     * @date 2024/9/5  下午4:21
     * @notes
     */
    public function cancelOrder()
    {
        $order_id = $this->request->post('order_id');
        $order = \app\api\model\service\Order::get($order_id);
        if($order->status != 0){
            $this->error('当前订单无法取消');
        }
        Db::startTrans();
        try{
            $order->status = -1;
            $order->save();
            OrderLog::create(['order_id'=>$order->id,'user_id'=>$order->user_id,'type'=>19,'content'=>'用户取消订单']);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error('取消订单失败',$e->getMessage());
        }
        $this->success('取消订单成功');
    }
    /**
     * 服务包下单
     * <AUTHOR>
     * @date 2024/8/26  下午5:54
     * @notes
     */
    public function fwOrderNum()
    {
        $order_id = $this->request->post('order_id');
        $address_id = $this->request->post('address_id');
        $skill_id = $this->request->post('skill_id');
        $night_money = $this->request->post('night_money',0);//夜间费用
        $bfw_id = $this->request->post('bfw_id');
        $bfw_name = $this->request->post('bfw_name');
        $bfw_mobile = $this->request->post('bfw_mobile');
        $bfw_sex = $this->request->post('bfw_sex');
        $case = $this->request->post('case');
        $is_yh_tools = $this->request->post('is_yh_tools');
        $is_gm = $this->request->post('is_gm');
        $gm_text = $this->request->post('gm_text');
        $cf_images = $this->request->post('cf_images');
        $yp_images = $this->request->post('yp_images');
        $goodsadd2_ids = $this->request->post('goodsadd2_ids');
        $starttime = $this->request->post('starttime');
        $paytype = $this->request->post('paytype');
        $memo = $this->request->post('memo');

        $order = \app\api\model\service\Order::where('id',$order_id)->find()->toArray();
        if(!$order){
            $this->error('服务包不存在');
        }
//        if($order['goods_type'] != 2){
//            $this->error('数据错误');
//        }
        if($order['total_cost_sy'] < 1){
            $this->error('服务包次数不足');
        }
        $data = $order;
        Db::startTrans();
        try{
            $orderDetails = OrderDetail::where('order_id',$order_id)->field('id,createtime,service_time_json',true)->find();
            if(!$orderDetails){
                throw new Exception('信息错误');
            }
            if ($skill_id) {
                $skill = Skill::skillInfo($skill_id);
                if($skill['is_rest'] == 1)
                {
                    throw new Exception('服务者休息中,无法预约');
                }
                $data['skill_id'] = $skill['id'];
                $data['choose_skill_type'] = 1;
            }
            if($data['coupon_price'] > 0){
                $data['coupon_price'] = truncateDecimal($data['coupon_price']/$data['total_cost_seconds']);//优惠券金额
            }
            $payprice = 0;
            //有医护工具
            if($goodsadd2_ids){
                $payprice += \app\api\model\service\GoodsAdd::where('id','in',$goodsadd2_ids)->sum('price');
                $data['goodsadd2_ids'] = $goodsadd2_ids;
            }
            $data['price'] = $data['sumprice'] = truncateDecimal($data['sumprice']/$data['total_cost_seconds']);//项目价格

            $data['payprice'] = $data['sumprice'] + $payprice + (float)$night_money;//此笔订单加上医护工具和夜间费用
            $data['goods_total_price'] = truncateDecimal($data['goods_total_price']/$data['total_cost_seconds']);//项目价格
            $data['coupon_price'] = truncateDecimal($data['coupon_price']/$data['total_cost_seconds']);//优惠券金额
            if($payprice > 0 || $night_money > 0){
                $data['status'] = 0;
            }else{
                $data['status'] = 1;
            }
            $data['p_order_id'] = $order_id;

            $data['goods_type'] = 0;
            $data['night_money'] = $night_money;
            $data['fwb_price'] = $payprice;
            $data['total_cost_sy']=0;
            $data['is_fwb']=0;
            $data['paytime']=time();
            if($starttime){
                $data['starttime'] = strtotime($starttime);
                // $data['endtime'] = $params['starttime'] + $needtime;
                // $data['actendtime'] = $data['endtime'] + 1799;
            }
            $data['total_cost_seconds']=1;
            unset($data['id']);
            unset($data['createtime']);
            $ordernew = \app\api\model\service\Order::create($data);
            if(!$ordernew){
                throw new Exception('订单创建失败');
            }
            //更新服务包信息
            $update = [
//                'total_cost_seconds'=>$order['total_cost_seconds']-1,
                'total_cost_use_num'=>$order['total_cost_use_num']+1,
                'total_cost_sy'=>$order['total_cost_sy']-1,
            ];
            $res = \app\api\model\service\Order::where('id',$order_id)->update($update);

            UseLog::create(['order_id'=>$order_id,'user_id'=>$order['user_id'],'use_num'=>1,'residue_num'=>$update['total_cost_sy']]);
            if(!$res){
                throw new Exception('服务包信息更新失败');
            }

            $address = model('app\api\model\wanlshop\Address')
                ->where(['id' => $address_id])
                ->field('name,mobile,province,city,district,address,formatted_address as area,location')
                ->find()->toArray();
            if(!$address){
                throw new Exception('用户地址错误');
            }
            $location = explode(',',$address['location']);
            $address['lng'] = $location[0];
            $address['lat'] = $location[1];
            unset($address['location']);
            if($skill_id)
            {
                $skillCity = Skill::where('id',$skill_id)->value('city');
                if($skillCity != $address['city'])
                {
                    throw new Exception('上门地址请选择服务者所在城市');
                }
            }
            $address['user_id'] = $ordernew->user_id;
            $address['order_id'] = $ordernew->id;
            $res = OrderAddress::create($address);
            if(!$res){
                throw new Exception('下单失败');
            }
            $orderDetails=$orderDetails->toArray();

            $orderDetails['order_id'] = $ordernew->id;
            $orderDetails['skill_id'] = $ordernew->skill_id;
            $orderDetails['sumprice'] = $data['sumprice'];

            $orderDetails['bfw_id'] = $bfw_id;
            $orderDetails['bfw_name'] = $bfw_name;
            $orderDetails['bfw_mobile'] = $bfw_mobile;
            $orderDetails['bfw_sex'] = $bfw_sex;
            $orderDetails['case'] = $case;
            $orderDetails['is_yh_tools'] = $is_yh_tools;
            $orderDetails['is_gm'] = $is_gm;
            $orderDetails['gm_text'] = $gm_text;
            $orderDetails['cf_images'] = $cf_images;
            $orderDetails['yp_images'] = $yp_images;
            $orderDetail = new OrderDetail();
            $res= $orderDetail->allowField(true)->insert($orderDetails);
//            OrderDetail::create($orderDetails);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage().'-'.$e->getLine());
        }
        $this->success('下单成功',$ordernew);
    }

    public function fwOrderNum1()
    {
        $uid = $this->auth->id;
        $post = input('post.','','trim,strip_tags,htmlspecialchars,xss_clean');
        $validate = Loader::validate('service.Order');
        if(!$validate->scene('add')->check($post)){
            $this->error($validate->getError());
        }
        $re = '';
        Db::startTrans();
        try{
            $post['user_id'] = $uid;
            $post['to_shop'] = 'door';
            $post['traveltype'] = 0;//出行方式
            $post['travel_price'] = 0;//出行金额
            $post['distance'] = 0;//距离
            $post['shop_id'] = '';//距离
            $re = \app\api\model\service\Order::createOrderNum($post);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('支付信息返回成功',$re);
    }

    /**
     * 用户订单列表
     * @return void
     */
    public function userOrderList()
    {
        $uid = $this->auth->id;
        $get = $this->request->param();
        if(isset($get['is_qydl']) && $get['is_qydl'] == 1){
            //获取此城市负责人代理区域
            $district = model('app\common\model\UserArea')->where('user_id',$this->auth->id)->value('district');
            if(!$district){
                $this->error('您还不是城市负责人');
            }
            $get['district'] = $district;
        }else{
            $get['user_id'] = $uid;
        }

        $re = [];
        try{
            $re = \app\api\model\service\Order::getOrderList($get);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('信息返回成功',$re);
    }

    /**
     * 我的服务包
     * <AUTHOR>
     * @date 2024/8/26  下午3:23
     * @notes
     */
    public function userFwb()
    {
        $uid = $this->auth->id;
        $get = $this->request->param();
        $get['user_id'] = $uid;
//        $get['goods_type'] = '1,2';
        $get['not_pay'] = '1';
        $get['is_fwb'] = 1;
        $re = [];
        try{
            $get['my_fwb']=1;
            $re = \app\api\model\service\Order::getOrderList($get);
        } catch (Exception $e) {
            $this->error($e->getMessage().'line-'.$e->getLine());
        }
        $this->success('信息返回成功',$re);
    }

    /**
     * 商户端订单列表
     * @return void
     */
    public function shopOrderList()
    {
        $uid = $this->auth->id;
        $shop = \app\api\model\service\Shop::where(['user_id'=>$uid])->field('id,goods_ids,city')->find();
        $get = input('get.','','trim,strip_tags');
        if(!isset($get['is_pool']))
        {
            $get['shop_id'] = $shop['id'];
        }else{
            $get['city'] = $shop['city'];
            $get['is_pool'] = 1;
            $get['goods_ids'] = $shop['goods_ids'];
        }
        $re = [];
        try{
            $re = \app\api\model\service\Order::getOrderList($get);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('信息返回成功',$re);
    }

    /**
     * 服务者订单列表
     * @return void
     */
    public function skillOrderList()
    {
        $uid = $this->auth->id;
        $skill = \app\api\model\service\Skill::where(['user_id'=>$uid])->field('id,lng,lat,goods_ids,city,hx_range,zd_range,qt_range')->find();
//        dump($skill);
        !$skill && $this->error('请先入驻为服务者');

        $get = $this->request->param();
        $get['lng'] = $skill['lng'];
        $get['lat'] = $skill['lat'];
        if(!isset($get['is_pool']) || $get['is_pool']==0)
        {
            $get['skill_id'] = $skill['id'];
        }else{
            // $get['city'] = $skill['city'];
            $district = $skill['hx_range'].','.$skill['zd_range'].','.$skill['qt_range'];

            // 使用 explode 函数将字符串转换为数组
            $array = explode(",", $district);
            // 使用 array_filter 去除空数据
            $filtered_array = array_filter($array);
            // 使用 array_unique 去除重复数据
            $unique_array = array_unique($filtered_array);
            if(empty($get['district'])) {
                $get['district'] = $unique_array;
            }
            // $get['goods_ids'] = $skill['goods_ids'];
            if(isset($get['skill_order']) && $get['skill_order'] ==1 )
            $get['user_skill']=UserSkill::where(['user_id'=>$uid])->value('skill_ids');
        }
        $get['to_skill_id'] = $skill['id'];
        $get['my_order']=0;
        $re = [];
        try{
            if(isset($get['is_pool']) && $get['is_pool']==1){
                $get['is_fwb']=0;
                $get['qiangdan'] =1;
            }
            $re = \app\api\model\service\Order::getOrderList($get);
        } catch (Exception $e) {
            $this->error($e->getMessage().'line-'.$e->getLine());
        }
        $this->success('信息返回成功',$re);
    }

    /**
     * 服务者订单搜索接口（增强版）
     * @return void
     */
    public function searchOrders()
    {
        $uid = $this->auth->id;
        $skill = \app\api\model\service\Skill::where(['user_id'=>$uid])->field('id,lng,lat,goods_ids,city,hx_range,zd_range,qt_range')->find();

        if (!$skill) {
            $this->error('请先入驻为服务者');
        }

        // 获取搜索参数
        $params = $this->request->param();

        // 基础参数设置
        $get = [
            'lng' => $skill['lng'],
            'lat' => $skill['lat'],
            'to_skill_id' => $skill['id'],
            'my_order' => 0
        ];

        // 处理订单池参数
        if (!isset($params['is_pool']) || $params['is_pool'] == 0) {
            $get['skill_id'] = $skill['id'];
        } else {
            $district = $skill['hx_range'].','.$skill['zd_range'].','.$skill['qt_range'];
            $array = explode(",", $district);
            $filtered_array = array_filter($array);
            $unique_array = array_unique($filtered_array);

            if (empty($params['district'])) {
                $get['district'] = $unique_array;
            }

            if (isset($params['skill_order']) && $params['skill_order'] == 1) {
                $get['user_skill'] = \app\admin\model\service\skill\UserSkill::where(['user_id'=>$uid])->value('skill_ids');
            }

            $get['is_fwb'] = 0;
            $get['qiangdan'] = 1;
        }

        // 新增搜索参数
        $searchParams = [
            'status',           // 订单状态
            'orderId',          // 订单号
            'user_mobile',      // 用户手机号
            'user_nickname',    // 用户昵称
            'skill_name',       // 服务者姓名
            'shop_name',        // 商家名称
            'goods_name',       // 服务项目名称
            'min_price',        // 最小金额
            'max_price',        // 最大金额
            'start_time',       // 创建开始时间
            'end_time',         // 创建结束时间
            'service_start_time', // 服务开始时间
            'service_end_time',   // 服务结束时间
            'city',             // 城市
            'district',         // 区域
            'goods_type',       // 服务类型
            'page',             // 页码
            'limit'             // 每页数量
        ];

        // 将搜索参数添加到查询条件中
        foreach ($searchParams as $param) {
            if (isset($params[$param]) && $params[$param] !== '') {
                $get[$param] = $params[$param];
            }
        }

        try {
            $result = \app\api\model\service\Order::getOrderList($get);
            $this->success('搜索成功', $result);
        } catch (\Exception $e) {
            $this->error('搜索失败：' . $e->getMessage());
        }
    }

    /**
     * 订单详情
     * @return void
     */
    public function orderInfo()
    {
        $id = $this->request->param('id/d',0);
        $order = \app\api\model\service\Order::getOrderInfo($id);
        $this->success('信息返回成功',$order);
    }


    /**
     * 申请退款
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function refundOrder()
    {
        $uid = $this->auth->id;
        $id = input('id/d','');

        $refundOrder = \app\api\model\service\RefundOrder::where(['user_id'=>$uid,'order_id'=>$id])->order('id desc')->find();
        $type = input('type','');
        if($type == 'refund')
        {
            $post = input('post.','','trim,strip_tags');
            $post['user_id'] = $uid;
            try{
                \app\api\model\service\RefundOrder::applyRefund($post);
            } catch (Exception $e) {
                $this->error($e->getMessage());
            }
            $this->success('已申请,等待管理员审核');
        }
        $this->success('信息返回成功',$refundOrder);
    }


    public function refundReason()
    {
        $list = \app\api\model\service\RefundReason::where(['state'=>1])->field('id,name')->select();
        $this->success('信息返回成功',$list);
    }


    public function complaintReason()
    {
        $list = \app\api\model\service\ComplaintReason::where(['state'=>1])->field('id,name')->select();
        $this->success('信息返回成功',$list);
    }


    /**
     * 订单投诉
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function complaint()
    {
        $post = input('post.','','trim,strip_tags');
        $uid = $this->auth->id;
        $order = \app\api\model\service\Order::where(['user_id'=>$uid,'id'=>$post['order_id']])->field('id,user_id,is_complaint')->find();
        (!$order && $order['is_complaint'] == 1)&& $this->error('当前订单无法投诉');
        $post['user_id'] = $uid;
        try{
            \app\api\model\service\Complaint::create($post);
            \app\api\model\service\OrderLog::create(['order_id'=>$order['id'],'user_id'=>$order['user_id'],'type'=>11,'content'=>'订单已投诉']);
            \app\api\model\service\Order::where(['id'=>$post['order_id']])->update(['is_complaint'=>1]);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('投诉已发送给管理员,等待管理员审核');
    }

    public function shopAccept()
    {
        $uid = $this->auth->id;
        $shop = \app\api\model\service\Shop::where(['user_id'=>$uid])->field('id,accept_nums')->find();
        $id = input('id/d','');
        $starttime = \app\api\model\service\Order::where(['id'=>$id,'status'=>1])->value('starttime');
        $orderCount = \app\api\model\service\Order::getTotalAccept(['shop_id'=>$shop['id'],'starttime'=>$starttime]);
        $orderCount>=$shop['accept_nums'] && $this->error('订单接取已达到上限',[],2);
        Db::startTrans();
        $order = \app\api\model\service\Order::where(['id'=>$id,'status'=>1])->lock(true)->find();
        $order['shop_id'] = $shop['id'];
        try{
            \app\api\model\service\Order::shopAccept($order);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error('订单接取失败',$e->getMessage());
        }
        $this->success('订单已接取');
    }


    /**
     * 服务者接取订单
     * @return void
     */
    public function skillAccept()
    {
        $uid = $this->auth->id;
        $skill = \app\api\model\service\Skill::where(['user_id'=>$uid])->field('id,user_id,accept_nums,shop_id,is_rest')->find();
        $id = input('id/d','');
        $starttime = \app\api\model\service\Order::where(['id'=>$id,'status'=>1])->value('starttime');
        if($skill['shop_id'])
        {
            $shop = \app\api\model\service\Shop::where(['id'=>$skill['shop_id']])->field('id,accept_nums')->find();
            $orderCount = \app\api\model\service\Order::getTotalAccept(['shop_id'=>$skill['shop_id'],'starttime'=>$starttime]);
            // $orderCount>=$shop['accept_nums'] && $this->error('商户订单接取已达到上限',[],2);
        }else{
            $orderCount = \app\api\model\service\Order::getTotalAccept(['skill_id'=>$skill['id'],'starttime'=>$starttime]);
            // $orderCount>=$skill['accept_nums'] && $this->error('服务者订单接取已达到上限',[],2);
        }

        $user=$this->auth->getUserinfo();
        if($user['skill_status'] =='hidden' && $user->hidden_time > time() ) $this->error('您的账号已被冻结！','',501);

        Db::startTrans();
        $order = \app\api\model\service\Order::where(['id'=>$id,'status'=>1])->lock(true)->find();
        if(!$order){
            $this->error('订单不存在或已接取');
        }
        //查询服务者是否有此技能
        $goods_skill_cate_ids = \app\api\model\service\Order::alias('o')
            ->join('service_goods g','o.goods_id=g.id')
            ->where(['o.id'=>$id])
            ->value('g.skill_cate_ids');
        $is_skill = false;
        $skill_cate_ids = explode(',',$goods_skill_cate_ids);
        foreach ($skill_cate_ids as $skill_cate_id)
        {
            $skill_cate_id = intval($skill_cate_id);
            $is_e = UserSkill::where(['user_id'=>$skill['user_id']])
                ->where('find_in_set(:id,skill_ids)',['id'=>$skill_cate_id])
                ->count();
            if($is_e > 0)
            {
                $is_skill = true;
                break;
            }
        }
        if($is_skill == false){
            $this->error('服务者未获得此技能');
        }
        try{
            \app\api\model\service\Order::accept(['id'=>$order['id'],'skill_id'=>$skill['id'],'shop_id'=>$skill['shop_id']]);
            $skill->is_rest=2;
            $skill->save();
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error('订单接取失败',$e->getMessage().'line-'.$e->getLine());
        }
        $goods_name = \app\admin\model\service\Goods::where(['id'=>$order['goods_id']])->value('name');
        \app\api\model\wanlshop\Notice::sendServiceMsg($order['user_id'],'服务通知','您的('.$goods_name.'）订单已被接单，请保持电话畅通，以便及时沟通！可在服务订单内查看详情。','service',$id,$this->auth->id);
        $this->success('订单已接取');
    }


    public function skillGo()
    {
        $uid = $this->auth->id;
        $id = input('id/d','');
        try{
            \app\api\model\service\Order::go(['id'=>$id,'user_id'=>$uid]);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('服务人员已出发');
    }

    /**
     * 服务人员到达
     * <AUTHOR>
     * @date 2024/8/21  下午5:12
     * @notes
     */
    public function skillReach()
    {
        $uid = $this->auth->id;
        $id = input('id/d','');
        $reachImages = input('reach_images','','trim,strip_tags');
        try{
            \app\api\model\service\Order::reach(['id'=>$id,'user_id'=>$uid,'reach_images'=>$reachImages]);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }

        $this->success('服务人员已到达');
    }

    /**
     * 开始服务
     * <AUTHOR>
     * @date 2024/8/21  下午5:13
     * @notes
     */
    public function skillStart()
    {
        $id = input('id/d','');
        $startImages  = input('start_images','','trim,strip_tags');
        try{
            \app\api\model\service\Order::skillStart(['id'=>$id,'start_images'=>$startImages]);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('服务人员已开始服务');
    }

    /**
     * 用户确认订单
     * <AUTHOR>
     * @date 2024/8/21  下午5:13
     * @notes
     */
    public function userConfirm()
    {
        $id = input('id/d','');

        try{
            Db::startTrans();
            \app\api\model\service\Order::userConfirm(['id'=>$id]);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('确认完成');
    }


    /**
     * 开始服务
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2024/8/21  下午6:10
     * @notes
     */
    public function skillFinish()
    {
        $uid = $this->auth->id;
        $skill = \app\api\model\service\Skill::where(['user_id'=>$uid])->find();
        $get = input();
        $get['skill_id'] = $skill->id;
        Db::startTrans();
//        $order = \app\api\model\service\Order::where(['id'=>$get['id']])->lock(true)->find();
        try{
            \app\api\model\service\Order::skillFinish($get);
            $skill->is_rest=0;
            $skill->save();
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        //扣除保险费
        (new ServiceInsurance())->add($uid,$get['id']);
        $this->success('订单已确认完成');
    }

    public function checkOrder()
    {
        $uid = $this->auth->id;
        $shopId = \app\api\model\service\Shop::where(['user_id'=>$uid])->value('id');
        $checkName = input('check_name','','trim,strip_tags');
        Db::startTrans();
        $order = \app\api\model\service\Order::where(['check_name'=>$checkName,'shop_id'=>$shopId,'status'=>2])->lock(true)->find();
        try{
            \app\api\model\service\Order::checkOrder($order);
            Db::commit();
        } catch (Exception $e) {
            Db::rollback();
            $this->error($e->getMessage());
        }
        $this->success('核销成功');
    }


    public function skillSettleOrder()
    {
        $uid = $this->auth->id;
        $skill = \app\api\model\service\Skill::where(['user_id'=>$uid])->field('id,shop_id')->find();
        $get = input('get.','','trim,strip_tags');
        $get['user_id'] = $uid;
        $get['skill_id'] = $skill['id'];
        if($get['types'] == 1)
        {
            $get['shop_id'] = $skill['shop_id'];
        }
        $get['type'] = 0;
        $re = '';
        try{
            $re = \app\api\model\service\Order::searchSettleOrder($get);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('信息返回成功',$re);
    }

    public function shopSettleOrder()
    {
        $uid = $this->auth->id;
        $shop = \app\api\model\service\Shop::where(['user_id'=>$uid])->field('id')->find();
        $get = input('get.','','trim,strip_tags');
        $get['user_id'] = $uid;
        $get['shop_id'] = $shop['id'];
        $get['type'] = 1;
        $re = '';
        try{
            $re = \app\api\model\service\Order::searchSettleOrder($get);
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
        $this->success('信息返回成功',$re);
    }

    public function priceInfo()
    {
        $id = input('id/d','');
        $order = \app\api\model\service\Order::where(['id'=>$id])->field('id,skill_id,shop_id,price,travel_price,sumprice,goods_total_price,coupon_price,premium_price,add_price,payprice,act_travel_price,refund_price,settle_price')->find();
        $order['skillPrice'] = $order['skill_id']?\app\api\model\service\Rebate::where(['order_id'=>$id,'type'=>0,'rebatetype'=>['<>',2]])->value('num'):0;
        $order['shopPrice'] = $order['shop_id']?\app\api\model\service\Rebate::where(['order_id'=>$id,'type'=>1,'rebatetype'=>['<>',2]])->value('num'):0;
        $this->success('信息返回成功',$order);
    }


    /**
     * 获取退款信息
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * <AUTHOR>
     * @date 2024/8/10  下午6:07
     * @notes
     */
    public function refundInfo()
    {
        $id = input('order_id','');
        $refundOrder = \app\api\model\service\RefundOrder::where('order_id',$id)->order('id desc')->find();
        if($refundOrder){
            $refundOrder['images'] = $refundOrder['images']?explode(',',$refundOrder['images']):'';
        }
        $this->success('信息返回成功',$refundOrder);
    }

    public function cancelRefund()
    {
        $id = input('order_id','');
        $refundOrder = \app\api\model\service\RefundOrder::where(['order_id'=>$id,'state'=>0])->find();
        !$refundOrder && $this->error('无法取消申请,请联系管理员');
        try{
            \app\api\model\service\RefundOrder::cancelRefundOrder($refundOrder);
            $this->success('申请退款已取消');
        } catch (Exception $e) {
            $this->error($e->getMessage());
        }
    }


    public function complaintInfo()
    {
        $id = input('order_id','');
        $complaint = \app\api\model\service\Complaint::where('order_id',$id)->find();
        if($complaint)
        {
            $complaint['images'] = $complaint['images']?explode(',',$complaint['images']):'';
        }
        $this->success('信息返回成功',$complaint);
    }



    public function fwbUseLog()
    {
        $order_id=$this->request->param('order_id');
        $list=UseLog::where(['user_id'=>$this->auth->id,'order_id'=>$order_id])->order('id desc')->paginate(15);
        $this->success('',$list);
    }

    /**
     * 用户确认到达
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function sureReach()
    {
        $params =$this->request->param();
        $order = \app\api\model\service\Order::where(['id'=>$params['id']])->find();
        if(empty($order) || $order['status']!=4)    $this->error('订单状态异常,无法操作');
        OrderLog::create(['order_id'=>$params['id'],'user_id'=>$order['user_id'],'type'=>8,'content'=>'确认服务人员到达开始服务']);
        $order->status=8;
        $order->save();
        $this->success('操作成功');
    }

    public function dsOrderReach()
    {
        $order_id = $this->request->param('order_id');
        $imgs = $this->request->param('imgs');
        $order = \app\api\model\service\Order::where(['id'=>$order_id])->find();
        if(empty($order) || $order['status']!=1)    $this->error('订单状态异常,无法操作');
//        $skill = \app\admin\model\service\skill\Skill::where(['user_id'=>$this->auth->id])->find();
        $f=FixedLog::create([
           'order_id'=>$order_id,
           'user_id'=>$order['user_id'],
           'skill_user_id'=>$this->auth->id,
           'reach_time'=>date('Y-m-d H:i:s',time()),
           'reach_imgs'=>$imgs,
           'num'=>$order['total_cost_seconds']-$order['total_cost_sy']+1,
        ]);

        $this->success('ok',$f);

    }


}
