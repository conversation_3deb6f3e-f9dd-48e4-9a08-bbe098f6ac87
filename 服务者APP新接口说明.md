# 服务者APP新接口说明

## 1. 手签板签名图片处理接口

### 接口地址
`POST /api/service/index/signatureProcess`

### 接口说明
接收前端传来的签字图片，将其合成到协议图片下方，生成新图片并返回。

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| signature_image | string | 是 | 签名图片，支持base64格式或图片URL |
| agreement_template | string | 否 | 协议模板类型，默认为default，可选值：default、service、privacy |

### 返回示例
```json
{
    "code": 1,
    "msg": "签名处理成功",
    "time": "2024-01-01 12:00:00",
    "data": {
        "image_url": "https://cdn.example.com/service/signature/20240101/merged_1234567890_1234.png"
    }
}
```

### 协议模板配置
需要在 `public/assets/img/agreement/` 目录下放置协议模板图片：
- `default_agreement.png` - 默认协议模板
- `service_agreement.png` - 服务协议模板  
- `privacy_agreement.png` - 隐私协议模板

### 签名图片要求
- 支持PNG格式（推荐透明背景）
- 建议尺寸不超过协议图片宽度的30%
- 支持base64和URL两种格式

---

## 2. APP升级更新内容接口

### 接口地址
`GET /api/service/index/appUpdate`

### 接口说明
返回后台配置的app升级信息，包括版本号、升级内容等。

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| platform | string | 否 | 平台类型，android或ios，默认android |
| version | string | 否 | 当前版本号，默认1.0.0 |

### 返回示例
```json
{
    "code": 1,
    "msg": "发现新版本",
    "time": "2024-01-01 12:00:00",
    "data": {
        "hasUpdate": true,
        "currentVersion": "1.0.0",
        "newVersion": "1.1.0",
        "versionName": "v1.1.0",
        "title": "新版本更新",
        "content": "1. 修复已知问题\n2. 优化用户体验\n3. 新增功能特性",
        "downloadUrl": "https://example.com/app-v1.1.0.apk",
        "packageSize": "25.6MB",
        "enforce": 0,
        "type": "release",
        "createtime": 1704067200
    }
}
```

### 无更新时返回
```json
{
    "code": 1,
    "msg": "当前已是最新版本",
    "time": "2024-01-01 12:00:00",
    "data": {
        "hasUpdate": false,
        "currentVersion": "1.1.0"
    }
}
```

---

## 3. 订单搜索接口（增强版）

### 接口地址
`GET /api/service/order/searchOrders`

### 接口说明
在现有订单搜索基础上增加更多搜索参数支持，如订单号、用户信息等。

### 请求参数
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| status | int | 否 | 订单状态 |
| orderId | string | 否 | 订单号（模糊搜索） |
| user_mobile | string | 否 | 用户手机号（模糊搜索） |
| user_nickname | string | 否 | 用户昵称（模糊搜索） |
| skill_name | string | 否 | 服务者姓名（模糊搜索） |
| shop_name | string | 否 | 商家名称（模糊搜索） |
| goods_name | string | 否 | 服务项目名称（模糊搜索） |
| min_price | float | 否 | 最小金额 |
| max_price | float | 否 | 最大金额 |
| start_time | string | 否 | 创建开始时间（时间戳或日期字符串） |
| end_time | string | 否 | 创建结束时间（时间戳或日期字符串） |
| service_start_time | string | 否 | 服务开始时间（时间戳或日期字符串） |
| service_end_time | string | 否 | 服务结束时间（时间戳或日期字符串） |
| city | string | 否 | 城市 |
| district | string | 否 | 区域 |
| goods_type | string | 否 | 服务类型 |
| is_pool | int | 否 | 是否订单池，0=个人订单，1=订单池 |
| page | int | 否 | 页码，默认1 |
| limit | int | 否 | 每页数量，默认10 |

### 返回示例
```json
{
    "code": 1,
    "msg": "搜索成功",
    "time": "2024-01-01 12:00:00",
    "data": [
        {
            "id": 1,
            "orderId": "GoodsOrd1234-123-1704067200",
            "user_id": 123,
            "skill_id": 456,
            "shop_id": 789,
            "payprice": 100.00,
            "status": 1,
            "createtime": 1704067200,
            "starttime": 1704070800,
            "orderDetail": {
                "name": "家政服务",
                "image": "https://example.com/image.jpg",
                "sku_name": "标准套餐",
                "num": 1,
                "price": 100.00
            },
            "address": {
                "name": "张三",
                "mobile": "13800138000",
                "address": "北京市朝阳区xxx街道"
            }
        }
    ]
}
```

### 搜索功能说明
1. **订单号搜索**：支持模糊匹配订单号
2. **用户信息搜索**：可通过手机号或昵称搜索相关订单
3. **服务者搜索**：可通过服务者姓名搜索其接单记录
4. **商家搜索**：可通过商家名称搜索相关订单
5. **服务项目搜索**：可通过项目名称搜索相关订单
6. **金额范围搜索**：支持设置最小和最大金额范围
7. **时间范围搜索**：支持按创建时间和服务时间范围搜索
8. **地理位置搜索**：支持按城市和区域搜索
9. **订单池支持**：可搜索个人订单或订单池订单

---

## 使用注意事项

1. **签名接口**：
   - 需要确保协议模板图片已正确放置在指定目录
   - 签名图片建议使用透明背景的PNG格式
   - 如果配置了OSS，图片会自动上传到OSS存储

2. **升级接口**：
   - 需要在后台版本管理中配置相应的版本信息
   - 支持强制更新和可选更新两种模式
   - 版本比较基于语义化版本号

3. **搜索接口**：
   - 所有搜索参数都是可选的，可以组合使用
   - 时间参数支持时间戳和日期字符串两种格式
   - 搜索结果会根据服务者权限自动过滤

4. **权限说明**：
   - 签名接口和升级接口无需登录即可访问
   - 搜索接口需要服务者身份验证
   - 所有接口都遵循现有的权限控制机制
