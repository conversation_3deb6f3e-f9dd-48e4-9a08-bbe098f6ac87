-- 修复分佣日志显示问题
-- 1. 确保数据库表存在
-- 2. 插入测试数据
-- 3. 检查菜单权限

-- 检查表是否存在，如果不存在则创建
CREATE TABLE IF NOT EXISTS `fa_commission_log` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `batch_no` varchar(32) NOT NULL DEFAULT '' COMMENT '批次号（同一订单的所有分佣记录）',
  `step_no` tinyint(3) unsigned NOT NULL DEFAULT '1' COMMENT '步骤序号（同批次内的分佣步骤）',
  
  -- 订单信息
  `order_type` enum('course','offline_course','course_package','wanlshop','service') NOT NULL COMMENT '订单类型',
  `order_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `order_no` varchar(50) NOT NULL DEFAULT '' COMMENT '订单号',
  `goods_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '商品/课程/服务ID',
  `goods_name` varchar(255) NOT NULL DEFAULT '' COMMENT '商品/课程/服务名称',
  `order_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '订单总金额',
  `base_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣计算基数',
  
  -- 分佣角色信息
  `commission_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '获得分佣的用户ID',
  `commission_user_role` enum('platform','city_manager','nursing_home_director','elderly_advisor','teacher','service_provider') NOT NULL COMMENT '获得分佣的用户角色',
  `buyer_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '购买用户ID',
  `buyer_user_role` enum('platform','city_manager','nursing_home_director','elderly_advisor','regular_user') NOT NULL COMMENT '购买用户角色',
  
  -- 分佣计算信息
  `commission_type` enum('direct','indirect','recommendation','teacher','service') NOT NULL COMMENT '分佣类型',
  `commission_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '分佣比例（百分比）',
  `commission_amount` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '分佣金额',
  `calculation_rule` text COMMENT '计算规则说明',
  `config_source` enum('global','goods_level','special_rule') NOT NULL DEFAULT 'global' COMMENT '配置来源',
  `config_value` varchar(100) NOT NULL DEFAULT '' COMMENT '使用的配置值',
  
  -- 发放信息
  `is_distributed` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否实际发放（0=未发放，1=已发放）',
  `distribution_type` enum('online','offline') NOT NULL DEFAULT 'online' COMMENT '发放方式',
  `distribution_status` enum('pending','success','failed') NOT NULL DEFAULT 'pending' COMMENT '发放状态',
  `distribution_memo` varchar(500) NOT NULL DEFAULT '' COMMENT '发放备注',
  `money_log_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '关联的资金变动记录ID',
  
  -- 上级关系信息
  `parent_user_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '上级用户ID',
  `parent_user_role` enum('platform','city_manager','nursing_home_director','elderly_advisor') NOT NULL DEFAULT 'platform' COMMENT '上级用户角色',
  `has_superior` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否有上级（影响发放方式）',
  
  -- 时间字段
  `createtime` bigint(16) NOT NULL COMMENT '创建时间',
  
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_order` (`order_type`, `order_id`),
  KEY `idx_commission_user` (`commission_user_id`),
  KEY `idx_buyer_user` (`buyer_user_id`),
  KEY `idx_createtime` (`createtime`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分佣日志表';

-- 插入一些测试数据
INSERT INTO `fa_commission_log` (`batch_no`, `step_no`, `order_type`, `order_id`, `order_no`, `goods_id`, `goods_name`, `order_amount`, `base_amount`, `commission_user_id`, `commission_user_role`, `buyer_user_id`, `buyer_user_role`, `commission_type`, `commission_rate`, `commission_amount`, `calculation_rule`, `is_distributed`, `distribution_type`, `distribution_status`, `createtime`) VALUES
('course_1001_' + UNIX_TIMESTAMP(), 1, 'course', 1001, 'C202412240001', 10, '养老院长课程', 6800.00, 6800.00, 2, 'nursing_home_director', 1001, 'regular_user', 'direct', 50.00, 3400.00, '养老院长直接分佣50%', 1, 'online', 'success', UNIX_TIMESTAMP()),
('course_1001_' + UNIX_TIMESTAMP(), 2, 'course', 1001, 'C202412240001', 10, '养老院长课程', 6800.00, 3400.00, 3, 'elderly_advisor', 1001, 'regular_user', 'indirect', 50.00, 1700.00, '养老顾问获得养老院长分佣的50%', 0, 'offline', 'pending', UNIX_TIMESTAMP()),
('wanlshop_2001_' + UNIX_TIMESTAMP(), 1, 'wanlshop', 2001, 'W202412240001', 201, '养老用品', 299.00, 299.00, 4, 'elderly_advisor', 1002, 'regular_user', 'direct', 30.00, 89.70, '养老顾问商城分佣30%', 1, 'online', 'success', UNIX_TIMESTAMP()),
('service_3001_' + UNIX_TIMESTAMP(), 1, 'service', 3001, 'S202412240001', 301, '上门护理服务', 500.00, 500.00, 5, 'service_provider', 1003, 'elderly_advisor', 'service', 20.00, 100.00, '服务者分佣20%', 0, 'online', 'pending', UNIX_TIMESTAMP());

-- 检查菜单是否存在
SELECT '=== 检查分佣管理菜单 ===' as info;
SELECT id, pid, name, title, icon, ismenu, status 
FROM fa_auth_rule 
WHERE name LIKE '%commission%' 
ORDER BY pid, weigh DESC;

-- 如果菜单不存在，添加菜单
INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', 0, 'commission_log', '分佣管理', 'fa fa-money', '', '分佣日志管理系统', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 95, 'normal');

-- 获取主菜单ID
SET @commission_menu_id = (SELECT id FROM fa_auth_rule WHERE name = 'commission_log' LIMIT 1);

-- 添加子菜单
INSERT IGNORE INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', @commission_menu_id, 'commission_log/index', '分佣日志', 'fa fa-list', '', '查看分佣日志列表', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 'normal'),
('file', @commission_menu_id, 'commission_log/detail', '分佣详情', 'fa fa-eye', '', '查看分佣详情', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 99, 'normal'),
('file', @commission_menu_id, 'commission_log/statistics', '分佣统计', 'fa fa-bar-chart', '', '分佣统计分析', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 98, 'normal');

-- 为超级管理员添加权限
UPDATE `fa_auth_group` 
SET `rules` = CONCAT(IFNULL(`rules`, ''), ',', @commission_menu_id, ',', @commission_menu_id + 1, ',', @commission_menu_id + 2, ',', @commission_menu_id + 3)
WHERE `id` = 1 AND FIND_IN_SET(@commission_menu_id, `rules`) = 0;

-- 清理重复逗号
UPDATE `fa_auth_group` 
SET `rules` = TRIM(BOTH ',' FROM REPLACE(REPLACE(REPLACE(`rules`, ',,', ','), ',,', ','), ',,', ','))
WHERE `id` = 1;

SELECT '=== 修复完成 ===' as info;
SELECT '请刷新页面查看效果' as notice;
