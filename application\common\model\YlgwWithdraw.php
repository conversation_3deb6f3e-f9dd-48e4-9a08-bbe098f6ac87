<?php

namespace app\common\model;

use think\Model;
use think\Db;

/**
 * 养老顾问提现申请模型
 */
class YlgwWithdraw extends Model
{
    // 表名
    protected $name = 'ylgw_withdraw';
    
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'status_text',
        'createtime_text',
        'updatetime_text',
        'audit_time_text'
    ];
    
    /**
     * 状态列表
     */
    public function getStatusList()
    {
        return [
            '0' => '待审核',
            '1' => '已审核通过', 
            '2' => '已拒绝'
        ];
    }
    
    /**
     * 状态文本
     */
    public function getStatusTextAttr($value, $data)
    {
        $status = isset($data['status']) ? $data['status'] : '';
        $list = $this->getStatusList();
        return isset($list[$status]) ? $list[$status] : '';
    }
    
    /**
     * 创建时间文本
     */
    public function getCreatetimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['createtime']) ? $data['createtime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }
    
    /**
     * 更新时间文本
     */
    public function getUpdatetimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['updatetime']) ? $data['updatetime'] : '');
        return is_numeric($value) ? date("Y-m-d H:i:s", $value) : $value;
    }
    
    /**
     * 审核时间文本
     */
    public function getAuditTimeTextAttr($value, $data)
    {
        $value = $value ? $value : (isset($data['audit_time']) ? $data['audit_time'] : '');
        return is_numeric($value) && $value > 0 ? date("Y-m-d H:i:s", $value) : '';
    }
    
    /**
     * 关联申请用户
     */
    public function user()
    {
        return $this->belongsTo('User', 'user_id');
    }
    
    /**
     * 关联审核用户
     */
    public function parent()
    {
        return $this->belongsTo('User', 'parent_id');
    }
    
    /**
     * 申请提现
     */
    public static function applyWithdraw($user_id, $money)
    {
        // 获取用户信息
        $user = User::find($user_id);
        if (!$user) {
           return '用户不存在';
        }
        
        // 检查是否为养老顾问
        if ($user->is_ylgw != 1) {
            return '您不是养老顾问，无法申请提现';
        }
        
        // 检查是否为非平台用户（有上级且上级不是平台）
        if (!$user->parent_id || $user->parent_id == 1) {
            return '您没有上级或上级为平台，无法使用对账功能';
        }
        
        // 计算可提现金额
        $availableAmount = self::getAvailableAmount($user_id);
        if ($money > $availableAmount) {
            return '申请金额超过可提现金额';
        }
        
        // 检查是否有待审核的申请
        $pendingCount = self::where([
            'user_id' => $user_id,
            'status' => 0
        ])->count();
        
        // if ($pendingCount > 0) {
        //     return '您有待审核的提现申请，请等待审核完成后再申请';
        // }
        
        // 创建提现申请
        $data = [
            'user_id' => $user_id,
            'parent_id' => $user->parent_id,
            'money' => $money,
            'status' => 0
        ];
        $result = self::create($data);
        if (!$result) {
            return '申请提现失败';
        }else{
            $result =true; 
        }
        return $result;
    }
    
    /**
     * 获取可提现金额
     */
    public static function getAvailableAmount($user_id)
    {
        $user = User::find($user_id);
        if (!$user) {
            return 0;
        }
        
        // 总佣金
        $totalCommission = $user->ylgw_total_commission ?: 0;
        
        // 已提现金额
        $withdrawnAmount = $user->ylgw_withdraw_amount ?: 0;
        
        // 待审核金额
        $pendingAmount = self::where([
            'user_id' => $user_id,
            'status' => 0
        ])->sum('money') ?: 0;
        
        return max(0, $totalCommission - $withdrawnAmount - $pendingAmount);
    }
    
    /**
     * 审核提现申请
     */
    public function auditWithdraw($status, $transfer_amount = 0, $transfer_image = '', $remark = '', $reject_reason = '')
    {
        if ($this->status != 0) {
            return '该申请已被审核，无法重复操作';
        }
        
        Db::startTrans();
        try {
            $updateData = [
                'status' => $status,
                'audit_time' => time(),
                'remark' => $remark
            ];
            
            if ($status == 1) { // 审核通过
                if ($transfer_amount <= 0) {
                    return '转账金额必须大于0';
                }
                
                $updateData['transfer_amount'] = $transfer_amount;
                $updateData['transfer_image'] = $transfer_image;
                
                // 更新用户已提现金额
                $user = User::find($this->user_id);
                $user->ylgw_withdraw_amount = bcadd($user->ylgw_withdraw_amount, $transfer_amount, 2);
                $user->save();
                
                // 记录资金日志
                User::xianxiamoney(-$transfer_amount, $this->user_id, '养老顾问线下提现', 0, $this->id, 'ylgw_withdraw');
                
            } elseif ($status == 2) { // 拒绝
                $updateData['reject_reason'] = $reject_reason;
            }
            
            $this->save($updateData);
            
            Db::commit();
            return true;
            
        } catch (\Exception $e) {
            Db::rollback();
            throw $e;
        }
    }
}
