<?php
/**
 * 测试分佣修复效果
 */

// 模拟不同购买方式的订单数据
$orders = [
    // 传统Pay控制器购买
    [
        'id' => 660,
        'user_id' => 2074,
        'course_id' => 16,
        'order_trade_no' => 'CO202506250858585105',
        'platform' => 'wxmin',
        'package_type' => 0,
        'pay_price' => 365.00,
        'description' => '传统Pay控制器购买'
    ],
    
    // Course16控制器购买
    [
        'id' => 654,
        'user_id' => 2000,
        'course_id' => 16,
        'order_trade_no' => 'C16202506242001102162',
        'platform' => 'wxmin',
        'package_type' => 1,
        'pay_price' => 1000.00,
        'description' => 'Course16控制器购买'
    ],
    
    // Package控制器购买
    [
        'id' => 655,
        'user_id' => 2002,
        'course_id' => 16,
        'order_trade_no' => 'QO202506242006385624',
        'platform' => 'quota',
        'package_type' => 0,
        'pay_price' => 0.00,
        'description' => 'Package控制器购买（额度开通）'
    ]
];

echo "=== 课程16分佣修复测试 ===\n\n";

foreach ($orders as $order) {
    echo "订单ID: {$order['id']}\n";
    echo "描述: {$order['description']}\n";
    echo "订单号: {$order['order_trade_no']}\n";
    echo "平台: {$order['platform']}\n";
    echo "套餐类型: {$order['package_type']}\n";
    echo "支付金额: {$order['pay_price']}\n";
    
    // 模拟修复后的判断逻辑
    $order_trade_no = $order['order_trade_no'] ?: '';
    $platform = $order['platform'] ?: '';
    
    $is_new_purchase = (
        strpos($order_trade_no, 'C16') === 0 ||  // Course16控制器
        strpos($order_trade_no, 'QO') === 0 ||   // Package控制器
        ($platform === 'quota' && strpos($order_trade_no, 'Q') === 0) // YlgwQuota控制器
    );
    
    echo "判断结果: " . ($is_new_purchase ? '新购买方式' : '旧购买方式') . "\n";
    
    if (!$is_new_purchase) {
        echo "处理方式: 在钩子中执行分佣逻辑\n";
        echo "分佣状态: 应该会收到分佣\n";
    } else {
        echo "处理方式: 在对应控制器中已处理分佣\n";
        echo "分佣状态: 分佣已在控制器中完成\n";
    }
    
    echo str_repeat("-", 50) . "\n\n";
}

echo "=== 修复前后对比 ===\n\n";

echo "修复前问题:\n";
echo "- 所有订单的 package_type 字段都存在\n";
echo "- isset(\$courseOrder->package_type) 总是返回 true\n";
echo "- 导致传统购买方式被误判为新购买方式\n";
echo "- 传统购买方式不执行分佣，用户收不到佣金\n\n";

echo "修复后改进:\n";
echo "- 根据订单号前缀和平台字段准确判断购买方式\n";
echo "- CO开头的订单号 = 传统Pay控制器购买\n";
echo "- C16开头的订单号 = Course16控制器购买\n";
echo "- QO开头且platform=quota = Package控制器购买\n";
echo "- 传统购买方式正确执行分佣逻辑\n\n";

echo "=== 预期效果 ===\n\n";
echo "1. 通过 /api/xiluedu.pay/pay 购买的用户现在应该能收到分佣\n";
echo "2. 通过 /api/xiluedu/course16/pay 购买的用户分佣不受影响\n";
echo "3. 通过 /api/xiluedu/package/payWithBalance 购买的用户分佣不受影响\n";
echo "4. 所有购买方式都有详细的日志记录\n\n";

echo "=== 验证方法 ===\n\n";
echo "1. 查看日志文件:\n";
echo "   tail -f runtime/log/202506/25.log | grep '课程16钩子处理'\n\n";
echo "2. 查看分佣记录:\n";
echo "   SELECT * FROM fa_user_money_log WHERE memo LIKE '%分成%' ORDER BY createtime DESC;\n\n";
echo "3. 测试传统购买接口:\n";
echo "   POST /api/xiluedu.pay/pay\n";
echo "   参数: order_no=订单号&pay_type=2&type=course\n\n";

?>
