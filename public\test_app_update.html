<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>服务者APP更新接口测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            margin-top: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 30px;
            padding: 20px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>服务者APP更新接口测试</h1>
        
        <form id="testForm">
            <div class="form-group">
                <label for="platform">平台类型:</label>
                <select id="platform" name="platform">
                    <option value="android">Android</option>
                    <option value="ios">iOS</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="version">当前版本号:</label>
                <input type="text" id="version" name="version" value="1.0.0" placeholder="例如: 1.0.0">
            </div>
            
            <button type="submit">检查更新</button>
        </form>
        
        <div id="result" class="result" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const platform = document.getElementById('platform').value;
            const version = document.getElementById('version').value;
            const resultDiv = document.getElementById('result');
            
            // 显示加载状态
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在检查更新...';
            
            // 构建请求URL
            const url = `/api/service/index/appUpdate?platform=${platform}&version=${version}`;
            
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    resultDiv.className = data.code === 1 ? 'result success' : 'result error';
                    resultDiv.textContent = JSON.stringify(data, null, 2);
                })
                .catch(error => {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '请求失败: ' + error.message;
                });
        });
    </script>
</body>
</html>
