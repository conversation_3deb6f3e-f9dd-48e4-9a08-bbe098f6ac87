<?php
/**
 * 安装服务者APP更新配置脚本
 * 执行此脚本将在fa_config表中添加APP更新相关配置项
 */

// 引入ThinkPHP框架
define('APP_PATH', __DIR__ . '/application/');
require_once __DIR__ . '/thinkphp/start.php';

try {
    echo "开始安装服务者APP更新配置...\n";

    // 检查配置项是否已存在
    $existingConfigs = \think\Db::name('config')
        ->where('name', 'like', 'service_app_%')
        ->column('name');
    
    if (!empty($existingConfigs)) {
        echo "检测到已存在的配置项: " . implode(', ', $existingConfigs) . "\n";
        echo "是否继续安装？这将覆盖现有配置。(y/n): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim($line) !== 'y' && trim($line) !== 'Y') {
            echo "安装已取消。\n";
            exit;
        }
    }
    
    // 配置项数据
    $configs = [
        [
            'name' => 'service_app_update_check',
            'group' => 'service',
            'title' => '启用APP更新检查',
            'tip' => '是否启用APP版本更新检查功能',
            'type' => 'radio',
            'value' => '1',
            'content' => '["1","0"]',
            'rule' => '',
            'extend' => ''
        ],
        [
            'name' => 'service_app_update_log',
            'group' => 'service',
            'title' => '记录APP更新日志',
            'tip' => '是否记录APP更新检查操作日志',
            'type' => 'radio',
            'value' => '1',
            'content' => '["1","0"]',
            'rule' => '',
            'extend' => ''
        ],
        [
            'name' => 'service_app_update_content',
            'group' => 'service',
            'title' => 'APP更新内容',
            'tip' => '填写APP更新的详细内容，支持富文本格式',
            'type' => 'text',
            'value' => "1. 优化用户体验\n2. 修复已知问题\n3. 提升系统稳定性\n4. 新增功能特性",
            'content' => '',
            'rule' => '',
            'extend' => ''
        ],
        [
            'name' => 'service_android_app_version',
            'group' => 'service',
            'title' => 'Android APP版本号',
            'tip' => '当前Android APP的版本号',
            'type' => 'string',
            'value' => '1.0.0',
            'content' => '',
            'rule' => 'require',
            'extend' => ''
        ],
        [
            'name' => 'service_android_app_download',
            'group' => 'service',
            'title' => 'Android APP下载地址',
            'tip' => 'Android APP安装包下载地址',
            'type' => 'string',
            'value' => '',
            'content' => '',
            'rule' => '',
            'extend' => ''
        ],
        [
            'name' => 'service_ios_app_version',
            'group' => 'service',
            'title' => 'iOS APP版本号',
            'tip' => '当前iOS APP的版本号',
            'type' => 'string',
            'value' => '1.0.0',
            'content' => '',
            'rule' => 'require',
            'extend' => ''
        ],
        [
            'name' => 'service_ios_app_download',
            'group' => 'service',
            'title' => 'iOS APP下载地址',
            'tip' => 'iOS APP安装包下载地址',
            'type' => 'string',
            'value' => '',
            'content' => '',
            'rule' => '',
            'extend' => ''
        ],
        [
            'name' => 'service_app_force_update',
            'group' => 'service',
            'title' => '强制更新',
            'tip' => '是否强制用户更新APP',
            'type' => 'radio',
            'value' => '0',
            'content' => '["1","0"]',
            'rule' => '',
            'extend' => ''
        ]
    ];
    
    // 开始事务
    \think\Db::startTrans();

    $insertCount = 0;
    $updateCount = 0;

    foreach ($configs as $config) {
        $existing = \think\Db::name('config')->where('name', $config['name'])->find();

        if ($existing) {
            // 更新现有配置
            \think\Db::name('config')->where('name', $config['name'])->update($config);
            $updateCount++;
            echo "更新配置项: {$config['name']}\n";
        } else {
            // 插入新配置
            \think\Db::name('config')->insert($config);
            $insertCount++;
            echo "添加配置项: {$config['name']}\n";
        }
    }
    
    // 创建日志表（如果不存在）
    $sql = "CREATE TABLE IF NOT EXISTS `fa_service_app_update_log` (
        `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'ID',
        `user_id` int(11) NOT NULL DEFAULT 0 COMMENT '用户ID',
        `platform` varchar(20) NOT NULL DEFAULT '' COMMENT '平台类型(android/ios)',
        `old_version` varchar(20) NOT NULL DEFAULT '' COMMENT '旧版本号',
        `new_version` varchar(20) NOT NULL DEFAULT '' COMMENT '新版本号',
        `update_type` enum('check','download','install') NOT NULL DEFAULT 'check' COMMENT '更新类型',
        `ip` varchar(50) NOT NULL DEFAULT '' COMMENT 'IP地址',
        `user_agent` varchar(500) NOT NULL DEFAULT '' COMMENT '用户代理',
        `createtime` int(10) NOT NULL DEFAULT 0 COMMENT '创建时间',
        PRIMARY KEY (`id`),
        KEY `user_id` (`user_id`),
        KEY `platform` (`platform`),
        KEY `createtime` (`createtime`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务者APP更新日志表'";
    
    \think\Db::execute($sql);
    echo "创建日志表: fa_service_app_update_log\n";

    // 提交事务
    \think\Db::commit();
    
    echo "\n安装完成！\n";
    echo "新增配置项: {$insertCount} 个\n";
    echo "更新配置项: {$updateCount} 个\n";
    echo "\n请在后台系统配置中设置相应的值。\n";
    echo "测试页面: /test_app_update.html\n";
    
} catch (\Exception $e) {
    // 回滚事务
    \think\Db::rollback();
    echo "安装失败: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . "\n";
    echo "行号: " . $e->getLine() . "\n";
}
