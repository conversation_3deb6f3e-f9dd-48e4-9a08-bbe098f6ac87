<?php

namespace app\admin\controller\ylgw;

use app\common\controller\Backend;
use app\common\model\YlgwWithdraw;
use think\Db;
use think\exception\PDOException;
use think\exception\ValidateException;

/**
 * 养老顾问提现管理
 */
class Withdraw extends Backend
{
    /**
     * YlgwWithdraw模型对象
     * @var \app\common\model\YlgwWithdraw
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new YlgwWithdraw;
        $this->view->assign("statusList", $this->model->getStatusList());
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自定义
     * 如果需要自定义必须将application/admin/library/traits/Backend.php中对应的方法复制到当前控制器,然后进行修改
     */

    /**
     * 查看
     */
    public function index()
    {
        //当前是否为关联查询
        $this->relationSearch = true;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $total = $this->model
                ->with(['user', 'parent'])
                ->where($where)
                ->order($sort, $order)
                ->count();

            $list = $this->model
                ->with(['user', 'parent'])
                ->where($where)
                ->order($sort, $order)
                ->limit($offset, $limit)
                ->select();

            foreach ($list as $row) {
                $row->getRelation('user')->visible(['id', 'username', 'nickname', 'mobile']);
                $row->getRelation('parent')->visible(['id', 'username', 'nickname']);
            }
            $result = array("total" => $total, "rows" => $list);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->with(['user', 'parent'])->find($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        $row->getRelation('user')->visible(['id', 'username', 'nickname', 'mobile', 'avatar']);
        $row->getRelation('parent')->visible(['id', 'username', 'nickname', 'mobile']);
        
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 审核
     */
    public function audit($ids = null)
    {
        $row = $this->model->with(['user', 'parent'])->find($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        if ($row->status != 0) {
            $this->error('该申请已被审核，无法重复操作');
        }
        
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $status = $params['status'];
                $transfer_amount = isset($params['transfer_amount']) ? $params['transfer_amount'] : 0;
                $transfer_image = isset($params['transfer_image']) ? $params['transfer_image'] : '';
                $remark = isset($params['remark']) ? $params['remark'] : '';
                $reject_reason = isset($params['reject_reason']) ? $params['reject_reason'] : '';
                
                try {
                    $row->auditWithdraw($status, $transfer_amount, $transfer_image, $remark, $reject_reason);
                    $this->success();
                } catch (\Exception $e) {
                    $this->error($e->getMessage());
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }
        
        $row->getRelation('user')->visible(['id', 'username', 'nickname', 'mobile', 'avatar']);
        $row->getRelation('parent')->visible(['id', 'username', 'nickname', 'mobile']);
        
        $this->view->assign("row", $row);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        if ($ids) {
            $pk = $this->model->getPk();
            $adminIds = $this->getDataLimitAdminIds();
            if (is_array($adminIds)) {
                $this->model->where($this->dataLimitField, 'in', $adminIds);
            }
            $list = $this->model->where($pk, 'in', $ids)->select();

            $count = 0;
            Db::startTrans();
            try {
                foreach ($list as $k => $v) {
                    // 只能删除待审核状态的申请
                    if ($v->status == 0) {
                        $count += $v->delete();
                    }
                }
                Db::commit();
            } catch (PDOException $e) {
                Db::rollback();
                $this->error($e->getMessage());
            } catch (\Exception $e) {
                Db::rollback();
                $this->error($e->getMessage());
            }
            if ($count) {
                $this->success();
            } else {
                $this->error(__('No rows were deleted'));
            }
        }
        $this->error(__('Parameter %s can not be empty', 'ids'));
    }
}
