-- 快速添加分佣日志管理菜单
-- 直接执行即可

-- 添加主菜单：分佣管理
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', 0, 'commissionlog', '分佣管理', 'fa fa-money', '', '分佣日志管理系统', 1, 1703123456, 1703123456, 95, 'normal');

-- 获取主菜单ID
SET @main_id = LAST_INSERT_ID();

-- 添加子菜单
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', @main_id, 'commissionlog/index', '分佣日志', 'fa fa-list', '', '查看分佣日志列表', 1, 1703123456, 1703123456, 100, 'normal'),
('file', @main_id, 'commissionlog/detail', '分佣详情', 'fa fa-eye', '', '查看分佣详情', 0, 1703123456, 1703123456, 99, 'normal'),
('file', @main_id, 'commissionlog/statistics', '分佣统计', 'fa fa-bar-chart', '', '分佣统计分析', 1, 1703123456, 1703123456, 98, 'normal'),
('file', @main_id, 'commissionlog/edit', '编辑分佣', 'fa fa-edit', '', '编辑分佣记录', 0, 1703123456, 1703123456, 97, 'normal'),
('file', @main_id, 'commissionlog/del', '删除分佣', 'fa fa-trash', '', '删除分佣记录', 0, 1703123456, 1703123456, 96, 'normal'),
('file', @main_id, 'commissionlog/multi', '批量操作', 'fa fa-cogs', '', '批量操作分佣记录', 0, 1703123456, 1703123456, 95, 'normal');

-- 为超级管理员组添加权限
UPDATE `fa_auth_group` 
SET `rules` = CONCAT(IFNULL(`rules`, ''), ',', @main_id, ',', @main_id+1, ',', @main_id+2, ',', @main_id+3, ',', @main_id+4, ',', @main_id+5, ',', @main_id+6)
WHERE `id` = 1;

-- 清理重复逗号
UPDATE `fa_auth_group` 
SET `rules` = TRIM(BOTH ',' FROM REPLACE(REPLACE(`rules`, ',,', ','), ',,', ','))
WHERE `id` = 1;
