<!DOCTYPE html>
<html>
<head>
    <title>分佣统计测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .btn { padding: 10px 15px; margin: 5px; border: none; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-default { background: #f8f9fa; color: #333; border: 1px solid #ddd; }
        .table { width: 100%; border-collapse: collapse; margin-top: 20px; }
        .table th, .table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .table th { background-color: #f2f2f2; }
        .alert { padding: 15px; margin: 20px 0; border: 1px solid transparent; border-radius: 4px; }
        .alert-info { color: #31708f; background-color: #d9edf7; border-color: #bce8f1; }
        .alert-warning { color: #8a6d3b; background-color: #fcf8e3; border-color: #faebcc; }
        .alert-danger { color: #a94442; background-color: #f2dede; border-color: #ebccd1; }
    </style>
</head>
<body>
    <h1>分佣统计测试页面</h1>
    
    <div>
        <button class="btn btn-primary active" data-type="role">按角色统计</button>
        <button class="btn btn-default" data-type="order_type">按订单类型统计</button>
        <button class="btn btn-default" data-type="daily">按日期统计</button>
    </div>
    
    <div id="statistics-content">
        <div class="alert alert-info">点击上方按钮加载统计数据</div>
    </div>
    
    <script>
    $(function() {
        // 统计类型切换
        $('.btn').click(function() {
            $('.btn').removeClass('btn-primary').addClass('btn-default');
            $(this).removeClass('btn-default').addClass('btn-primary');
            
            var type = $(this).data('type');
            loadStatistics(type);
        });
        
        // 默认加载角色统计
        loadStatistics('role');
        
        function loadStatistics(type) {
            $('#statistics-content').html('<div class="alert alert-info">正在加载统计数据...</div>');
            
            // 这里需要替换为实际的后台URL
            var baseUrl = 'http://service.jiaqingfu.com.cn/admin/commission_log/statistics';
            
            $.ajax({
                url: baseUrl,
                type: 'POST',
                data: {type: type},
                dataType: 'json',
                success: function(res) {
                    console.log('统计数据响应:', res);
                    if (res.code === 1) {
                        renderStatistics(type, res.data);
                    } else {
                        $('#statistics-content').html('<div class="alert alert-warning">加载统计数据失败：' + (res.msg || '未知错误') + '</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX请求失败:', xhr, status, error);
                    $('#statistics-content').html('<div class="alert alert-danger">请求失败：' + error + '<br>状态码：' + xhr.status + '</div>');
                }
            });
        }
        
        function renderStatistics(type, data) {
            var html = '';
            
            if (!data || data.length === 0) {
                html = '<div class="alert alert-info">暂无统计数据</div>';
            } else {
                if (type === 'role') {
                    html = renderRoleStatistics(data);
                } else if (type === 'order_type') {
                    html = renderOrderTypeStatistics(data);
                } else if (type === 'daily') {
                    html = renderDailyStatistics(data);
                }
            }
            
            $('#statistics-content').html(html);
        }
        
        function renderRoleStatistics(data) {
            var html = '<table class="table">';
            html += '<thead><tr>';
            html += '<th>角色</th>';
            html += '<th>分佣次数</th>';
            html += '<th>总分佣金额</th>';
            html += '<th>已发放金额</th>';
            html += '<th>待发放金额</th>';
            html += '<th>发放率</th>';
            html += '</tr></thead>';
            html += '<tbody>';
            
            var totalAmount = 0;
            var totalDistributed = 0;
            
            $.each(data, function(index, item) {
                var pendingAmount = parseFloat(item.total_amount) - parseFloat(item.distributed_amount);
                var distributionRate = item.total_amount > 0 ? ((item.distributed_amount / item.total_amount) * 100).toFixed(2) : 0;
                
                totalAmount += parseFloat(item.total_amount);
                totalDistributed += parseFloat(item.distributed_amount);
                
                html += '<tr>';
                html += '<td>' + (item.commission_user_role_text || item.commission_user_role) + '</td>';
                html += '<td>' + item.count + '</td>';
                html += '<td>¥' + parseFloat(item.total_amount).toFixed(2) + '</td>';
                html += '<td>¥' + parseFloat(item.distributed_amount).toFixed(2) + '</td>';
                html += '<td>¥' + pendingAmount.toFixed(2) + '</td>';
                html += '<td>' + distributionRate + '%</td>';
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            
            return html;
        }
        
        function renderOrderTypeStatistics(data) {
            var html = '<table class="table">';
            html += '<thead><tr>';
            html += '<th>订单类型</th>';
            html += '<th>分佣次数</th>';
            html += '<th>总分佣金额</th>';
            html += '<th>已发放金额</th>';
            html += '<th>待发放金额</th>';
            html += '</tr></thead>';
            html += '<tbody>';
            
            $.each(data, function(index, item) {
                var pendingAmount = parseFloat(item.total_amount) - parseFloat(item.distributed_amount);
                var orderTypeName = getOrderTypeName(item.order_type);
                
                html += '<tr>';
                html += '<td>' + orderTypeName + '</td>';
                html += '<td>' + item.count + '</td>';
                html += '<td>¥' + parseFloat(item.total_amount).toFixed(2) + '</td>';
                html += '<td>¥' + parseFloat(item.distributed_amount).toFixed(2) + '</td>';
                html += '<td>¥' + pendingAmount.toFixed(2) + '</td>';
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            
            return html;
        }
        
        function renderDailyStatistics(data) {
            var html = '<table class="table">';
            html += '<thead><tr>';
            html += '<th>日期</th>';
            html += '<th>分佣次数</th>';
            html += '<th>总分佣金额</th>';
            html += '<th>已发放金额</th>';
            html += '<th>待发放金额</th>';
            html += '</tr></thead>';
            html += '<tbody>';
            
            $.each(data, function(index, item) {
                var pendingAmount = parseFloat(item.total_amount) - parseFloat(item.distributed_amount);
                
                html += '<tr>';
                html += '<td>' + item.date + '</td>';
                html += '<td>' + item.count + '</td>';
                html += '<td>¥' + parseFloat(item.total_amount).toFixed(2) + '</td>';
                html += '<td>¥' + parseFloat(item.distributed_amount).toFixed(2) + '</td>';
                html += '<td>¥' + pendingAmount.toFixed(2) + '</td>';
                html += '</tr>';
            });
            
            html += '</tbody></table>';
            
            return html;
        }
        
        function getOrderTypeName(orderType) {
            var typeMap = {
                'course': '线上课程',
                'offline_course': '线下课程',
                'course_package': '课程套餐',
                'wanlshop': '商城商品',
                'service': '服务订单'
            };
            
            return typeMap[orderType] || orderType;
        }
    });
    </script>
</body>
</html>
