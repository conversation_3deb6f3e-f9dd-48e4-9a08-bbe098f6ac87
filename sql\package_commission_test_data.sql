-- 套餐分佣测试数据
-- 创建测试用户、套餐配置和模拟订单数据

-- 1. 创建测试用户
INSERT IGNORE INTO `fa_user` (`id`, `nickname`, `mobile`, `is_qydl`, `is_sqdl`, `is_ylgw`, `parent_id`, `money`, `createtime`) VALUES
(200, '测试城市负责人1', '13800000200', 1, 0, 0, 0, 10000.00, UNIX_TIMESTAMP()),
(201, '测试城市负责人2', '13800000201', 1, 0, 0, 0, 10000.00, UNIX_TIMESTAMP()),
(300, '测试养老院长1', '13800000300', 0, 1, 0, 200, 5000.00, UNIX_TIMESTAMP()), -- 上级是城市负责人200
(301, '测试养老院长2', '13800000301', 0, 1, 0, 0, 5000.00, UNIX_TIMESTAMP()),   -- 无上级
(302, '测试养老院长3', '13800000302', 0, 1, 0, 201, 5000.00, UNIX_TIMESTAMP()), -- 上级是城市负责人201
(400, '测试养老顾问1', '13800000400', 0, 0, 1, 300, 1000.00, UNIX_TIMESTAMP()),
(500, '测试普通用户1', '13800000500', 0, 0, 0, 0, 1000.00, UNIX_TIMESTAMP());

-- 2. 创建测试套餐配置
INSERT IGNORE INTO `fa_xiluedu_course_package` (`id`, `course_id`, `name`, `description`, `quantity`, `original_price`, `qydl_price`, `sqdl_price`, `ylgw_price`, `user_types`, `sort`, `status`, `createtime`) VALUES
(1, 16, '课程16标准套餐', '10个养老顾问名额', 10, 3650.00, 1000.00, 1300.00, 3650.00, 'qydl,sqdl,ylgw,user', 1, 1, UNIX_TIMESTAMP()),
(2, 16, '课程16高级套餐', '10个养老顾问名额（高级版）', 10, 3650.00, 1500.00, 1300.00, 3650.00, 'qydl,sqdl,ylgw,user', 2, 1, UNIX_TIMESTAMP()),
(3, 16, '课程16豪华套餐', '50个养老顾问名额', 50, 18250.00, 5000.00, 6500.00, 18250.00, 'qydl,sqdl', 3, 1, UNIX_TIMESTAMP());

-- 3. 创建测试订单数据
INSERT IGNORE INTO `fa_xiluedu_course_order` (`id`, `platform`, `user_id`, `order_no`, `course_id`, `package_id`, `package_type`, `package_quantity`, `total_price`, `pay_price`, `favourable_price`, `pay_status`, `pay_type`, `paytime`, `order_trade_no`, `ip`, `is_service`, `createtime`) VALUES
-- 场景1：养老院长购买套餐（有上级城市负责人，差价为负）
(1001, 'wxmin', 300, 'P202412240001', 16, 1, 1, 10, 1300.00, 1300.00, 0.00, 2, 2, UNIX_TIMESTAMP(), 'T' . UNIX_TIMESTAMP(), '127.0.0.1', 0, UNIX_TIMESTAMP()),

-- 场景2：养老院长购买套餐（无上级）
(1002, 'wxmin', 301, 'P202412240002', 16, 1, 1, 10, 1300.00, 1300.00, 0.00, 2, 1, UNIX_TIMESTAMP(), 'T' . UNIX_TIMESTAMP(), '127.0.0.1', 0, UNIX_TIMESTAMP()),

-- 场景3：城市负责人购买套餐
(1003, 'wxmin', 200, 'P202412240003', 16, 1, 1, 10, 1000.00, 1000.00, 0.00, 2, 1, UNIX_TIMESTAMP(), 'T' . UNIX_TIMESTAMP(), '127.0.0.1', 0, UNIX_TIMESTAMP()),

-- 场景4：养老院长购买套餐（有上级城市负责人，差价为正）
(1004, 'wxmin', 302, 'P202412240004', 16, 2, 1, 10, 1300.00, 1300.00, 0.00, 2, 2, UNIX_TIMESTAMP(), 'T' . UNIX_TIMESTAMP(), '127.0.0.1', 0, UNIX_TIMESTAMP()),

-- 场景5：普通用户购买套餐
(1005, 'wxmin', 500, 'P202412240005', 16, 1, 1, 10, 3650.00, 3650.00, 0.00, 2, 1, UNIX_TIMESTAMP(), 'T' . UNIX_TIMESTAMP(), '127.0.0.1', 0, UNIX_TIMESTAMP());

-- 4. 创建对应的用户课程关联
INSERT IGNORE INTO `fa_xiluedu_user_course` (`user_id`, `course_id`, `from_type`, `createtime`) VALUES
(300, 16, 1, UNIX_TIMESTAMP()),
(301, 16, 1, UNIX_TIMESTAMP()),
(200, 16, 1, UNIX_TIMESTAMP()),
(302, 16, 1, UNIX_TIMESTAMP()),
(500, 16, 1, UNIX_TIMESTAMP());

-- 5. 模拟分佣日志数据（用于验证）
INSERT IGNORE INTO `fa_commission_log` (
    `batch_no`, `step_no`, `order_type`, `order_id`, `order_no`, 
    `goods_id`, `goods_name`, `order_amount`, `payment_method`, 
    `balance_amount`, `online_payment_amount`, `base_amount`,
    `commission_user_id`, `commission_user_role`, `buyer_user_id`, `buyer_user_role`,
    `commission_type`, `commission_rate`, `commission_amount`, `platform_profit`, `total_distributed`,
    `calculation_rule`, `calculation_details`, `config_source`, `config_value`,
    `is_distributed`, `distribution_type`, `distribution_status`, `distribution_memo`,
    `special_rules`, `createtime`
) VALUES 
-- 场景1：养老院长购买套餐（差价为负，全部归平台）
(
    'course_package_1001_test', 1, 'course_package', 1001, 'P202412240001',
    16, '课程16标准套餐', 1300.00, 'balance',
    1300.00, 0.00, 1300.00,
    0, 'platform', 300, 'nursing_home_director',
    'direct', 100.00, 1300.00, 1300.00, 0.00,
    '养老院长购买套餐：差价为负，全部归平台 1300', 
    '{"price_difference": -300, "city_manager_price": 1000, "nursing_home_price": 1300}',
    'special_rule', '100%',
    1, 'online', 'success', '差价为负数，不分佣',
    '有上级但差价为负,全部归平台', UNIX_TIMESTAMP()
),

-- 场景2：养老院长购买套餐（无上级，全部归平台）
(
    'course_package_1002_test', 1, 'course_package', 1002, 'P202412240002',
    16, '课程16标准套餐', 1300.00, 'wechat',
    0.00, 1300.00, 1300.00,
    0, 'platform', 301, 'nursing_home_director',
    'direct', 100.00, 1300.00, 1300.00, 0.00,
    '养老院长购买套餐：无上级城市负责人，全部归平台 1300',
    '{"has_parent": false, "reason": "no_superior"}',
    'special_rule', '100%',
    1, 'online', 'success', '无上级城市负责人',
    '无上级城市负责人,全部归平台', UNIX_TIMESTAMP()
),

-- 场景3：城市负责人购买套餐（全部归平台）
(
    'course_package_1003_test', 1, 'course_package', 1003, 'P202412240003',
    16, '课程16标准套餐', 1000.00, 'wechat',
    0.00, 1000.00, 1000.00,
    0, 'platform', 200, 'city_manager',
    'direct', 100.00, 1000.00, 1000.00, 0.00,
    '城市负责人购买套餐：全部归平台 1000',
    '{"buyer_role": "city_manager", "rule": "all_to_platform"}',
    'special_rule', '100%',
    1, 'online', 'success', '城市负责人购买套餐',
    '城市负责人购买套餐，全部归平台', UNIX_TIMESTAMP()
),

-- 场景4：养老院长购买套餐（有正差价分佣）
(
    'course_package_1004_test', 1, 'course_package', 1004, 'P202412240004',
    16, '课程16高级套餐', 1300.00, 'balance',
    1300.00, 0.00, 1300.00,
    201, 'city_manager', 302, 'nursing_home_director',
    'direct', 0.00, 200.00, 1100.00, 200.00,
    '城市负责人套餐分佣：城市负责人价格 1500 - 养老院长价格 1300 = 200',
    '{"price_difference": 200, "city_manager_price": 1500, "nursing_home_price": 1300}',
    'special_rule', '城市负责人:1500, 养老院长:1300',
    1, 'online', 'success', '差价分佣给城市负责人',
    '养老院长购买套餐，有上级城市负责人,分佣金额 = 城市负责人套餐价格 - 养老院长套餐价格', UNIX_TIMESTAMP()
);

-- 6. 创建余额支付记录
INSERT IGNORE INTO `fa_balance_payment_log` (
    `order_type`, `order_id`, `order_no`, `user_id`,
    `balance_before`, `balance_used`, `balance_after`,
    `online_payment`, `total_amount`, `payment_method`,
    `memo`, `createtime`
) VALUES 
('course_package', 1001, 'P202412240001', 300, 6300.00, 1300.00, 5000.00, 0.00, 1300.00, 'balance', '套餐购买：余额支付', UNIX_TIMESTAMP()),
('course_package', 1004, 'P202412240004', 302, 6300.00, 1300.00, 5000.00, 0.00, 1300.00, 'balance', '套餐购买：余额支付', UNIX_TIMESTAMP());

-- 7. 查询验证SQL
-- 查看所有套餐分佣记录
SELECT 
    cl.order_id,
    cl.order_no,
    cl.goods_name,
    cl.order_amount,
    cl.commission_user_id,
    u.nickname as commission_user_name,
    CASE cl.commission_user_role
        WHEN 'platform' THEN '平台'
        WHEN 'city_manager' THEN '城市负责人'
        WHEN 'nursing_home_director' THEN '养老院长'
        ELSE cl.commission_user_role
    END as role_name,
    cl.commission_amount,
    cl.platform_profit,
    cl.calculation_rule,
    FROM_UNIXTIME(cl.createtime) as create_time
FROM fa_commission_log cl
LEFT JOIN fa_user u ON cl.commission_user_id = u.id
WHERE cl.order_type = 'course_package'
ORDER BY cl.order_id, cl.step_no;

-- 统计套餐分佣情况
SELECT 
    '总订单数' as metric,
    COUNT(*) as value
FROM fa_commission_log 
WHERE order_type = 'course_package'

UNION ALL

SELECT 
    '总订单金额' as metric,
    SUM(order_amount) as value
FROM fa_commission_log 
WHERE order_type = 'course_package'

UNION ALL

SELECT 
    '总分佣金额' as metric,
    SUM(commission_amount) as value
FROM fa_commission_log 
WHERE order_type = 'course_package'

UNION ALL

SELECT 
    '平台总利润' as metric,
    SUM(platform_profit) as value
FROM fa_commission_log 
WHERE order_type = 'course_package';

-- 查看城市负责人获得的套餐分佣
SELECT 
    cl.commission_user_id,
    u.nickname,
    COUNT(*) as package_count,
    SUM(cl.commission_amount) as total_commission,
    AVG(cl.commission_amount) as avg_commission
FROM fa_commission_log cl
LEFT JOIN fa_user u ON cl.commission_user_id = u.id
WHERE cl.order_type = 'course_package'
AND cl.commission_user_role = 'city_manager'
AND cl.commission_amount > 0
GROUP BY cl.commission_user_id;

-- 验证数据完整性
SELECT 
    order_id,
    order_amount,
    commission_amount,
    platform_profit,
    (commission_amount + platform_profit) as total_check,
    CASE 
        WHEN ABS((commission_amount + platform_profit) - order_amount) < 0.01 THEN '✅ 正确'
        ELSE '❌ 错误'
    END as validation
FROM fa_commission_log 
WHERE order_type = 'course_package'
ORDER BY order_id;
