<?php

namespace app\admin\controller;

use app\common\controller\Backend;
use app\common\model\CommissionLog as CommissionLogModel;
use think\Db;

/**
 * 分佣日志管理
 *
 * @icon fa fa-money
 */
class CommissionLog extends Backend
{
    /**
     * CommissionLog模型对象
     * @var \app\common\model\CommissionLog
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new CommissionLogModel;
    }

    /**
     * 默认生成的控制器所继承的父类中有index/add/edit/del/multi五个基础方法、destroy/restore/recyclebin三个回收站方法
     * 因此在当前控制器中可不用编写增删改查的代码,除非需要自定义
     * 如果需要自定义，请复制对应的方法至此文件中
     */

    public function index()
    {
       
        //当前是否为关联查询
        $this->relationSearch = false;
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();

            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);

            // 格式化数据
            $rows = $list->items();
            foreach ($rows as $row) {
                $row->commission_user_role_text = CommissionLogModel::getRoleNameCn($row->commission_user_role);
                $row->buyer_user_role_text = CommissionLogModel::getRoleNameCn($row->buyer_user_role);
                $row->commission_type_text = CommissionLogModel::getCommissionTypeCn($row->commission_type);
                $row->order_type_text = $this->getOrderTypeText($row->order_type);
                $row->distribution_type_text = $row->distribution_type == 'online' ? '线上' : '线下';
                $row->distribution_status_text = $this->getDistributionStatusText($row->distribution_status);
                $row->is_distributed_text = $row->is_distributed ? '已发放' : '未发放';
            }

            $result = array("total" => $list->total(), "rows" => $rows);

            return json($result);
        }
        return $this->view->fetch();
    }

    /**
     * 测试方法
     */
    public function test()
    {
        // 检查表是否存在
        try {
            $count = $this->model->count();
            $sample = $this->model->limit(3)->select();

            return json([
                'code' => 1,
                'msg' => '分佣日志控制器工作正常',
                'data' => [
                    'table_exists' => true,
                    'record_count' => $count,
                    'sample_data' => $sample
                ]
            ]);
        } catch (\Exception $e) {
            return json([
                'code' => 0,
                'msg' => '数据库错误: ' . $e->getMessage(),
                'data' => [
                    'table_exists' => false,
                    'error' => $e->getMessage()
                ]
            ]);
        }
    }

    /**
     * 查看详情
     */
    public function detail($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        // 获取同批次的所有分佣记录
        $batch_logs = $this->model
            ->where('batch_no', $row->batch_no)
            ->order('step_no ASC')
            ->select();
            
        foreach ($batch_logs as $log) {
            $log->commission_user_role_text = CommissionLogModel::getRoleNameCn($log->commission_user_role);
            $log->buyer_user_role_text = CommissionLogModel::getRoleNameCn($log->buyer_user_role);
            $log->commission_type_text = CommissionLogModel::getCommissionTypeCn($log->commission_type);
            $log->order_type_text = $this->getOrderTypeText($log->order_type);
            $log->distribution_type_text = $log->distribution_type == 'online' ? '线上' : '线下';
            $log->distribution_status_text = $this->getDistributionStatusText($log->distribution_status);
            $log->is_distributed_text = $log->is_distributed ? '已发放' : '未发放';
        }

        $this->view->assign("row", $row);
        $this->view->assign("batch_logs", $batch_logs);
        return $this->view->fetch();
    }

    /**
     * 统计分析
     */
    public function statistics()
    {
        if ($this->request->isAjax()) {
            $type = $this->request->param('type', 'role');
            
            if ($type == 'role') {
                // 按角色统计
                $stats = Db::name('commission_log')
                    ->field('commission_user_role, COUNT(*) as count, SUM(commission_amount) as total_amount, SUM(CASE WHEN is_distributed = 1 THEN commission_amount ELSE 0 END) as distributed_amount')
                    ->group('commission_user_role')
                    ->select();
                    
                foreach ($stats as &$stat) {
                    $stat['commission_user_role_text'] = CommissionLogModel::getRoleNameCn($stat['commission_user_role']);
                    $stat['pending_amount'] = $stat['total_amount'] - $stat['distributed_amount'];
                }
            } elseif ($type == 'order_type') {
                // 按订单类型统计
                $stats = Db::name('commission_log')
                    ->field('order_type, COUNT(*) as count, SUM(commission_amount) as total_amount, SUM(CASE WHEN is_distributed = 1 THEN commission_amount ELSE 0 END) as distributed_amount')
                    ->group('order_type')
                    ->select();
                    
                foreach ($stats as &$stat) {
                    $stat['order_type_text'] = $this->getOrderTypeText($stat['order_type']);
                    $stat['pending_amount'] = $stat['total_amount'] - $stat['distributed_amount'];
                }
            } elseif ($type == 'daily') {
                // 按日期统计
                $stats = Db::name('commission_log')
                    ->field('FROM_UNIXTIME(createtime, "%Y-%m-%d") as date, COUNT(*) as count, SUM(commission_amount) as total_amount, SUM(CASE WHEN is_distributed = 1 THEN commission_amount ELSE 0 END) as distributed_amount')
                    ->where('createtime', '>', strtotime('-30 days'))
                    ->group('FROM_UNIXTIME(createtime, "%Y-%m-%d")')
                    ->order('date DESC')
                    ->select();
                    
                foreach ($stats as &$stat) {
                    $stat['pending_amount'] = $stat['total_amount'] - $stat['distributed_amount'];
                }
            }
            
            return json(['code' => 1, 'data' => $stats]);
        }
        
        return $this->view->fetch();
    }

    /**
     * 获取订单类型文本
     */
    private function getOrderTypeText($type)
    {
        $typeMap = [
            'course' => '线上课程',
            'offline_course' => '线下课程',
            'course_package' => '课程套餐',
            'wanlshop' => '商城商品',
            'service' => '服务'
        ];
        
        return $typeMap[$type] ?? $type;
    }

    /**
     * 获取发放状态文本
     */
    private function getDistributionStatusText($status)
    {
        $statusMap = [
            'pending' => '待发放',
            'success' => '发放成功',
            'failed' => '发放失败'
        ];
        
        return $statusMap[$status] ?? $status;
    }
}
