-- 添加分佣日志管理菜单到后台系统
-- 执行前请先备份数据库

-- 1. 查看当前菜单结构（用于确定插入位置）
SELECT '=== 当前相关菜单结构 ===' as info;
SELECT id, pid, name, title, icon, ismenu, weigh, status 
FROM fa_auth_rule 
WHERE name LIKE '%user%' OR name LIKE '%money%' OR title LIKE '%用户%' OR title LIKE '%资金%'
ORDER BY pid, weigh DESC;

-- 2. 添加分佣管理主菜单
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
('file', 0, 'commissionlog', '分佣管理', 'fa fa-money', '', '分佣日志管理系统', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 95, 'normal');

-- 获取刚插入的主菜单ID
SET @commission_menu_id = LAST_INSERT_ID();

-- 3. 添加分佣日志子菜单
INSERT INTO `fa_auth_rule` (`type`, `pid`, `name`, `title`, `icon`, `condition`, `remark`, `ismenu`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
-- 分佣日志列表
('file', @commission_menu_id, 'commissionlog/index', '分佣日志', 'fa fa-list', '', '查看分佣日志列表', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 'normal'),
-- 分佣详情查看
('file', @commission_menu_id, 'commissionlog/detail', '分佣详情', 'fa fa-eye', '', '查看分佣详情', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 99, 'normal'),
-- 分佣统计分析
('file', @commission_menu_id, 'commissionlog/statistics', '分佣统计', 'fa fa-bar-chart', '', '分佣统计分析', 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 98, 'normal'),
-- 编辑分佣记录
('file', @commission_menu_id, 'commissionlog/edit', '编辑分佣', 'fa fa-edit', '', '编辑分佣记录', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 97, 'normal'),
-- 删除分佣记录
('file', @commission_menu_id, 'commissionlog/del', '删除分佣', 'fa fa-trash', '', '删除分佣记录', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 96, 'normal'),
-- 批量操作
('file', @commission_menu_id, 'commissionlog/multi', '批量操作', 'fa fa-cogs', '', '批量操作分佣记录', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 95, 'normal'),
-- 导出功能
('file', @commission_menu_id, 'commissionlog/export', '导出数据', 'fa fa-download', '', '导出分佣数据', 0, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 94, 'normal');

-- 4. 获取所有新添加的菜单ID
SET @menu_ids = (
    SELECT GROUP_CONCAT(id) 
    FROM fa_auth_rule 
    WHERE name LIKE 'commissionlog%' 
    ORDER BY id
);

-- 5. 为超级管理员组（ID=1）添加权限
UPDATE `fa_auth_group` 
SET `rules` = CASE 
    WHEN `rules` IS NULL OR `rules` = '' THEN @menu_ids
    ELSE CONCAT(`rules`, ',', @menu_ids)
END
WHERE `id` = 1;

-- 6. 清理可能的重复逗号
UPDATE `fa_auth_group` 
SET `rules` = TRIM(BOTH ',' FROM REPLACE(REPLACE(REPLACE(`rules`, ',,', ','), ',,', ','), ',,', ','))
WHERE `id` = 1;

-- 7. 查看添加结果
SELECT '=== 新添加的分佣管理菜单 ===' as info;
SELECT 
    r.id,
    r.pid,
    r.name,
    r.title,
    r.icon,
    r.ismenu,
    r.weigh,
    r.status,
    CASE 
        WHEN r.pid = @commission_menu_id THEN '子菜单'
        WHEN r.name = 'commissionlog' THEN '主菜单'
        ELSE '其他'
    END as menu_type
FROM `fa_auth_rule` r 
WHERE r.name LIKE 'commissionlog%' 
ORDER BY r.pid, r.weigh DESC;

-- 8. 验证超级管理员权限
SELECT '=== 超级管理员权限验证 ===' as info;
SELECT 
    g.id as group_id,
    g.name as group_name,
    CASE 
        WHEN FIND_IN_SET(@commission_menu_id, g.rules) > 0 THEN '已配置分佣管理权限'
        ELSE '未配置分佣管理权限'
    END as permission_status,
    LENGTH(g.rules) - LENGTH(REPLACE(g.rules, ',', '')) + 1 as total_permissions
FROM `fa_auth_group` g 
WHERE g.id = 1;

-- 9. 显示菜单层级结构
SELECT '=== 分佣管理菜单层级结构 ===' as info;
SELECT 
    CASE 
        WHEN r.pid = 0 THEN CONCAT('├── ', r.title, ' (', r.name, ')')
        ELSE CONCAT('│   ├── ', r.title, ' (', r.name, ')')
    END as menu_structure,
    CASE 
        WHEN r.ismenu = 1 THEN '显示在菜单'
        ELSE '不显示在菜单'
    END as menu_display
FROM `fa_auth_rule` r 
WHERE r.name LIKE 'commissionlog%' 
ORDER BY r.pid, r.weigh DESC;

-- 10. 创建测试用管理员账号的权限（可选）
-- 如果需要为其他管理员组添加权限，可以执行以下语句
-- UPDATE `fa_auth_group` 
-- SET `rules` = CONCAT(IFNULL(`rules`, ''), ',', @menu_ids)
-- WHERE `id` IN (2, 3); -- 替换为需要添加权限的管理员组ID

SELECT '=== 菜单添加完成 ===' as info;
SELECT CONCAT('主菜单ID: ', @commission_menu_id) as main_menu_info;
SELECT CONCAT('子菜单数量: ', (SELECT COUNT(*) FROM fa_auth_rule WHERE pid = @commission_menu_id)) as sub_menu_count;
SELECT '请在后台刷新页面查看新菜单' as notice;
