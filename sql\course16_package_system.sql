-- 课程16套餐功能实现SQL脚本（基于配置项方案）

-- 1. 添加课程16套餐相关配置项
INSERT INTO `fa_config` (`name`, `group`, `title`, `tip`, `type`, `value`, `content`, `rule`, `extend`) VALUES
('course16_single_price', 'site', '课程16单个价格', '设置课程16单个购买的价格，单位为元', 'number', '365', '', 'required', ''),
('course16_package_size', 'site', '课程16套餐数量', '设置课程16套餐包含的数量', 'number', '10', '', 'required', ''),
('course16_qydl_single_price', 'site', '课程16城市负责人单价', '设置课程16城市负责人的单价，单位为元', 'number', '100', '', 'required', ''),
('course16_sqdl_single_price', 'site', '课程16养老院长单价', '设置课程16养老院长的单价，单位为元', 'number', '130', '', 'required', ''),
('course16_enable_package', 'site', '课程16套餐功能开关', '是否启用课程16的套餐功能', 'radio', '1', '0:关闭\n1:开启', 'required', '');

-- 2. 为课程订单表添加套餐相关字段
ALTER TABLE `fa_xiluedu_course_order` ADD COLUMN `package_type` tinyint(1) NOT NULL DEFAULT '0' COMMENT '套餐类型:0=单个,1=套餐' AFTER `course_id`;
ALTER TABLE `fa_xiluedu_course_order` ADD COLUMN `package_quantity` int(10) NOT NULL DEFAULT '1' COMMENT '套餐数量' AFTER `package_type`;

-- 3. 为用户表添加开通额度字段
ALTER TABLE `fa_user` ADD COLUMN `ylgw_quota` int(10) NOT NULL DEFAULT '0' COMMENT '养老顾问开通额度' AFTER `ylgw_total_commission`;

-- 3. 添加新的分佣逻辑标识配置
INSERT INTO `fa_config` (`name`, `group`, `title`, `tip`, `type`, `value`, `content`, `rule`, `extend`) VALUES
('new_commission_courses', 'site', '新分佣逻辑课程', '使用新分佣逻辑的课程ID，多个用逗号分隔', 'string', '10,16', '', 'required', ''),
('course_sq_commission_rate', 'site', '课程养老院长分佣比例', '新分佣逻辑中养老院长的分佣比例', 'number', '50', '', 'required', ''),
('course_qy_commission_rate', 'site', '课程城市负责人分佣比例', '新分佣逻辑中城市负责人的分佣比例', 'number', '10', '', 'required', ''),
('course_ylgw_commission_rate', 'site', '课程养老顾问推荐分佣比例', '新分佣逻辑中养老顾问推荐的分佣比例（基于养老院长分佣）', 'number', '50', '', 'required', '');

-- 4. 查看创建结果
SELECT 'Course16 Package Config Created' as status;
SELECT name, title, value FROM fa_config WHERE name LIKE 'course16_%' OR name LIKE 'course_%commission%' OR name = 'new_commission_courses';
