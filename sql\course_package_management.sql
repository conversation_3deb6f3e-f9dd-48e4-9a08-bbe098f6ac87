-- 课程套餐管理功能数据库脚本

-- 1. 创建课程套餐表
CREATE TABLE IF NOT EXISTS `fa_xiluedu_course_package` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
  `course_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '课程ID',
  `name` varchar(100) NOT NULL DEFAULT '' COMMENT '套餐名称',
  `description` varchar(255) NOT NULL DEFAULT '' COMMENT '套餐描述',
  `quantity` int(10) NOT NULL DEFAULT '1' COMMENT '套餐数量',
  `original_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '原价',
  `qydl_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '城市负责人价格',
  `sqdl_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '养老院长价格',
  `ylgw_price` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '养老顾问价格',
  `user_types` varchar(100) NOT NULL DEFAULT '' COMMENT '适用人员类型(qydl,sqdl,ylgw,user)',
  `sort` int(10) NOT NULL DEFAULT '0' COMMENT '排序',
  `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态:0=禁用,1=启用',
  `createtime` bigint(16) DEFAULT NULL COMMENT '创建时间',
  `updatetime` bigint(16) DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `course_id` (`course_id`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='课程套餐表';

-- 2. 为课程16添加默认套餐数据
INSERT INTO `fa_xiluedu_course_package` (`course_id`, `name`, `description`, `quantity`, `original_price`, `qydl_price`, `sqdl_price`, `ylgw_price`, `user_types`, `sort`, `status`, `createtime`, `updatetime`) VALUES 
(16, '单个购买', '课程16单个购买', 1, 365.00, 365.00, 365.00, 365.00, 'qydl,sqdl,ylgw,user', 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP()),
(16, '套餐购买(10个)', '课程16套餐购买，10个为一套', 10, 3650.00, 1000.00, 1300.00, 3650.00, 'qydl,sqdl', 2, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP());

-- 3. 查看创建结果
SELECT 'Course Package Table Created' as status;
SELECT * FROM fa_xiluedu_course_package WHERE course_id = 16;
