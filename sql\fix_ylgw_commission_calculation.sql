-- 修复养老顾问分佣计算问题
-- 确保gjia_bili配置为50%，并添加课程10的特殊分佣逻辑说明

-- 1. 更新gjia_bili配置为50%
UPDATE `fa_config` SET 
    `value` = '50',
    `title` = '养老顾问分成比例',
    `tip` = '设置养老顾问基于养老院长分佣金额的分成比例，单位为百分比。对于课程10，养老院长分佣为课程价格的50%，养老顾问分佣为养老院长分佣的50%'
WHERE `name` = 'gjia_bili';

-- 2. 如果gjia_bili配置不存在，则添加
INSERT IGNORE INTO `fa_config` (`name`, `group`, `title`, `tip`, `type`, `value`, `content`, `rule`, `extend`) VALUES 
('gjia_bili', 'site', '养老顾问分成比例', '设置养老顾问基于养老院长分佣金额的分成比例，单位为百分比。对于课程10，养老院长分佣为课程价格的50%，养老顾问分佣为养老院长分佣的50%', 'number', '50', '', 'required', '');

-- 3. 查看当前配置
SELECT name, title, value, tip FROM fa_config WHERE name = 'gjia_bili';

-- 4. 查看课程10的配置信息
SELECT id, name, salesprice, service_bili, shequ_bili, quyu_bili 
FROM fa_xiluedu_offline_course 
WHERE id = 10;

-- 5. 计算验证（假设课程10价格为6800元）
-- 期望结果：
-- 养老院长分佣：6800 * 50% = 3400元
-- 养老顾问分佣：3400 * 50% = 1700元
SELECT 
    '课程10分佣计算验证' as description,
    6800 as course_price,
    6800 * 0.5 as expected_sq_money,
    6800 * 0.5 * 0.5 as expected_ylgw_money;
