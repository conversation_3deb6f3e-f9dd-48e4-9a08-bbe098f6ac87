<?php
/**
 * 创建证书模板图片的示例脚本
 * 运行此脚本可以生成三个示例证书模板
 */

// 检查GD扩展
if (!extension_loaded('gd')) {
    die('GD扩展未安装');
}

// 模板配置
$templates = [
    'ylgw_template.jpg' => [
        'title' => '养老院长资格证书',
        'subtitle' => 'Certificate of Nursing Home Director',
        'bg_color' => [240, 248, 255], // 淡蓝色背景
        'border_color' => [70, 130, 180] // 钢蓝色边框
    ],
    'sqdl_template.jpg' => [
        'title' => '社区代理资格证书', 
        'subtitle' => 'Certificate of Community Agent',
        'bg_color' => [240, 255, 240], // 淡绿色背景
        'border_color' => [34, 139, 34] // 森林绿边框
    ],
    'qydl_template.jpg' => [
        'title' => '区域代理资格证书',
        'subtitle' => 'Certificate of Regional Agent', 
        'bg_color' => [255, 248, 220], // 淡黄色背景
        'border_color' => [218, 165, 32] // 金黄色边框
    ]
];

foreach ($templates as $filename => $config) {
    // 创建画布
    $width = 800;
    $height = 600;
    $image = imagecreatetruecolor($width, $height);
    
    // 设置颜色
    $bg_color = imagecolorallocate($image, $config['bg_color'][0], $config['bg_color'][1], $config['bg_color'][2]);
    $border_color = imagecolorallocate($image, $config['border_color'][0], $config['border_color'][1], $config['border_color'][2]);
    $text_color = imagecolorallocate($image, 0, 0, 0); // 黑色文字
    $white = imagecolorallocate($image, 255, 255, 255);
    
    // 填充背景
    imagefill($image, 0, 0, $bg_color);
    
    // 绘制边框
    $border_width = 10;
    for ($i = 0; $i < $border_width; $i++) {
        imagerectangle($image, $i, $i, $width - 1 - $i, $height - 1 - $i, $border_color);
    }
    
    // 绘制装饰性边框
    $inner_border = 30;
    for ($i = 0; $i < 3; $i++) {
        imagerectangle($image, $inner_border + $i, $inner_border + $i, 
                      $width - $inner_border - 1 - $i, $height - $inner_border - 1 - $i, $border_color);
    }
    
    // 添加标题
    $title_size = 5;
    $title_x = ($width - strlen($config['title']) * imagefontwidth($title_size)) / 2;
    $title_y = 80;
    imagestring($image, $title_size, $title_x, $title_y, $config['title'], $text_color);
    
    // 添加英文副标题
    $subtitle_size = 3;
    $subtitle_x = ($width - strlen($config['subtitle']) * imagefontwidth($subtitle_size)) / 2;
    $subtitle_y = 120;
    imagestring($image, $subtitle_size, $subtitle_x, $subtitle_y, $config['subtitle'], $text_color);
    
    // 添加姓名标签
    $name_label = "姓名 Name:";
    $name_label_x = 150;
    $name_label_y = 200;
    imagestring($image, 4, $name_label_x, $name_label_y, $name_label, $text_color);
    
    // 添加身份证号标签
    $idcard_label = "身份证号 ID Number:";
    $idcard_label_x = 150;
    $idcard_label_y = 250;
    imagestring($image, 4, $idcard_label_x, $idcard_label_y, $idcard_label, $text_color);
    
    // 添加签发信息
    $issue_text = "签发单位：家庆福养老服务平台";
    $issue_x = 150;
    $issue_y = 400;
    imagestring($image, 3, $issue_x, $issue_y, $issue_text, $text_color);
    
    $date_text = "签发日期：" . date('Y年m月d日');
    $date_x = 150;
    $date_y = 430;
    imagestring($image, 3, $date_x, $date_y, $date_text, $text_color);
    
    // 添加印章区域（圆形）
    $seal_x = 600;
    $seal_y = 450;
    $seal_radius = 40;
    imageellipse($image, $seal_x, $seal_y, $seal_radius * 2, $seal_radius * 2, $border_color);
    imageellipse($image, $seal_x, $seal_y, ($seal_radius - 2) * 2, ($seal_radius - 2) * 2, $border_color);
    
    $seal_text = "官方印章";
    $seal_text_x = $seal_x - (strlen($seal_text) * imagefontwidth(2)) / 2;
    $seal_text_y = $seal_y - imagefontheight(2) / 2;
    imagestring($image, 2, $seal_text_x, $seal_text_y, $seal_text, $border_color);
    
    // 保存图片
    $filepath = __DIR__ . '/' . $filename;
    if (imagejpeg($image, $filepath, 90)) {
        echo "成功创建模板: {$filename}\n";
    } else {
        echo "创建模板失败: {$filename}\n";
    }
    
    // 释放内存
    imagedestroy($image);
}

echo "\n所有证书模板创建完成！\n";
echo "请根据实际需要调整文字位置坐标。\n";
?>
