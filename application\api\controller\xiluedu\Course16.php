<?php

namespace app\api\controller\xiluedu;

use app\common\controller\Api;
use app\common\model\User;
use app\common\model\xiluedu\CourseOrder;
use app\common\model\xiluedu\CoursePackage;
use app\common\model\xiluedu\UserCourse;
use think\Db;
use think\Exception;

/**
 * 课程16套餐功能控制器
 */
class Course16 extends Api
{
    protected $noNeedLogin = ['config'];
    protected $noNeedRight = ['*'];

    /**
     * 获取课程16套餐配置
     */
    public function config()
    {
        // 从数据库获取课程16的套餐配置
        $packages = CoursePackage::getCoursePackages(16);

        if ($packages->isEmpty()) {
            // 如果没有套餐配置，返回默认配置（兼容旧版本）
            $config = [
                'single_price' => config('site.course16_single_price') ?: 365,
                'package_size' => config('site.course16_package_size') ?: 10,
                'qydl_single_price' => config('site.course16_qydl_single_price') ?: 100,
                'sqdl_single_price' => config('site.course16_sqdl_single_price') ?: 130,
                'enable_package' => config('site.course16_enable_package') ?: 1,
            ];

            // 计算套餐价格
            $config['qydl_package_price'] = $config['qydl_single_price'] * $config['package_size'];
            $config['sqdl_package_price'] = $config['sqdl_single_price'] * $config['package_size'];
            $config['single_package_price'] = $config['single_price'] * $config['package_size'];

            $this->success('获取配置成功', $config);
            return;
        }

        // 基于数据库套餐的新配置
        $config = [
            'course_id' => 16,
            'total_packages' => count($packages),
            'enable_package' => 1,
        ];

        // 处理套餐数据
        $packageList = [];
        foreach ($packages as $package) {
            $packageData = [
                'id' => $package->id,
                'name' => $package->name,
                'description' => $package->description,
                'quantity' => $package->quantity,
                'original_price' => floatval($package->original_price),
                'qydl_price' => floatval($package->qydl_price),
                'sqdl_price' => floatval($package->sqdl_price),
                'ylgw_price' => floatval($package->ylgw_price),
                'user_types' => explode(',', $package->user_types),
                'sort' => $package->sort,
                'status' => $package->status
            ];

            $packageList[] = $packageData;
        }

        $config['packages'] = $packageList;

        $this->success('获取配置成功', $config);
    }

    /**
     * 获取用户对应的价格
     */
    public function price()
    {
        $package_id = $this->request->param('package_id');
        $package_type = $this->request->param('package_type', 0); // 兼容旧版本

        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $user = $this->auth->getUser();

        // 确定用户类型
        $userType = 'user';
        if ($user->is_qydl == 1) {
            $userType = 'qydl';
        } elseif ($user->is_sqdl == 1) {
            $userType = 'sqdl';
        } elseif ($user->is_ylgw == 1) {
            $userType = 'ylgw';
        }

        if ($package_id) {
            // 新版本：基于套餐ID获取价格
            $package = CoursePackage::where('id', $package_id)
                ->where('course_id', 16)
                ->where('status', 1)
                ->find();

            if (!$package) {
                $this->error('套餐不存在或已下架');
            }

            if (!$package->canUserBuy($userType)) {
                $this->error('您无权购买此套餐');
            }

            $price = $package->getPriceByUserType($userType);
            $result = [
                'package_id' => $package->id,
                'package_name' => $package->name,
                'price' => $price,
                'quantity' => $package->quantity,
                'total_price' => $price, // 套餐价格固定，不按数量计算
                'user_type' => $this->getUserTypeName($userType),
                'original_price' => floatval($package->original_price),
                'discount' => floatval($package->original_price) - $price,
                'quota_count' => $package->quantity, // 明确表示这是名额数量
            ];
        } else {
            // 旧版本：基于套餐类型获取价格（兼容）
            $config = [
                'single_price' => config('site.course16_single_price') ?: 365,
                'package_size' => config('site.course16_package_size') ?: 10,
                'qydl_single_price' => config('site.course16_qydl_single_price') ?: 100,
                'sqdl_single_price' => config('site.course16_sqdl_single_price') ?: 130,
            ];

            $price = 0;
            $user_type_name = '';

            if ($package_type == 1) {
                // 套餐价格
                if ($user->is_qydl == 1) {
                    $price = $config['qydl_single_price'] * $config['package_size'];
                    $user_type_name = '城市负责人';
                } elseif ($user->is_sqdl == 1) {
                    $price = $config['sqdl_single_price'] * $config['package_size'];
                    $user_type_name = '养老院长';
                } else {
                    $price = $config['single_price'] * $config['package_size'];
                    $user_type_name = '普通用户';
                }
            } else {
                // 单个价格
                $price = $config['single_price'];
                $user_type_name = '单个购买';
            }

            $result = [
                'price' => $price,
                'user_type' => $user_type_name,
                'package_type' => $package_type,
                'package_size' => $package_type == 1 ? $config['package_size'] : 1,
            ];
        }

        $this->success('获取价格成功', $result);
    }

    /**
     * 获取用户类型名称
     */
    private function getUserTypeName($userType)
    {
        $typeNames = [
            'qydl' => '城市负责人',
            'sqdl' => '养老院长',
            'ylgw' => '养老顾问',
            'user' => '普通用户'
        ];

        return isset($typeNames[$userType]) ? $typeNames[$userType] : '普通用户';
    }

    /**
     * 创建课程16订单
     */
    public function createOrder()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $package_id = $this->request->param('package_id');
        $package_type = $this->request->param('package_type', 0); // 兼容旧版本
        $platform = $this->request->param('platform', 'wxmin');

        $user = $this->auth->getUser();

        // 确定用户类型
        $userType = 'user';
        if ($user->is_qydl == 1) {
            $userType = 'qydl';
        } elseif ($user->is_sqdl == 1) {
            $userType = 'sqdl';
        } elseif ($user->is_ylgw == 1) {
            $userType = 'ylgw';
        }

        if ($package_id) {
            // 新版本：基于套餐ID创建订单
            $package = CoursePackage::where('id', $package_id)
                ->where('course_id', 16)
                ->where('status', 1)
                ->find();

            if (!$package) {
                $this->error('套餐不存在或已下架');
            }

            if (!$package->canUserBuy($userType)) {
                $this->error('您无权购买此套餐');
            }

            $unit_price = $package->getPriceByUserType($userType);
            $quantity = 1; // 套餐购买数量固定为1
            $total_price = $unit_price; // 套餐价格固定，不按数量计算
            $package_name = $package->name;
            $quota_count = $package->quantity; // 套餐包含的名额数量

            $order_data = [
                'platform' => $platform,
                'user_id' => $user->id,
                'order_no' => "C16" . date("YmdHis") . mt_rand(10, 9999),
                'course_id' => 16,
                'package_id' => $package_id,
                'package_type' => $package->quantity > 1 ? 1 : 0, // 根据名额数量判断是否为套餐
                'package_quantity' => $package->quantity, // 存储名额数量
                'total_price' => $total_price,
                'pay_price' => $total_price,
                'favourable_price' => 0,
                'ip' => request()->ip(),
                'pay_status' => 1, // 待支付
                'is_service' => 0,
            ];
        } 

        Db::startTrans();
        try {
            $order = CourseOrder::create($order_data);

            Db::commit();
            $this->success('订单创建成功', [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'package_id' => $package_id,
                'package_name' => $package_name,
                'total_price' => $total_price,
                'package_type' => isset($order_data['package_type']) ? $order_data['package_type'] : ($quantity > 1 ? 1 : 0),
                'quantity' => $quantity,
                'unit_price' => $unit_price,
                'user_type' => $this->getUserTypeName($userType),
            ]);

        } catch (Exception $e) {
            Db::rollback();
            $this->error('订单创建失败：' . $e->getMessage().'-'.$e->getLine());
        }
    }

    /**
     * 支付订单
     */
    public function pay()
    {
        if (!$this->auth->isLogin()) {
            $this->error('请先登录');
        }

        $order_id = $this->request->param('order_id');
        $pay_type = $this->request->param('pay_type', 2); // 默认余额支付

        if (!$order_id) {
            $this->error('订单ID不能为空');
        }

        $order = CourseOrder::where('id', $order_id)
            ->where('user_id', $this->auth->id)
            ->where('pay_status', 1)
            ->find();

        if (!$order) {
            $this->error('订单不存在或已支付');
        }

        if ($pay_type == 2) {
            // 余额支付
            if ($this->auth->money < $order->pay_price) {
                $this->error('余额不足，当前余额：' . $this->auth->money . '元，需要：' . $order->pay_price . '元');
            }

            // 获取套餐信息
            $package = null;
            if (isset($order->package_id) && $order->package_id > 0) {
                $package = CoursePackage::where('id', $order->package_id)->find();
            }

            Db::startTrans();
            try {
                // 扣除余额
                $memo = $package ? '购买课程16套餐-' . $package->name : '购买养老顾问身份套餐';
                User::money(-$order->pay_price, $this->auth->id, $memo, 1, $order->order_no, 'pay');

                // 更新订单状态
                $order->pay_status = 2;
                $order->pay_type = 2;
                $order->paytime = time();
                $order->order_trade_no = 'C16' . date('YmdHis') . mt_rand(10, 9999);
                $order->save();

                // 创建用户课程关联（只创建一个，因为是购买一个套餐）
                UserCourse::create([
                    'user_id' => $order->user_id,
                    'course_id' => 16,
                    'from_type' => 1, // 购买获得
                ]);

                // 获取用户信息
                $user = User::where('id', $order->user_id)->find();

                // 只有普通用户购买课程16才开通养老顾问身份
                // 城市负责人和养老院长购买套餐是为了获得开通额度，不改变自己的身份
                if ($user && $user->is_ylgw == 0 && $user->is_qydl == 0 && $user->is_sqdl == 0) {
                    $user->is_ylgw = 1;
                    $user->save();
                }

                // 增加开通额度（package_quantity表示名额数量）
                // 所有用户购买套餐都会获得开通额度
                $user->ylgw_quota = $user->ylgw_quota + $order->package_quantity;
                $user->save();

                // 执行分佣逻辑
                $this->executeCommission($order, $package);

                Db::commit();
                $this->success('支付成功', [
                    'order_id' => $order->id,
                    'order_no' => $order->order_no,
                    'package_name' => $package ? $package->name : '课程16',
                    'quantity' => $order->package_quantity,
                    'total_price' => $order->pay_price,
                ]);

            } catch (Exception $e) {
                Db::rollback();
                $this->error('支付失败：' . $e->getMessage());
            }
        } else {
            $this->error('暂不支持该支付方式');
        }
    }

    /**
     * 执行课程16的新分佣逻辑（仅使用新逻辑，避免双重分佣）
     */
    private function executeCommission($order, $package = null)
    {
        // 记录分佣开始日志
        \think\Log::info("课程16分佣开始 - 订单ID: {$order->id}, 用户ID: {$order->user_id}, 金额: {$order->total_price}");

        $user = User::where('id', $order->user_id)->field('id,parent_id,is_sqdl,is_qydl')->find();
        $package_name = $package ? $package->name : '课程16';

        // 基础分佣金额（固定金额，不再使用配置比例）
        $ylgw_base = 91.25;  // 养老顾问基础分佣 (25%)
        $sqdl_base = 182.5;  // 养老院长基础分佣 (50%)
        $qydl_base = 36.5;   // 城市负责人基础分佣 (10%)

        \think\Log::info("新分佣逻辑 - 基础金额: 养老顾问={$ylgw_base}, 养老院长={$sqdl_base}, 城市负责人={$qydl_base}");

        // 使用统一的分佣逻辑
        $this->processCourse16Commission($user, $package_name, $order);

        \think\Log::info("课程16分佣完成 - 订单ID: {$order->id}");
    }

    /**
     * 统一处理课程16分佣逻辑（供控制器和钩子共同使用）
     */
    public static function processCourse16Commission($user, $package_name, $order)
    {
        // 检查上级ID是否为0
        if (!$user['parent_id'] || $user['parent_id'] == 0) {
            \think\Log::info("课程16分佣-用户无上级，结束分佣");
            return;
        }

        // 获取上级用户信息
        $parent = \app\common\model\User::where('id', $user['parent_id'])
            ->field('id,nickname,is_ylgw,is_sqdl,is_qydl,parent_id,ylgw_total_commission')
            ->find();

        if (!$parent) {
            \think\Log::info("课程16分佣-上级用户不存在，结束分佣");
            return;
        }

        \think\Log::info("课程16分佣-开始检查 - 购买用户: {$user['id']}, 上级: {$parent['id']}, 上级身份: 养老顾问={$parent['is_ylgw']}, 院长={$parent['is_sqdl']}, 城市负责人={$parent['is_qydl']}");

        // 获取订单金额
        $order_amount = isset($order->total_price) ? $order->total_price : $order->pay_price;

        // 按比例计算分佣金额
        $ylgw_rate = 25;  // 养老顾问基础分佣比例 25%
        $sqdl_rate = 50;  // 养老院长基础分佣比例 50%
        $qydl_rate = 10;  // 城市负责人基础分佣比例 10%
        $qydl_direct_rate = 60; // 城市负责人直接推荐分佣比例 60%

        $ylgw_base = truncateDecimal($order_amount * ($ylgw_rate / 100));
        $sqdl_base = truncateDecimal($order_amount * ($sqdl_rate / 100));
        $qydl_base = truncateDecimal($order_amount * ($qydl_rate / 100));
        $qydl_direct = truncateDecimal($order_amount * ($qydl_direct_rate / 100));

        \think\Log::info("课程16分佣-计算金额 - 订单金额: {$order_amount}, 养老顾问: {$ylgw_base}, 养老院长: {$sqdl_base}, 城市负责人: {$qydl_base}, 城市负责人直接: {$qydl_direct}");

        if ($parent['is_ylgw'] == 1) {
            // 上级是养老顾问
            self::handleYlgwParent($parent, $ylgw_base, $sqdl_base, $qydl_base, $qydl_direct, $package_name, $order);
        } elseif ($parent['is_sqdl'] == 1) {
            // 上级是养老院长 - 情况4
            self::handleSqdlParent($parent, $sqdl_base, $qydl_base, $package_name, $order);
        } elseif ($parent['is_qydl'] == 1) {
            // 上级是城市负责人（直接推荐的情况）
            self::handleQydlParent($parent, $qydl_direct, $package_name, $order);
        } else {
            // 情况6：普通用户的下级成为养老顾问，不分佣
            \think\Log::info("课程16分佣-上级是普通用户，不分佣");
        }
    }

    /**
     * 处理上级是养老顾问的情况（统一逻辑）
     */
    private static function handleYlgwParent($ylgw_user, $ylgw_base, $sqdl_base, $qydl_base, $qydl_direct, $package_name, $order)
    {
        // 检查养老顾问的上级
        if (!$ylgw_user['parent_id'] || $ylgw_user['parent_id'] == 0) {
            // 情况5：养老顾问无上级，直接分给养老顾问到money，不记录ylgw_total_commission
            \app\common\model\User::money($ylgw_base, $ylgw_user['id'], '养老顾问分佣-' . $package_name, 2, $order->order_no, 'fenyong');
            \think\Log::info("课程16分佣-情况5：养老顾问无上级，直接分佣到money - 用户ID: {$ylgw_user['id']}, 金额: {$ylgw_base}");
            return;
        }

        // 有上级的养老顾问才记录ylgw_total_commission
        \app\common\model\User::where('id', $ylgw_user['id'])->setInc('ylgw_total_commission', $ylgw_base);
        \think\Log::info("课程16分佣-有上级的养老顾问获得ylgw_total_commission - 用户ID: {$ylgw_user['id']}, 金额: {$ylgw_base}");

        $ylgw_parent = \app\common\model\User::where('id', $ylgw_user['parent_id'])
            ->field('id,nickname,is_sqdl,is_qydl,parent_id')
            ->find();

        if (!$ylgw_parent) {
            \think\Log::info("课程16分佣-养老顾问的上级不存在，结束分佣");
            return;
        }

        if ($ylgw_parent['is_sqdl'] == 1) {
            // 情况3：养老院长 -> 养老顾问 -> 用户（用户成为养老顾问）
            \app\common\model\User::money($sqdl_base, $ylgw_parent['id'], '养老院长分佣-' . $package_name, 2, $order->order_no, 'fenyong');
            \think\Log::info("课程16分佣-情况3：养老院长分佣 - 用户ID: {$ylgw_parent['id']}, 金额: {$sqdl_base}");

            // 检查养老院长的上级是否是城市负责人
            if ($ylgw_parent['parent_id'] && $ylgw_parent['parent_id'] > 0) {
                $sqdl_parent = \app\common\model\User::where('id', $ylgw_parent['parent_id'])
                    ->field('id,is_qydl')
                    ->find();
                if ($sqdl_parent && $sqdl_parent['is_qydl'] == 1) {
                    // 情况2：城市负责人 -> 养老院长 -> 养老顾问 -> 用户
                    \app\common\model\User::money($qydl_base, $sqdl_parent['id'], '城市负责人分佣-' . $package_name, 2, $order->order_no, 'fenyong');
                    \think\Log::info("课程16分佣-情况2：城市负责人分佣 - 用户ID: {$sqdl_parent['id']}, 金额: {$qydl_base}");
                }
            }
        } elseif ($ylgw_parent['is_qydl'] == 1) {
            // 情况1：城市负责人 -> 养老顾问 -> 用户（用户成为养老顾问）
            \app\common\model\User::money($qydl_direct, $ylgw_parent['id'], '城市负责人分佣-' . $package_name, 2, $order->order_no, 'fenyong');
            \think\Log::info("课程16分佣-情况1：城市负责人分佣 - 用户ID: {$ylgw_parent['id']}, 金额: {$qydl_direct}");
        }
    }

    /**
     * 处理上级是养老院长的情况（统一逻辑）
     */
    private static function handleSqdlParent($sqdl_user, $sqdl_base, $qydl_base, $package_name, $order)
    {
        // 情况4：养老院长 -> 用户（用户成为养老顾问）
        \app\common\model\User::money($sqdl_base, $sqdl_user['id'], '养老院长分佣-' . $package_name, 2, $order->order_no, 'fenyong');
        \think\Log::info("课程16分佣-情况4：养老院长分佣 - 用户ID: {$sqdl_user['id']}, 金额: {$sqdl_base}");

        // 检查养老院长的上级是否是城市负责人
        if ($sqdl_user['parent_id'] && $sqdl_user['parent_id'] > 0) {
            $sqdl_parent = \app\common\model\User::where('id', $sqdl_user['parent_id'])
                ->field('id,is_qydl')
                ->find();
            if ($sqdl_parent && $sqdl_parent['is_qydl'] == 1) {
                \app\common\model\User::money($qydl_base, $sqdl_parent['id'], '城市负责人分佣-' . $package_name, 2, $order->order_no, 'fenyong');
                \think\Log::info("课程16分佣-养老院长上级城市负责人分佣 - 用户ID: {$sqdl_parent['id']}, 金额: {$qydl_base}");
            }
        }
    }

    /**
     * 处理上级是城市负责人的情况（统一逻辑）
     */
    private static function handleQydlParent($qydl_user, $qydl_direct_commission, $package_name, $order)
    {
        // 城市负责人直接推荐用户成为养老顾问，获得60%分佣
        \app\common\model\User::money($qydl_direct_commission, $qydl_user['id'], '城市负责人分佣-' . $package_name, 2, $order->order_no, 'fenyong');
        \think\Log::info("课程16分佣-城市负责人直接推荐分佣 - 用户ID: {$qydl_user['id']}, 金额: {$qydl_direct_commission}");
    }
}

/**
 * 截取小数位数
 */
function truncateDecimal($number, $decimals = 2) {
    $factor = pow(10, $decimals);
    return floor($number * $factor) / $factor;
}
